using UnityEngine;
using System.Collections;
using System.Runtime.InteropServices;

public class RegionResolver : MonoBehaviour
{
    public static RegionResolver Instance;

    // Cached WaitForSeconds instances for performance
    private static readonly WaitForSeconds WaitOneSecond = new WaitForSeconds(1f);
    private static readonly WaitForSeconds WaitTwoSeconds = new WaitForSeconds(2f);
    private static readonly WaitForSeconds WaitThreeSeconds = new WaitForSeconds(3f);

    private void Awake()
    {
        if (Instance == null) Instance = this;
    }

    /// <summary>
    /// Gets region: SIM country → GPS lat/lon → fallback "lat:0,lon:0"
    /// </summary>
    public void ResolveRegion(System.Action<string> onRegionResolved)
    {
        string simRegion = GetSimRegion();
        if (!string.IsNullOrEmpty(simRegion))
        {
            onRegionResolved?.Invoke(simRegion.ToUpper());
            return;
        }

        // If no SIM, try GPS (async coroutine)
        StartCoroutine(GetLatLon(onRegionResolved));
    }

    // ---------------- SIM ----------------
    private static string GetSimRegion()
    {
#if UNITY_ANDROID && !UNITY_EDITOR
        using (var unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer"))
        using (var activity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity"))
        using (var telephonyManager = activity.Call<AndroidJavaObject>("getSystemService", "phone"))
        {
            return telephonyManager.Call<string>("getSimCountryIso"); // e.g. "pk"
        }
#elif UNITY_IOS && !UNITY_EDITOR
        return _GetCarrierCountryCode(); // native iOS plugin needed
#else
        return ""; // Editor or no SIM
#endif
    }

#if UNITY_IOS && !UNITY_EDITOR
    [DllImport("__Internal")]
    private static extern string _GetCarrierCountryCode();
#endif

    // ---------------- GPS ----------------
    private IEnumerator GetLatLon(System.Action<string> callback)
    {
        // Check if location is enabled by user
        if (!Input.location.isEnabledByUser)
        {
            Debug.Log("Location service not enabled by user.");
            yield return StartCoroutine(HandleLocationNotEnabled(callback));
            yield break;
        }

        yield return StartCoroutine(AttemptLocationAccess(callback));
    }

    private IEnumerator HandleLocationNotEnabled(System.Action<string> callback)
    {
        // Ask user to enable location
        bool userWantsToEnable = false;
        yield return StartCoroutine(ShowLocationPermissionDialog((result) => userWantsToEnable = result));

        if (userWantsToEnable)
        {
            // User agreed to enable location, try again after a brief delay
            Debug.Log("User agreed to enable location. Please enable location in device settings and try again.");
            yield return WaitTwoSeconds; // Give user time to enable location

            // Check again if location is now enabled
            if (Input.location.isEnabledByUser)
            {
                yield return StartCoroutine(AttemptLocationAccess(callback));
            }
            else
            {
                Debug.Log("Location still not enabled after user prompt.");
                callback?.Invoke("ACCESS_DENIED");
            }
        }
        else
        {
            // User declined to enable location
            Debug.Log("User declined to enable location.");
            callback?.Invoke("ACCESS_DENIED");
        }
    }

    private IEnumerator AttemptLocationAccess(System.Action<string> callback)
    {
        Input.location.Start();

        int maxWait = 20;
        while (Input.location.status == LocationServiceStatus.Initializing && maxWait > 0)
        {
            yield return WaitOneSecond;
            maxWait--;
        }

        if (Input.location.status != LocationServiceStatus.Running)
        {
            Debug.Log("Location service failed to start.");
            callback?.Invoke("ACCESS_DENIED");
            yield break;
        }

        double lat = Input.location.lastData.latitude;
        double lon = Input.location.lastData.longitude;

        Debug.Log($"Lat: {lat}, Lon: {lon}");
        callback?.Invoke($"LAT:{lat},LON:{lon}");
    }

    private IEnumerator ShowLocationPermissionDialog(System.Action<bool> callback)
    {
        // For now, we'll use a simple approach that opens device settings
        // In a real implementation, you might want to show a custom UI dialog

#if UNITY_ANDROID && !UNITY_EDITOR
        yield return StartCoroutine(ShowAndroidLocationDialog(callback));
#elif UNITY_IOS && !UNITY_EDITOR
        yield return StartCoroutine(ShowIOSLocationDialog(callback));
#else
        // In editor or other platforms, simulate user agreeing
        Debug.Log("Simulating location permission dialog - user agrees");
        callback?.Invoke(true);
        yield break;
#endif
    }

#if UNITY_ANDROID && !UNITY_EDITOR
    private IEnumerator ShowAndroidLocationDialog(System.Action<bool> callback)
    {
        bool success = false;
        try
        {
            using (var unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer"))
            using (var activity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity"))
            using (var intent = new AndroidJavaObject("android.content.Intent", "android.settings.LOCATION_SOURCE_SETTINGS"))
            {
                Debug.Log("Opening Android location settings...");
                activity.Call("startActivity", intent);
                success = true;
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to open Android location settings: {e.Message}");
            success = false;
        }

        // Wait outside the try-catch block
        if (success)
        {
            yield return WaitThreeSeconds;
            callback?.Invoke(true); // Assume user will enable it
        }
        else
        {
            callback?.Invoke(false);
        }
    }
#endif

#if UNITY_IOS && !UNITY_EDITOR
    private IEnumerator ShowIOSLocationDialog(System.Action<bool> callback)
    {
        // On iOS, we can't directly open location settings, but we can show an alert
        // This would require a native iOS plugin to show proper dialogs
        Debug.Log("Please enable location services in iOS Settings > Privacy & Security > Location Services");
        yield return WaitTwoSeconds;
        callback?.Invoke(true); // Assume user will enable it
    }
#endif
}
