{"buildFiles": ["/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/CMakeLists.txt", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/CMakeLists.txt"], "cleanCommandsComponents": [["/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/LocationService/.utmp/RelWithDebInfo/5v35454h/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/LocationService/.utmp/RelWithDebInfo/5v35454h/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"game::@d02bb112ea9f9c2ed29f": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "game", "output": "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/5v35454h/obj/arm64-v8a/libgame.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}