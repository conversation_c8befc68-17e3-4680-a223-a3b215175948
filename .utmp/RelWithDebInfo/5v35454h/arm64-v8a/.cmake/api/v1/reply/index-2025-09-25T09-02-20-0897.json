{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/cmake", "cpack": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/cpack", "ctest": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ctest", "root": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": false, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-74f3b682fea69982c03e.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-9fb52df1922d1c5efc9a.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-7d958f01e1c605f326b1.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-9fb52df1922d1c5efc9a.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-7d958f01e1c605f326b1.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-74f3b682fea69982c03e.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}