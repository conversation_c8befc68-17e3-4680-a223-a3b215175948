# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: Unity
# Configurations: RelWithDebInfo
# =============================================================================
# =============================================================================

#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__game_RelWithDebInfo
  depfile = $DEP_FILE
  deps = gcc
  command = /Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android23 --sysroot=/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX shared library.

rule CXX_SHARED_LIBRARY_LINKER__game_RelWithDebInfo
  command = $PRE_LINK && /Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android23 --sysroot=/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -fPIC $LANGUAGE_COMPILE_FLAGS $ARCH_FLAGS $LINK_FLAGS -shared $SONAME_FLAG$SONAME -o $TARGET_FILE $in $LINK_PATH $LINK_LIBRARIES && $POST_BUILD
  description = Linking CXX shared library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = /Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp -B/Users/<USER>/Desktop/LocationService/.utmp/RelWithDebInfo/5v35454h/arm64-v8a
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = /Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = /Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja -t targets
  description = All primary targets available:

