{"BeeBuildProgramCommon.Data.ConfigurationData": {"Il2CppDir": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp", "UnityLinkerPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/build/deploy/UnityLinker", "Il2CppPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/build/deploy/il2cpp", "NetCoreRunPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/netcorerun/netcorerun", "DotNetExe": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/NetCoreRuntime/dotnet", "EditorContentsPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents", "Packages": [{"Name": "com.unity.ai.navigation", "ResolvedPath": "Library/PackageCache/com.unity.ai.navigation@eb5635ad590d"}, {"Name": "com.unity.collab-proxy", "ResolvedPath": "Library/PackageCache/com.unity.collab-proxy@ab839cc7d2ad"}, {"Name": "com.unity.ide.rider", "ResolvedPath": "Library/PackageCache/com.unity.ide.rider@4d374c7eb6db"}, {"Name": "com.unity.ide.visualstudio", "ResolvedPath": "Library/PackageCache/com.unity.ide.visualstudio@198cdf337d13"}, {"Name": "com.unity.inputsystem", "ResolvedPath": "Library/PackageCache/com.unity.inputsystem@be6c4fd0abf5"}, {"Name": "com.unity.multiplayer.center", "ResolvedPath": "Library/PackageCache/com.unity.multiplayer.center@f3fb577b3546"}, {"Name": "com.unity.render-pipelines.universal", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4"}, {"Name": "com.unity.test-framework", "ResolvedPath": "Library/PackageCache/com.unity.test-framework@a6f5be5f149c"}, {"Name": "com.unity.timeline", "ResolvedPath": "Library/PackageCache/com.unity.timeline@6b9e48457ddb"}, {"Name": "com.unity.ugui", "ResolvedPath": "Library/PackageCache/com.unity.ugui@754dbde2be27"}, {"Name": "com.unity.visualscripting", "ResolvedPath": "Library/PackageCache/com.unity.visualscripting@6279e2b7c485"}, {"Name": "com.unity.modules.accessibility", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.accessibility"}, {"Name": "com.unity.modules.ai", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.ai"}, {"Name": "com.unity.modules.androidjni", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.androidjni"}, {"Name": "com.unity.modules.animation", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.animation"}, {"Name": "com.unity.modules.assetbundle", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.assetbundle"}, {"Name": "com.unity.modules.audio", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.audio"}, {"Name": "com.unity.modules.cloth", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.cloth"}, {"Name": "com.unity.modules.director", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.director"}, {"Name": "com.unity.modules.imageconversion", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.imageconversion"}, {"Name": "com.unity.modules.imgui", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.imgui"}, {"Name": "com.unity.modules.jsonserialize", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.jsonserialize"}, {"Name": "com.unity.modules.particlesystem", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.particlesystem"}, {"Name": "com.unity.modules.physics", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics"}, {"Name": "com.unity.modules.physics2d", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.physics2d"}, {"Name": "com.unity.modules.screencapture", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.screencapture"}, {"Name": "com.unity.modules.terrain", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.terrain"}, {"Name": "com.unity.modules.terrainphysics", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.terrainphysics"}, {"Name": "com.unity.modules.tilemap", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.tilemap"}, {"Name": "com.unity.modules.ui", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.ui"}, {"Name": "com.unity.modules.uielements", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.uielements"}, {"Name": "com.unity.modules.umbra", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.umbra"}, {"Name": "com.unity.modules.unityanalytics", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unityanalytics"}, {"Name": "com.unity.modules.unitywebrequest", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequest"}, {"Name": "com.unity.modules.unitywebrequestassetbundle", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestassetbundle"}, {"Name": "com.unity.modules.unitywebrequestaudio", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestaudio"}, {"Name": "com.unity.modules.unitywebrequesttexture", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequesttexture"}, {"Name": "com.unity.modules.unitywebrequestwww", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.unitywebrequestwww"}, {"Name": "com.unity.modules.vehicles", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.vehicles"}, {"Name": "com.unity.modules.video", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.video"}, {"Name": "com.unity.modules.vr", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.vr"}, {"Name": "com.unity.modules.wind", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.wind"}, {"Name": "com.unity.modules.xr", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.xr"}, {"Name": "com.unity.modules.subsystems", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.subsystems"}, {"Name": "com.unity.modules.hierarchycore", "ResolvedPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Resources/PackageManager/BuiltInPackages/com.unity.modules.hierarchycore"}, {"Name": "com.unity.ext.nunit", "ResolvedPath": "Library/PackageCache/com.unity.ext.nunit@031a54704bff"}, {"Name": "com.unity.render-pipelines.core", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.core@5e056b397fc0"}, {"Name": "com.unity.shadergraph", "ResolvedPath": "Library/PackageCache/com.unity.shadergraph@287dd3fa176c"}, {"Name": "com.unity.render-pipelines.universal-config", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.universal-config@8dc1aab4af1d"}, {"Name": "com.unity.searcher", "ResolvedPath": "Library/PackageCache/com.unity.searcher@1e17ce91558d"}, {"Name": "com.unity.burst", "ResolvedPath": "Library/PackageCache/com.unity.burst@f7a407abf4d5"}, {"Name": "com.unity.mathematics", "ResolvedPath": "Library/PackageCache/com.unity.mathematics@8017b507cc74"}, {"Name": "com.unity.collections", "ResolvedPath": "Library/PackageCache/com.unity.collections@d49facba0036"}, {"Name": "com.unity.rendering.light-transport", "ResolvedPath": "Library/PackageCache/com.unity.rendering.light-transport@2c9279f90d7c"}, {"Name": "com.unity.nuget.mono-cecil", "ResolvedPath": "Library/PackageCache/com.unity.nuget.mono-cecil@d78732e851eb"}, {"Name": "com.unity.test-framework.performance", "ResolvedPath": "Library/PackageCache/com.unity.test-framework.performance@92d1d09a72ed"}], "UnityVersion": "6000.2.2f1", "UnityVersionNumeric": {"Release": 6000, "Major": 2, "Minor": 2}, "Batchmode": false, "EmitDataForBeeWhy": false, "NamedPipeOrUnixSocket": "/tmp/ilpp.sock-f250519532fc0bb8e7b3b2a071a764a2"}, "PlayerBuildProgramLibrary.Data.PlayerBuildConfig": {"DestinationPath": "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle", "StagingArea": "Temp/StagingArea", "DataFolder": "Library/PlayerDataCache/Android/Data", "CompanyName": "DefaultCompany", "ProductName": "LocationService", "PlayerPackage": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer", "ApplicationIdentifier": "com.location.pryze", "Architecture": "", "ScriptingBackend": "IL2CPP", "NoGUID": false, "InstallIntoBuildsFolder": false, "GenerateIdeProject": false, "Development": false, "UseNewInputSystem": true, "GenerateNativePluginsForAssembliesSettings": {"HasCallback": true, "DisplayName": "<PERSON> <PERSON><PERSON><PERSON>", "AdditionalInputFiles": ["ProjectSettings/BurstAotSettings_Android.json", "Library/BurstCache/AotSettings_Android.hash", "/Users/<USER>/Desktop/LocationService/Library/PackageCache/com.unity.burst@f7a407abf4d5/.Runtime/bcl.exe"]}, "Services": {"EnableUnityConnect": true, "EnablePerformanceReporting": false, "EnableAnalytics": false, "EnableCrashReporting": true, "EnableInsights": true}, "ManagedAssemblies": ["/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AIModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ARModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AccessibilityModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AndroidJNIModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AnimationModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AssetBundleModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AudioModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ClothModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ContentLoadModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CoreModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CrashReportingModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DSPGraphModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DirectorModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GIModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GameCenterModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GridModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HierarchyCoreModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HotReloadModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.IMGUIModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.IdentifiersModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ImageConversionModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputForUIModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputLegacyModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InsightsModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.JSONSerializeModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.LocalizationModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.MarshallingModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.MultiplayerModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ParticleSystemModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PerformanceReportingModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.Physics2DModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PropertiesModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ScreenCaptureModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SharedInternalsModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteMaskModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteShapeModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.StreamingModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubstanceModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubsystemsModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TLSModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainPhysicsModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreFontEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreTextEngineModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextRenderingModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TilemapModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIElementsModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UmbraModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsCommonModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityConnectModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityConsentModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityCurlModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityTestProtocolModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAudioModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestTextureModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestWWWModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VFXModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VRModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VehiclesModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VideoModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.WindModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.XRModule.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.AI.Navigation.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll", "/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll", "/Users/<USER>/Desktop/LocationService/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll", "/Users/<USER>/Desktop/LocationService/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll", "/Users/<USER>/Desktop/LocationService/Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll", "/Users/<USER>/Desktop/LocationService/Library/PackageCache/com.unity.burst@f7a407abf4d5/Unity.Burst.Unsafe.dll", "/Users/<USER>/Desktop/LocationService/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Accessibility.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Registry.AccessControl.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Registry.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.AppContext.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Buffers.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.Concurrent.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.NonGeneric.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.Specialized.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.Annotations.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.EventBasedAsync.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.TypeConverter.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Console.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Data.Common.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Data.SqlClient.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Contracts.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Debug.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.FileVersionInfo.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Process.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.StackTrace.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TextWriterTraceListener.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Tools.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TraceEvent.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TraceSource.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Tracing.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Drawing.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Dynamic.Runtime.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.Calendars.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.Compression.ZipFile.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.AccessControl.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.DriveInfo.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.Watcher.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.IsolatedStorage.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.MemoryMappedFiles.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.Pipes.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.UnmanagedMemoryStream.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Expressions.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Parallel.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Queryable.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Memory.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.AuthenticationManager.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Cache.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.HttpListener.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Mail.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.NameResolution.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.NetworkInformation.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Ping.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Requests.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Security.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.ServicePoint.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Sockets.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Utilities.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebHeaderCollection.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebSockets.Client.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebSockets.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ObjectModel.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.DispatchProxy.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.ILGeneration.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.Lightweight.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.TypeExtensions.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.Reader.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.ReaderWriter.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.ResourceManager.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.Writer.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.CompilerServices.VisualC.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Handles.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.RuntimeInformation.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.WindowsRuntime.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Loader.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Numerics.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Formatters.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Json.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Xml.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.AccessControl.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Claims.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Algorithms.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Cng.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Csp.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.DeriveBytes.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encoding.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.Aes.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.ECDiffieHellman.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.ECDsa.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Hashing.Algorithms.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Hashing.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.OpenSsl.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Pkcs.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.ProtectedData.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.RSA.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.RandomNumberGenerator.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.X509Certificates.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Principal.Windows.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Principal.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.SecureString.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Duplex.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Http.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.NetTcp.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Security.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceProcess.ServiceController.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.CodePages.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.RegularExpressions.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.AccessControl.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Overlapped.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.Parallel.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Thread.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.ThreadPool.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Timer.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ValueTuple.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.ReaderWriter.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XDocument.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.XDocument.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.XmlDocument.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XmlDocument.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XmlSerializer.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.Xsl.Primitives.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/netstandard.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.CJK.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.MidEast.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.Other.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.Rare.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.West.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Microsoft.CSharp.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Data.Sqlite.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Data.Tds.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Security.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ComponentModel.Composition.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ComponentModel.DataAnnotations.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Configuration.Install.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Configuration.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Core.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.DataSetExtensions.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Entity.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Linq.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.OracleClient.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Services.Client.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Services.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Design.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.DirectoryServices.Protocols.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.DirectoryServices.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Drawing.Design.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Drawing.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.EnterpriseServices.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.IO.Compression.FileSystem.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.IO.Compression.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.IdentityModel.Selectors.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.IdentityModel.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Json.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Management.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Messaging.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.Http.WebRequest.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.Http.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Numerics.Vectors.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Numerics.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Reflection.Context.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Caching.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.DurableInstancing.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Remoting.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Serialization.Formatters.Soap.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Serialization.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Security.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Activation.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Discovery.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Internals.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Routing.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Web.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceProcess.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Transactions.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.ApplicationServices.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.DynamicData.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Extensions.Design.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Extensions.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.RegularExpressions.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Services.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.Forms.DataVisualization.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.Forms.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xaml.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.Linq.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.Serialization.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.dll", "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/mscorlib.dll"], "StreamingAssetsFiles": []}, "PlayerBuildProgramLibrary.Data.PluginsData": {"Plugins": []}, "PlayerBuildProgramLibrary.Data.LinkerConfig": {"LinkXmlFiles": ["/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/UnityLinkerInputs/MethodsToPreserve.xml", "/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/UnityLinkerInputs/TypesInScenes.xml", "/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/UnityLinkerInputs/SerializedTypes.xml", "/Users/<USER>/Desktop/LocationService/Library/InputSystem/AndroidLink.xml"], "AssembliesToProcess": ["Assembly-CSharp.dll", "Unity.InputSystem.dll", "Unity.RenderPipelines.Core.Runtime.dll", "Unity.RenderPipelines.Universal.Runtime.dll", "Unity.TextMeshPro.dll", "UnityEngine.UI.dll"], "EditorToLinkerData": "/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/UnityLinkerInputs/EditorToUnityLinkerData.json", "Runtime": "il2cpp", "Profile": "unityaot-linux", "Ruleset": "Minimal", "ModulesAssetPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/modules.asset", "AdditionalArgs": [], "AllowDebugging": false, "PerformEngineStripping": true}, "PlayerBuildProgramLibrary.Data.Il2CppConfig": {"EnableDeepProfilingSupport": false, "EnableFullGenericSharing": false, "Profile": "unityaot-linux", "IDEProjectDefines": ["ALL_INTERIOR_POINTERS=1", "GC_GCJ_SUPPORT=1", "JAVA_FINALIZATION=1", "NO_EXECUTE_PERMISSION=1", "GC_NO_THREADS_DISCOVERY=1", "IGNORE_DYNAMIC_LOADING=1", "GC_DONT_REGISTER_MAIN_STATIC_DATA=1", "GC_VERSION_MAJOR=7", "GC_VERSION_MINOR=7", "GC_VERSION_MICRO=0", "GC_THREADS=1", "USE_MMAP=1", "USE_MUNMAP=1", "NET_4_0=1", "UNITY_AOT=1", "NET_STANDARD_2_0=1", "NET_UNITY_4_8=1", "NET_STANDARD=1", "IL2CPP_ENABLE_WRITE_BARRIERS=1", "IL2CPP_INCREMENTAL_TIME_SLICE=3"], "ConfigurationName": "Release", "GcWBarrierValidation": false, "GcIncremental": true, "AdditionalCppFiles": [], "AdditionalArgs": ["--emit-source-mapping"], "AdditionalLibraries": [], "AdditionalDefines": [], "AdditionalIncludeDirectories": [], "AdditionalLinkDirectories": [], "CreateSymbolFiles": true, "AllowDebugging": false, "RelativeDataPath": "Data", "GenerateUsymFile": false, "UsymtoolPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/macosx/usymtool"}, "AndroidPlayerBuildProgram.Data.AndroidPlayerBuildConfiguration": {"GradleProjectCreateInfo": {"EnvironmentVariableInputs": ["UNITY_THISISABUILDMACHINE:"], "HostPlatform": "OSX", "ApplicationType": "APK", "BuildType": "Release", "AndroidSDKPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/SDK", "AndroidNDKPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK", "AndroidJavaPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/OpenJDK", "PreferredHeapSizeForJVM": 4096, "GradleVersion": "8.11", "ProjectFiles": {"UnityLibraryBuildGradle": {"RelativeDestinationPath": "unityLibrary/build.gradle", "CanBeModifiedByUser": true}, "LauncherBuildGradle": {"RelativeDestinationPath": "launcher/build.gradle", "CanBeModifiedByUser": true}, "LauncherSetupUnitySymbolsGradle": {"SourcePath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/launcher/setupSymbols.gradle", "RelativeDestinationPath": "launcher/setupSymbols.gradle", "CanBeModifiedByUser": false}, "SharedKeepUnitySymbolsGradle": {"SourcePath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/shared/keepUnitySymbols.gradle", "RelativeDestinationPath": "shared/keepUnitySymbols.gradle", "CanBeModifiedByUser": false}, "SharedCommonGradle": {"SourcePath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/shared/common.gradle", "RelativeDestinationPath": "shared/common.gradle", "CanBeModifiedByUser": false}, "ProjectLevelBuildGradle": {"RelativeDestinationPath": "build.gradle", "CanBeModifiedByUser": true}, "GradleProperties": {"RelativeDestinationPath": "gradle.properties", "CanBeModifiedByUser": true}, "UnityProguard": {"SourcePath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/UnityProGuardTemplate.txt", "RelativeDestinationPath": "unityLibrary/proguard-unity.txt", "CanBeModifiedByUser": true}, "ProguardUser": {"RelativeDestinationPath": "unityLibrary/proguard-user.txt", "CanBeModifiedByUser": true}, "GradleSettings": {"RelativeDestinationPath": "settings.gradle", "CanBeModifiedByUser": true}, "LocalProperties": {"RelativeDestinationPath": "local.properties", "CanBeModifiedByUser": true}}, "AdditionalUserInputs": [], "AdditionalUserOutputs": {"AdditionalManifests": [], "AdditionalBuildGradleFiles": [], "AdditionalGradleSettings": [], "AdditionalGradleProperties": [], "AdditionalFilesWithContents": []}, "UserCopyData": {"FilesToCopy": [], "DirectoriesToCopy": []}, "AdditionalUserData": [], "BuildTools": "34.0.0", "TargetSDKVersion": 36, "MinSDKVersion": 23, "PackageName": "com.location.pryze", "Architectures": "ARM64", "BuildApkPerCpuArchitecture": false, "DebugSymbols": {"Level": "None", "Format": "5"}, "VersionCode": 1, "VersionName": "0.1.0", "Minify": 1, "NoCompressOptions": {"RelativeFilePaths": [], "FileExtensions": []}, "UseCustomKeystore": false, "KeystorePath": "", "KeystoreName": "", "KeystorePassword": "", "KeystoreAliasName": "", "KeystoreAliasPassword": "", "ScriptingImplementation": "IL2CPP", "AndroidLibraries": [], "AARFiles": [], "BuiltinJavaSourcePaths": ["com/unity3d/player/UnityPlayerGameActivity.java"], "JavaSourcePaths": [], "KotlinSourcePaths": [], "PlayerPackage": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer", "PlayerPackageTools": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools", "SymlinkSources": false, "InstallIntoBuildsFolder": false, "UnityPath": "", "UnityProjectPath": "/Users/<USER>/Desktop/LocationService", "OverrideCMakeIntermdiateDirectory": true, "Dependencies": [], "ApplicationEntry": "GameActivity", "JarFiles": ["classes.jar"], "UseOptimizedFramePacing": false, "ReportGooglePlayAppDependencies": false, "UnityVersion": "6000.2.2f1"}, "Architectures": "ARM64", "BuildType": "Release", "LinkTimeOptimization": "None", "BuildSystem": "<PERSON><PERSON><PERSON>", "ScriptingImplementation": "IL2CPP", "DebugSymbol": {"Level": "None", "Format": "5"}, "ApplicationSplitMode": "Disabled", "TargetTextureCompression": false, "GradleResourcesInformation": {"TargetSDKVersion": 36, "RoundIconsAvailable": false, "RoundIconsSupported": true, "AdaptiveIconsSupported": true, "AdaptiveIconsAvailable": false}, "PreloadedJavaClasses": [], "PatchPackage": false, "ArchitectureExtensions": "None", "ApplicationEntry": "GameActivity", "BuildFingerPrintContents": "6000.2.2f1;IL2CPP;Release;StripEngineCode:1;OptimizedFramePacing:0;AppEntry:2;LTO:0", "UserSymbols": [], "ConfigurationManagerAssemblies": [], "PostGenerateGradleCallbackUsed": false, "TrackedFeatures": ["UnityEngine.Android.AndroidGame::SetGameState", "UnityEngine.Android.AndroidGame::get_GameMode", "UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn", "UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"], "ApplicationName": "LocationService", "StaticSplashScreenBackgroundColor": "231F20", "APIRequiringInternetPermission": ["UnityEngine.Networking", "System.Net.Sockets", "System.Net.WebRequest", "UnityEngine.Ping", "UnityEngine.Networking.UnityWebRequest"]}, "AndroidPlayerBuildProgram.Data.AndroidManifestConfiguration": {"TargetSDKVersion": 36, "LauncherManifestTemplatePath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Apk/LauncherManifest.xml", "LauncherManifestTemplateUsed": false, "LibraryManifestTemplatePath": "Assets/Plugins/Android/AndroidManifest.xml", "LibraryManifestCustomTemplateUsed": true, "LauncherManifestPath": "launcher/src/main/AndroidManifest.xml", "LibraryManifestPath": "unityLibrary/src/main/AndroidManifest.xml", "TVCompatibility": false, "AppCategory": "game", "BannerEnabled": true, "PreferredInstallLocation": "PreferExternal", "TextureSupport": "Generic", "GamepadSupportLevel": "SupportsDPad", "SupportedAspectRatioMode": 1, "MaxAspectRatio": 2.4, "MinAspectRatio": 1, "ForceInternetPermission": false, "UseLowAccuracyLocation": false, "ForceSDCardPermission": false, "PreserveFramebufferAlpha": false, "DefaultInterfaceOrientation": "AutoRotation", "AllowedAutorotateToPortrait": true, "AllowedAutorotateToPortraitUpsideDown": true, "AllowedAutorotateToLandscapeLeft": true, "AllowedAutorotateToLandscapeRight": true, "SplashScreenScale": "Center", "RenderOutsideSafeArea": true, "GraphicsDevices": ["Vulkan", "OpenGLES3"], "OpenGLRequireES31": false, "OpenGLRequireES31AEP": false, "OpenGLRequireES32": false, "StartInFullscreen": true, "DefaultWindowWidth": 1920, "DefaultWindowHeight": 1080, "MinimumWindowWidth": 400, "MinimumWindowHeight": 300, "ResizeableActivity": true, "FullScreenMode": "FullScreenWindow", "AutoRotationBehavior": "User", "StripEngineCode": true, "ApplicationEntry": "GameActivity", "JavaFileNames": ["UnityPlayerGameActivity.java"], "EnableOnBackInvokedCallback": true}, "AndroidPlayerBuildProgram.Data.AndroidSharedLibraryConfiguration": {"ClangPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++", "ABITools": [{"Architecture": "ARM64", "ObjCopyPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-objcopy", "StripPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-strip"}]}}