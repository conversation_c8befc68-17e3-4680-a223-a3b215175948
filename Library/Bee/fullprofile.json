{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 37977, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 37977, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 37977, "tid": 647, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 37977, "tid": 647, "ts": 1758795325771898, "dur": 414, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 37977, "tid": 647, "ts": 1758795325774891, "dur": 556, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 37977, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 37977, "tid": 1, "ts": 1758795324952469, "dur": 6070, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 37977, "tid": 1, "ts": 1758795324958542, "dur": 181798, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 37977, "tid": 1, "ts": 1758795325140348, "dur": 43608, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 37977, "tid": 647, "ts": 1758795325775452, "dur": 10, "ph": "X", "name": "", "args": {}}, {"pid": 37977, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324950514, "dur": 4426, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324954942, "dur": 809075, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324955742, "dur": 3469, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324959215, "dur": 948, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324960165, "dur": 9819, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324969989, "dur": 460, "ph": "X", "name": "ProcessMessages 8172", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970452, "dur": 38, "ph": "X", "name": "ReadAsync 8172", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970492, "dur": 6, "ph": "X", "name": "ProcessMessages 8162", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970498, "dur": 51, "ph": "X", "name": "ReadAsync 8162", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970553, "dur": 1, "ph": "X", "name": "ProcessMessages 1552", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970555, "dur": 47, "ph": "X", "name": "ReadAsync 1552", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970613, "dur": 2, "ph": "X", "name": "ProcessMessages 1454", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970617, "dur": 66, "ph": "X", "name": "ReadAsync 1454", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970685, "dur": 1, "ph": "X", "name": "ProcessMessages 1646", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970698, "dur": 26, "ph": "X", "name": "ReadAsync 1646", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970725, "dur": 1, "ph": "X", "name": "ProcessMessages 1532", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970727, "dur": 28, "ph": "X", "name": "ReadAsync 1532", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970757, "dur": 1, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970759, "dur": 19, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970781, "dur": 17, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970801, "dur": 19, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970823, "dur": 17, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970843, "dur": 17, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970863, "dur": 24, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970889, "dur": 17, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970908, "dur": 21, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970932, "dur": 18, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970953, "dur": 16, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324970972, "dur": 635, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324971609, "dur": 24, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324971642, "dur": 6, "ph": "X", "name": "ProcessMessages 8190", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324971649, "dur": 34, "ph": "X", "name": "ReadAsync 8190", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324971685, "dur": 1, "ph": "X", "name": "ProcessMessages 1426", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324971687, "dur": 5392, "ph": "X", "name": "ReadAsync 1426", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324977086, "dur": 7, "ph": "X", "name": "ProcessMessages 8189", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324977094, "dur": 128, "ph": "X", "name": "ReadAsync 8189", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324977224, "dur": 8, "ph": "X", "name": "ProcessMessages 8166", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795324977232, "dur": 92197, "ph": "X", "name": "ReadAsync 8166", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325123949, "dur": 11, "ph": "X", "name": "ProcessMessages 8189", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124042, "dur": 95, "ph": "X", "name": "ReadAsync 8189", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124139, "dur": 6, "ph": "X", "name": "ProcessMessages 8178", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124150, "dur": 430, "ph": "X", "name": "ReadAsync 8178", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124583, "dur": 5, "ph": "X", "name": "ProcessMessages 6017", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124588, "dur": 101, "ph": "X", "name": "ReadAsync 6017", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124703, "dur": 2, "ph": "X", "name": "ProcessMessages 2231", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124706, "dur": 102, "ph": "X", "name": "ReadAsync 2231", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124811, "dur": 2, "ph": "X", "name": "ProcessMessages 3548", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124818, "dur": 40, "ph": "X", "name": "ReadAsync 3548", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124860, "dur": 1, "ph": "X", "name": "ProcessMessages 1196", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124862, "dur": 24, "ph": "X", "name": "ReadAsync 1196", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124889, "dur": 13, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124913, "dur": 28, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124943, "dur": 1, "ph": "X", "name": "ProcessMessages 1194", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124945, "dur": 31, "ph": "X", "name": "ReadAsync 1194", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124977, "dur": 1, "ph": "X", "name": "ProcessMessages 1146", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325124981, "dur": 25, "ph": "X", "name": "ReadAsync 1146", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125007, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125009, "dur": 29, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125066, "dur": 1, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125068, "dur": 63, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125136, "dur": 3, "ph": "X", "name": "ProcessMessages 1833", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125142, "dur": 46, "ph": "X", "name": "ReadAsync 1833", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125190, "dur": 2, "ph": "X", "name": "ProcessMessages 1903", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125195, "dur": 40, "ph": "X", "name": "ReadAsync 1903", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125243, "dur": 240, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125489, "dur": 2, "ph": "X", "name": "ProcessMessages 1617", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125494, "dur": 42, "ph": "X", "name": "ReadAsync 1617", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125539, "dur": 2, "ph": "X", "name": "ProcessMessages 1141", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125554, "dur": 45, "ph": "X", "name": "ReadAsync 1141", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125601, "dur": 3, "ph": "X", "name": "ProcessMessages 1882", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125607, "dur": 32, "ph": "X", "name": "ReadAsync 1882", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125641, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125644, "dur": 39, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125685, "dur": 1, "ph": "X", "name": "ProcessMessages 1036", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125687, "dur": 90, "ph": "X", "name": "ReadAsync 1036", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125779, "dur": 4, "ph": "X", "name": "ProcessMessages 2273", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125785, "dur": 37, "ph": "X", "name": "ReadAsync 2273", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125841, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125844, "dur": 30, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125876, "dur": 3, "ph": "X", "name": "ProcessMessages 1625", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125880, "dur": 72, "ph": "X", "name": "ReadAsync 1625", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125956, "dur": 2, "ph": "X", "name": "ProcessMessages 1062", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325125959, "dur": 79, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126040, "dur": 3, "ph": "X", "name": "ProcessMessages 1965", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126047, "dur": 46, "ph": "X", "name": "ReadAsync 1965", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126096, "dur": 2, "ph": "X", "name": "ProcessMessages 1506", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126099, "dur": 46, "ph": "X", "name": "ReadAsync 1506", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126147, "dur": 1, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126150, "dur": 50, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126202, "dur": 1, "ph": "X", "name": "ProcessMessages 1347", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126245, "dur": 44, "ph": "X", "name": "ReadAsync 1347", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126291, "dur": 1, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126293, "dur": 53, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126350, "dur": 2, "ph": "X", "name": "ProcessMessages 1295", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126355, "dur": 59, "ph": "X", "name": "ReadAsync 1295", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126418, "dur": 2, "ph": "X", "name": "ProcessMessages 1483", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126420, "dur": 43, "ph": "X", "name": "ReadAsync 1483", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126467, "dur": 44, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126512, "dur": 1, "ph": "X", "name": "ProcessMessages 1055", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126537, "dur": 27, "ph": "X", "name": "ReadAsync 1055", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126565, "dur": 1, "ph": "X", "name": "ProcessMessages 1416", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126568, "dur": 32, "ph": "X", "name": "ReadAsync 1416", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126602, "dur": 1, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126605, "dur": 58, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126664, "dur": 1, "ph": "X", "name": "ProcessMessages 1220", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126666, "dur": 30, "ph": "X", "name": "ReadAsync 1220", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126697, "dur": 1, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126699, "dur": 30, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126731, "dur": 29, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126762, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126763, "dur": 46, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126815, "dur": 1, "ph": "X", "name": "ProcessMessages 1271", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126817, "dur": 40, "ph": "X", "name": "ReadAsync 1271", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126859, "dur": 1, "ph": "X", "name": "ProcessMessages 1028", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126862, "dur": 27, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126890, "dur": 1, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126892, "dur": 33, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126926, "dur": 1, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126928, "dur": 37, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126969, "dur": 1, "ph": "X", "name": "ProcessMessages 870", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325126972, "dur": 43, "ph": "X", "name": "ReadAsync 870", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127021, "dur": 2, "ph": "X", "name": "ProcessMessages 1106", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127024, "dur": 40, "ph": "X", "name": "ReadAsync 1106", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127068, "dur": 2, "ph": "X", "name": "ProcessMessages 1140", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127072, "dur": 36, "ph": "X", "name": "ReadAsync 1140", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127110, "dur": 1, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127132, "dur": 31, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127165, "dur": 2, "ph": "X", "name": "ProcessMessages 1653", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127170, "dur": 26, "ph": "X", "name": "ReadAsync 1653", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127199, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127202, "dur": 84, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127290, "dur": 2, "ph": "X", "name": "ProcessMessages 1984", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127292, "dur": 40, "ph": "X", "name": "ReadAsync 1984", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127334, "dur": 1, "ph": "X", "name": "ProcessMessages 1170", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127335, "dur": 69, "ph": "X", "name": "ReadAsync 1170", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127406, "dur": 1, "ph": "X", "name": "ProcessMessages 1122", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127409, "dur": 65, "ph": "X", "name": "ReadAsync 1122", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127479, "dur": 1, "ph": "X", "name": "ProcessMessages 2397", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127482, "dur": 36, "ph": "X", "name": "ReadAsync 2397", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127520, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127522, "dur": 54, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127581, "dur": 1, "ph": "X", "name": "ProcessMessages 1489", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127583, "dur": 51, "ph": "X", "name": "ReadAsync 1489", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127730, "dur": 1, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127733, "dur": 66, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127802, "dur": 3, "ph": "X", "name": "ProcessMessages 2028", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127807, "dur": 114, "ph": "X", "name": "ReadAsync 2028", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127923, "dur": 2, "ph": "X", "name": "ProcessMessages 2045", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325127926, "dur": 90, "ph": "X", "name": "ReadAsync 2045", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128031, "dur": 2, "ph": "X", "name": "ProcessMessages 1446", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128033, "dur": 47, "ph": "X", "name": "ReadAsync 1446", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128082, "dur": 1, "ph": "X", "name": "ProcessMessages 1833", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128084, "dur": 48, "ph": "X", "name": "ReadAsync 1833", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128133, "dur": 1, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128135, "dur": 54, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128199, "dur": 1, "ph": "X", "name": "ProcessMessages 1489", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128201, "dur": 51, "ph": "X", "name": "ReadAsync 1489", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128254, "dur": 1, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128261, "dur": 54, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128316, "dur": 1, "ph": "X", "name": "ProcessMessages 1507", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128319, "dur": 51, "ph": "X", "name": "ReadAsync 1507", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128372, "dur": 1, "ph": "X", "name": "ProcessMessages 981", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128375, "dur": 34, "ph": "X", "name": "ReadAsync 981", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128412, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128414, "dur": 48, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128469, "dur": 1, "ph": "X", "name": "ProcessMessages 926", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128481, "dur": 335, "ph": "X", "name": "ReadAsync 926", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128818, "dur": 2, "ph": "X", "name": "ProcessMessages 1946", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128821, "dur": 37, "ph": "X", "name": "ReadAsync 1946", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128860, "dur": 1, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128861, "dur": 38, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128902, "dur": 2, "ph": "X", "name": "ProcessMessages 843", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325128905, "dur": 255, "ph": "X", "name": "ReadAsync 843", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325129171, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325129211, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325129214, "dur": 84, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325129299, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325129302, "dur": 193, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325129512, "dur": 269, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325129784, "dur": 362, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325130147, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325130149, "dur": 610, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325130761, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325130762, "dur": 469, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325131233, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325131235, "dur": 444, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325131682, "dur": 137, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325131822, "dur": 184, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325132050, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325132052, "dur": 486, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325132541, "dur": 171, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325132716, "dur": 386, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325133129, "dur": 233, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325133412, "dur": 336, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325133764, "dur": 142, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325133911, "dur": 451, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325134364, "dur": 7, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325134375, "dur": 27, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325134405, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325134406, "dur": 195, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325134602, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325134604, "dur": 178, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325134784, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325134786, "dur": 513, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325135301, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325135303, "dur": 149, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325135455, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325135457, "dur": 514, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325135974, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325135976, "dur": 501, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325136495, "dur": 11, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325136515, "dur": 253, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325136775, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325136777, "dur": 514, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325137294, "dur": 2, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325137297, "dur": 482, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325137783, "dur": 157, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325137943, "dur": 371, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325138315, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325138317, "dur": 27, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325138347, "dur": 477, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325138826, "dur": 167, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325138995, "dur": 1, "ph": "X", "name": "ProcessMessages 1164", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325138997, "dur": 51, "ph": "X", "name": "ReadAsync 1164", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325139049, "dur": 1, "ph": "X", "name": "ProcessMessages 902", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325139051, "dur": 358, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325139410, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325139412, "dur": 533, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325139948, "dur": 2, "ph": "X", "name": "ProcessMessages 1822", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325139952, "dur": 32, "ph": "X", "name": "ReadAsync 1822", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325139986, "dur": 1, "ph": "X", "name": "ProcessMessages 899", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325139988, "dur": 548, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325140539, "dur": 154, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325140696, "dur": 340, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325141038, "dur": 1, "ph": "X", "name": "ProcessMessages 1238", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325141040, "dur": 33, "ph": "X", "name": "ReadAsync 1238", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325141075, "dur": 1, "ph": "X", "name": "ProcessMessages 1002", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325141077, "dur": 29, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325141110, "dur": 671, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325141784, "dur": 29, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325141825, "dur": 257, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325142083, "dur": 1, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325142085, "dur": 439, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325142527, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325142529, "dur": 153, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325142698, "dur": 1, "ph": "X", "name": "ProcessMessages 1163", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325142701, "dur": 20, "ph": "X", "name": "ReadAsync 1163", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325142722, "dur": 1, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325142724, "dur": 442, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325143168, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325143169, "dur": 153, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325143324, "dur": 1, "ph": "X", "name": "ProcessMessages 1174", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325143325, "dur": 27, "ph": "X", "name": "ReadAsync 1174", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325143355, "dur": 26, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325143385, "dur": 357, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325143743, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325143745, "dur": 426, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325144174, "dur": 140, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325144316, "dur": 361, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325144679, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325144681, "dur": 41, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325144725, "dur": 619, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325145345, "dur": 1, "ph": "X", "name": "ProcessMessages 1110", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325145348, "dur": 32, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325145382, "dur": 443, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325145840, "dur": 1, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325145842, "dur": 406, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325146250, "dur": 1, "ph": "X", "name": "ProcessMessages 911", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325146252, "dur": 427, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325146680, "dur": 1, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325146682, "dur": 2370, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325149055, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325149056, "dur": 170, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325149228, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325149229, "dur": 274, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325149507, "dur": 139, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325149649, "dur": 1, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325149651, "dur": 39, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325149693, "dur": 648, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325150342, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325150344, "dur": 184, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325150531, "dur": 269, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325150804, "dur": 312, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325151117, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325151129, "dur": 38, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325151168, "dur": 1, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325151170, "dur": 109, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325151282, "dur": 197, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325151482, "dur": 56, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325151541, "dur": 34, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325151577, "dur": 397, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325151977, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325151979, "dur": 305, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325152285, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325152289, "dur": 254, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325152544, "dur": 1, "ph": "X", "name": "ProcessMessages 968", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325152546, "dur": 314, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325152862, "dur": 1, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325152864, "dur": 185, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325153051, "dur": 46, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325153100, "dur": 383, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325153484, "dur": 1, "ph": "X", "name": "ProcessMessages 953", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325153486, "dur": 228, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325153716, "dur": 2, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325153718, "dur": 198, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325153918, "dur": 1, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325153920, "dur": 216, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325154192, "dur": 130, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325154324, "dur": 188, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325154514, "dur": 2, "ph": "X", "name": "ProcessMessages 1103", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325154686, "dur": 24, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325154712, "dur": 1, "ph": "X", "name": "ProcessMessages 1321", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325154714, "dur": 244, "ph": "X", "name": "ReadAsync 1321", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325154960, "dur": 1, "ph": "X", "name": "ProcessMessages 1143", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325154962, "dur": 37, "ph": "X", "name": "ReadAsync 1143", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325155001, "dur": 1, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325155003, "dur": 36, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325155042, "dur": 289, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325155333, "dur": 1, "ph": "X", "name": "ProcessMessages 1214", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325155335, "dur": 44, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325155381, "dur": 1, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325155383, "dur": 40, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325155424, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325155426, "dur": 225, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325155653, "dur": 205, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325155859, "dur": 246, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156107, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156148, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156150, "dur": 42, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156194, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156197, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156240, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156242, "dur": 99, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156343, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156440, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156444, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156493, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156638, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156640, "dur": 89, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156768, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156804, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156905, "dur": 59, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156966, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325156968, "dur": 35, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325157007, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325157278, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325157280, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325157322, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325157324, "dur": 43, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325157370, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325157399, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325157490, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325157492, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325157583, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325157631, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325157693, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325157731, "dur": 174, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325157907, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325157910, "dur": 112, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325158026, "dur": 673, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325158702, "dur": 4, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325158722, "dur": 77, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325158802, "dur": 102, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325158908, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325158947, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325158950, "dur": 133, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325159085, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325159086, "dur": 87, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325159177, "dur": 92, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325159409, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325159411, "dur": 46, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325159459, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325159461, "dur": 399, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325159862, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325159864, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325159900, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325159903, "dur": 76, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325159989, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160080, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160083, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160135, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160187, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160279, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160330, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160378, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160455, "dur": 137, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160595, "dur": 54, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160659, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160661, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160696, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160792, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160793, "dur": 166, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160961, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325160963, "dur": 140, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325161105, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325161107, "dur": 910, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162019, "dur": 19, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162039, "dur": 30, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162072, "dur": 101, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162174, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162176, "dur": 38, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162217, "dur": 78, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162296, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162298, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162329, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162364, "dur": 100, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162465, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162467, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162508, "dur": 38, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162549, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162591, "dur": 77, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162736, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162738, "dur": 127, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162867, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162869, "dur": 34, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162906, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325162973, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163017, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163019, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163079, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163081, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163105, "dur": 99, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163206, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163208, "dur": 71, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163280, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163282, "dur": 70, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163355, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163400, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163402, "dur": 107, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163512, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163564, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163617, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325163753, "dur": 14790, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325178549, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325178552, "dur": 11026, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325189581, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325189583, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325189745, "dur": 274, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325190023, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325190142, "dur": 3845, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325193991, "dur": 10131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325204128, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325204247, "dur": 1944, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325206194, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325206195, "dur": 128, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325206326, "dur": 190, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325206518, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325206520, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325206569, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325206571, "dur": 609, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325207183, "dur": 345, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325207530, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325207577, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325207620, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325207774, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325207931, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325208002, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325208003, "dur": 640, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325208648, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325208772, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325208859, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325208861, "dur": 335, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325209197, "dur": 8, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325209207, "dur": 205, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325209416, "dur": 335, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325209753, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325209760, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325209788, "dur": 581, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325210372, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325210417, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325210512, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325210592, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325210593, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325210639, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325210703, "dur": 218, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325210922, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325210924, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325210993, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325211163, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325211166, "dur": 295, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325211463, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325211465, "dur": 252, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325211720, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325211757, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325211833, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325211938, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325212065, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325212116, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325212219, "dur": 404, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325212630, "dur": 3128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325215762, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325215763, "dur": 2613, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325218380, "dur": 930, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325219314, "dur": 303, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325219621, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325219623, "dur": 109, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325219735, "dur": 235, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325219975, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325220011, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325220190, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325220255, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325220390, "dur": 204, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325220596, "dur": 107, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325220706, "dur": 295, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325221003, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325221006, "dur": 104, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325221114, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325221221, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325221224, "dur": 209, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325221436, "dur": 266, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325221706, "dur": 418, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325222127, "dur": 172, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325222300, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325222302, "dur": 325, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325222629, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325222630, "dur": 187, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325222822, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325222918, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325222922, "dur": 120, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325223045, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325223094, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325223225, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325223227, "dur": 361, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325223590, "dur": 324, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325223917, "dur": 174, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325224094, "dur": 237, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325224347, "dur": 278, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325224627, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325224671, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325224797, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325224863, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325224901, "dur": 769, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325225674, "dur": 188, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325225864, "dur": 395, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325226261, "dur": 53, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325226315, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325226317, "dur": 83, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325226403, "dur": 84, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325226490, "dur": 199, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325226692, "dur": 451, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325227145, "dur": 117, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325227265, "dur": 172, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325227438, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325227447, "dur": 292, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325227742, "dur": 104, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325227849, "dur": 178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325228029, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325228078, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325228117, "dur": 180, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325228301, "dur": 284, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325228588, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325228589, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325228628, "dur": 340, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325228971, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325229068, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325229070, "dur": 18, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325229090, "dur": 159, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325229252, "dur": 278, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325229534, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325229645, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325229746, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325229958, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325230062, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325230064, "dur": 177, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325230243, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325230366, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325230369, "dur": 405, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325230776, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325230779, "dur": 107795, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325338581, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325338583, "dur": 63, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325338650, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325338652, "dur": 43, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325338699, "dur": 31, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325338756, "dur": 37, "ph": "X", "name": "ReadAsync 4569", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325338796, "dur": 20, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325338818, "dur": 27, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325338846, "dur": 5710, "ph": "X", "name": "ProcessMessages 6828", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325344562, "dur": 598, "ph": "X", "name": "ReadAsync 6828", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325345165, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325345170, "dur": 493, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325345667, "dur": 2070, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325347740, "dur": 614, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325348358, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325348360, "dur": 426, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325348789, "dur": 139, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325348932, "dur": 505, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325349440, "dur": 8, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325349449, "dur": 1620, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325351075, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325351077, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325351193, "dur": 740, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325351936, "dur": 383, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325352322, "dur": 1004, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325353330, "dur": 858, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325354193, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325354195, "dur": 727, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325354925, "dur": 517, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325355444, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325355446, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325355504, "dur": 1139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325356647, "dur": 176, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325356825, "dur": 1301, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325358130, "dur": 883, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325359016, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325359018, "dur": 861, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325359881, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325359883, "dur": 414, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325360300, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325360361, "dur": 600, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325360965, "dur": 2127, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325363094, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325363096, "dur": 1405, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325364509, "dur": 420, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325364933, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325364935, "dur": 269, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325365208, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325365333, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325365435, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325365491, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325365493, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325365558, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325365661, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325365698, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325365744, "dur": 35, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325365782, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325365876, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325365879, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325365930, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325365983, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366042, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366106, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366170, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366222, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366335, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366390, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366392, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366438, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366509, "dur": 40, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366552, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366605, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366679, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366724, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366762, "dur": 135, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366899, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366953, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325366954, "dur": 133, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325367089, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325367090, "dur": 94, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325367186, "dur": 139, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325367327, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325367329, "dur": 89, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325367420, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325367469, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325367471, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325367523, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325367594, "dur": 134, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325367730, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325367732, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325367786, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325367830, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325367878, "dur": 130, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325368011, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325368056, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325368104, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325368208, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325368241, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325368338, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325368417, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325368462, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325368501, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325368554, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325368621, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325368736, "dur": 261, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325369068, "dur": 138, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325369208, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325369211, "dur": 369, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325369583, "dur": 610, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325370196, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325370268, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325370309, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325370332, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325370352, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325370506, "dur": 19128, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325389639, "dur": 30, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325389672, "dur": 1136, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325390811, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325390823, "dur": 2159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325392986, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325392988, "dur": 236, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325393227, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325393228, "dur": 344277, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325737512, "dur": 3, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325737516, "dur": 58, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325737583, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325737585, "dur": 68, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325737667, "dur": 68, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325737740, "dur": 43, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325737785, "dur": 46, "ph": "X", "name": "ProcessMessages 6430", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325737833, "dur": 64, "ph": "X", "name": "ReadAsync 6430", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325737900, "dur": 53, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325737957, "dur": 44, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325738005, "dur": 33, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325738041, "dur": 48, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325738091, "dur": 33, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325738125, "dur": 10, "ph": "X", "name": "ProcessMessages 1407", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325738136, "dur": 14606, "ph": "X", "name": "ReadAsync 1407", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325752749, "dur": 13, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325752763, "dur": 761, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325753529, "dur": 33, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325753568, "dur": 118, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325753690, "dur": 221, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 37977, "tid": 12884901888, "ts": 1758795325753913, "dur": 9633, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 37977, "tid": 647, "ts": 1758795325775464, "dur": 1388, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 37977, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 37977, "tid": 8589934592, "ts": 1758795324948106, "dur": 235882, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 37977, "tid": 8589934592, "ts": 1758795325183994, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 37977, "tid": 8589934592, "ts": 1758795325184003, "dur": 1921, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 37977, "tid": 647, "ts": 1758795325776855, "dur": 34, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 37977, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 37977, "tid": 4294967296, "ts": 1758795324912457, "dur": 852870, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 37977, "tid": 4294967296, "ts": 1758795324916614, "dur": 27322, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 37977, "tid": 4294967296, "ts": 1758795325765490, "dur": 4326, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 37977, "tid": 4294967296, "ts": 1758795325767339, "dur": 1529, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 37977, "tid": 4294967296, "ts": 1758795325769879, "dur": 11, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 37977, "tid": 647, "ts": 1758795325776892, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1758795324951002, "dur": 2945, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758795324953955, "dur": 16067, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758795324970073, "dur": 91, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1758795324970164, "dur": 129, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758795324971010, "dur": 186, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0CB46753FA7C312F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1758795324971986, "dur": 352, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_6450FF118201D284.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1758795324973205, "dur": 419, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1758795324974408, "dur": 3517, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1758795324980244, "dur": 87454, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_FF44434556825F42.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1758795325068220, "dur": 56609, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_E35BE511F4283487.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1758795324970301, "dur": 185978, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758795325156294, "dur": 597858, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758795325754284, "dur": 3752, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1758795324970198, "dur": 186106, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325156313, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1758795325156500, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_C6011564DA201A52.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325156884, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_85BC3F815A006E37.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325157109, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_7D46FF134A659955.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325157344, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325157445, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1DB14EE0893E74EF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325157562, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325157653, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_05C8CFED34CB8739.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325157790, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325157867, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_FD774B970C96B0D0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325157973, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325158066, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_A9F3FB8E6C0780FC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325158227, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325158325, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_FD04B64CA8B1F690.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325158474, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325158555, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_FFC4A5DF3CDD2051.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325158695, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325158777, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BB987306F50BED91.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325158884, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325158966, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_910BD9D1CE0E2BD1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325159084, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325159146, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_730EC33EED2E94C6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325159254, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325159332, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2F018D732F244A87.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325159448, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325159526, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325159624, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_927C166CA6DDB07E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325159710, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325159806, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1758795325160070, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325160158, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1758795325160605, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1758795325160832, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1758795325161079, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1758795325161293, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325161407, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325161506, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1758795325161578, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325161710, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1758795325161872, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325161939, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325162092, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1758795325162824, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325162901, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325163006, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325163137, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325163248, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325163367, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1758795325163521, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325163596, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325163719, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325163825, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325163919, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325164052, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1758795325164247, "dur": 1495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325165742, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325167321, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325168793, "dur": 1351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325170144, "dur": 1529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325171673, "dur": 1431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325173105, "dur": 1794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325174899, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325176380, "dur": 1708, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@287dd3fa176c/Editor/Generation/Descriptors/AdditionalCommandDescriptor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1758795325176380, "dur": 3327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325179707, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325180794, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325181852, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325182949, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325184005, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325185132, "dur": 1405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325186807, "dur": 469, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1758795325187277, "dur": 2227, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1758795325189504, "dur": 659, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1758795325186537, "dur": 3626, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325190163, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325191372, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325192530, "dur": 1282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325193812, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325194927, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325196001, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325197154, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325198217, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325199325, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325200435, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325201577, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325202886, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325204019, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325205341, "dur": 1618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325207005, "dur": 1078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758795325208083, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325208314, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325208587, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325208642, "dur": 1750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325210392, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325210448, "dur": 1088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325211537, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325211614, "dur": 8334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758795325219948, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325220140, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325220657, "dur": 2297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758795325222955, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325223110, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_8456852F39E72DEA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325223298, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325223425, "dur": 1822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758795325225247, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325225379, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325225512, "dur": 95, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325225697, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325225788, "dur": 65, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325226198, "dur": 1719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325227918, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795325228073, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758795325228645, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325228784, "dur": 112084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325340871, "dur": 8075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1758795325348946, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325349038, "dur": 8216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1758795325357254, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325357344, "dur": 4038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1758795325361383, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325361475, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1758795325361568, "dur": 3904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1758795325365472, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795325365594, "dur": 5367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1758795325371016, "dur": 383108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795324970203, "dur": 186122, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325156334, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1758795325156516, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_3BE3DE4E4FF3C20F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325156886, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_51F928846D19D7E2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325157115, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325157211, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_BF21A61A4CC82206.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325157365, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325157465, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_23AD0A7361E5AF91.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325157571, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325157667, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F56BB6A2C38EBCEC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325157823, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325157902, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_8B47C372A80AB1D5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325158010, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325158117, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_2F7C47F2C923EECA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325158257, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325158358, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_1879345459B1F65E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325158569, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3260E1D96E1E22C0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325158789, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_5F70C3E0135C03E6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325158883, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325158970, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_102C078AEE4D5C34.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325159085, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325159162, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1758795325159298, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325159358, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2A5CE0F0450FED37.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325159490, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325159556, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1758795325159800, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325159882, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325160257, "dur": 16289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758795325176547, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325176712, "dur": 1350, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@287dd3fa176c/Editor/Drawing/Views/Slots/ColorRGBSlotControlView.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758795325176712, "dur": 2717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325179429, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325180501, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325181564, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325182642, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325183722, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325184774, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325186090, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325187484, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325188544, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325189661, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325190987, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325192028, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325193153, "dur": 1351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325194504, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325195600, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325196709, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325197778, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325198886, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325199981, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325201108, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325202333, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325203575, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325204699, "dur": 1574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325206274, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325206796, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325206858, "dur": 2290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758795325209148, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325209264, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_02929A3ECA791895.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325209359, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325209433, "dur": 1514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325210992, "dur": 830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325211856, "dur": 8268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758795325220124, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325220291, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325220398, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325220515, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325220569, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325221216, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795325221322, "dur": 956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758795325222279, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325222436, "dur": 3066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1758795325225503, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325225595, "dur": 726, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325339100, "dur": 412, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325226592, "dur": 112937, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1758795325340862, "dur": 4855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1758795325345719, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325345862, "dur": 3714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1758795325349577, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325349661, "dur": 3275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1758795325352937, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325353014, "dur": 4419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1758795325357434, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325357520, "dur": 4186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1758795325361706, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325361826, "dur": 3289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1758795325365117, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325365240, "dur": 5635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1758795325370930, "dur": 382312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795325753255, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/post-processed/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1758795325753245, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1758795325753356, "dur": 739, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1758795324970210, "dur": 186126, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325156341, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_320B820FE8FC5AE8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325156607, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_2338B6331E4A589D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325157042, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325157128, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_6BADCFBF033360D0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325157281, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325157376, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_8FC1791ACEE3E790.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325157650, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325157745, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_5341A2509D5ED266.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325157881, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325157952, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_6DFD6E15EC2CCADA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325158083, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325158191, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_AD829E7041D38787.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325158335, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325158437, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_99C535C3D6E374C3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325158607, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325158726, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_E5C0679566569A75.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325158839, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325158906, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_11639AB4BFA07D5F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325159030, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325159109, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D8293B7A6760759A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325159221, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325159287, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_3CB6A4B5DF319A4A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325159397, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325159478, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325159599, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325159674, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_37F04A890A90A5DB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325159806, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325159900, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1758795325160036, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325160108, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758795325160307, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1758795325160569, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758795325161038, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758795325161229, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325161338, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325161405, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758795325161574, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325161702, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758795325161886, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325161957, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325162028, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758795325162079, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325162140, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758795325162703, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325162831, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325162926, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325163024, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758795325163280, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325163391, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758795325163514, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325163581, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325163715, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325163812, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325163911, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325164094, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325164201, "dur": 1482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325165683, "dur": 1574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325167257, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325168758, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325170106, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325171620, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325173064, "dur": 1792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325174856, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325176356, "dur": 1743, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@287dd3fa176c/Editor/Generation/Enumerations/KeywordShaderStage.cs"}}, {"pid": 12345, "tid": 3, "ts": 1758795325176356, "dur": 3371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325179728, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325180797, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325181855, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325182920, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325183970, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325185078, "dur": 1360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325186439, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325187719, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325188794, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325189932, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325191211, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325192306, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325193524, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325194747, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325195809, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325196951, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325197989, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325199110, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325200208, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325201343, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325202621, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325203786, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325205096, "dur": 1915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325207056, "dur": 2637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758795325209693, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325209875, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_9957BA1AF208A4B4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325209962, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325210033, "dur": 1259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325211333, "dur": 891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758795325212225, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325212372, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325212453, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325212864, "dur": 4454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758795325217318, "dur": 1547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325218877, "dur": 1177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325220055, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325220142, "dur": 3184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758795325223326, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325223537, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PerformanceTesting.ref.dll_B4B9F9210D591244.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325223587, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325223654, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325224432, "dur": 1897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758795325226330, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325226422, "dur": 3199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325229622, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795325229716, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758795325230187, "dur": 110712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325340900, "dur": 5380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1758795325346281, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325346408, "dur": 3688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1758795325350096, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325350193, "dur": 5688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1758795325355899, "dur": 4638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1758795325360537, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325360845, "dur": 5582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1758795325366428, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325366528, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325366768, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325366867, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325367061, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325367346, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325367442, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325367584, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758795325367643, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325367736, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325367886, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325368044, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325368306, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325368521, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1758795325368574, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325368745, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325368904, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1758795325368960, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325369284, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325369433, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325369707, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325369759, "dur": 1150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325370945, "dur": 382306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795325753313, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/post-processed/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758795325753252, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758795325753390, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795324970216, "dur": 186129, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325156350, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_3F0AF0E8FD5B91AB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325156610, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_139664EF1148BEA3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325156870, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_AE483740772154F6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325157084, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325157173, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B4E6D214D3E2A1D4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325157326, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325157429, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CF5F61BCAC5D1F90.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325157562, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325157635, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_C0AB20643B6FF208.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325157768, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325157852, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_968EEE9C3279D1EC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325158004, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325158102, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_C69304A59A7A1B74.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325158290, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325158381, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_7E945FCC8473B7C7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325158575, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_21D14335F6F8B5A4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325158698, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325158783, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A18767039B605D8A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325158884, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325158984, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5BA053E42555A2B5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325159085, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325159156, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B964A68D830AC6BF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325159318, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_57F26D0ACBE8DD19.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325159418, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325159515, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325159604, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1758795325159821, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325159910, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1758795325160042, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325160095, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325160172, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1758795325160327, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1758795325160613, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1758795325160921, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1758795325161250, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325161351, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325161422, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325161518, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325161601, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325161728, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1758795325161884, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325161949, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325162095, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1758795325162236, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325162324, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325162383, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325162451, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1758795325162705, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325162834, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325162934, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325163037, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325163121, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325163239, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325163344, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325163437, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1758795325163523, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325163621, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325163739, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325163840, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325163929, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325164077, "dur": 1503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325165580, "dur": 1561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325167141, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325168639, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325169955, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325171499, "dur": 1450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325172949, "dur": 1770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325174720, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325176240, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325176851, "dur": 1246, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@287dd3fa176c/Editor/Drawing/Interfaces/ISGViewModel.cs"}}, {"pid": 12345, "tid": 4, "ts": 1758795325176851, "dur": 2853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325179704, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325180771, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325181844, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325182928, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325183966, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325185082, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325186451, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325187747, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325188815, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325189944, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325191219, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325192334, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325193557, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325194761, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325195814, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325196940, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325197985, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325199107, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325200211, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325201329, "dur": 1257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325202586, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325203757, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325205027, "dur": 1521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325206550, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325206609, "dur": 1813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758795325208423, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325208543, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325208623, "dur": 1708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325210332, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325210390, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325211290, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758795325212123, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325212407, "dur": 8173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758795325220580, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325220890, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325220949, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325221648, "dur": 948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325222641, "dur": 2295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758795325224937, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325225085, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325225751, "dur": 758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758795325226509, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325226624, "dur": 4106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325230731, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795325230906, "dur": 109960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325340870, "dur": 4250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1758795325345121, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325345201, "dur": 6574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1758795325351775, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325351875, "dur": 4165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1758795325356040, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325356132, "dur": 3985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1758795325360118, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325360244, "dur": 5235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1758795325365479, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795325365617, "dur": 5508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1758795325371146, "dur": 382974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795324970234, "dur": 186121, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325156361, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_ACDD54761A8A8AD1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325156622, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_621CCEC35B13C3FF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325156964, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_5AFB90E5B3938F6A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325157128, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325157217, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_611725854B2C7A29.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325157465, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325157537, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_67D1F298EAAF8408.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325157692, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325157794, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_E1CEC1A55253DF7A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325157910, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325157987, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_734ECF7F5E8D9BFB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325158134, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325158249, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_D53BC0BA2A1258D1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325158452, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325158512, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_56E9249B00D2323D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325158599, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325158686, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_0B92978C8F2A41F2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325158860, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_3A024D3CFAEFB3CE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325159000, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325159092, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_712FA009D6907B2B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325159200, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325159278, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_6F2DFF6A42ECB2AA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325159375, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325159450, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_F6EE2176487706CE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325159585, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325159657, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_49F2D3B3C857FB89.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325159740, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325159861, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325159944, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1758795325160068, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325160198, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1758795325160407, "dur": 674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1758795325161087, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1758795325161357, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325161427, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1758795325161599, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325161721, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325161800, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325161903, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325161985, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1758795325162123, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325162211, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325162295, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325162369, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325162426, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325162603, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1758795325162762, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325162841, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325162941, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325163045, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325163124, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325163237, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325163357, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1758795325163487, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325163554, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325163651, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325163753, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325163855, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325163994, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325164107, "dur": 1527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325165634, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325167197, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325168691, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325170020, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325171581, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325173005, "dur": 1733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325174738, "dur": 1541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325176280, "dur": 1759, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@287dd3fa176c/Editor/Generation/Targets/BuiltIn/Editor/ShaderGraph/BuiltInFields.cs"}}, {"pid": 12345, "tid": 5, "ts": 1758795325176280, "dur": 3151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325179432, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325180515, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325181600, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325182652, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325183717, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325184784, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325186100, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325187504, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325188556, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325189669, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325190999, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325192030, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325193160, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325194517, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325195597, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325196716, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325197770, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325198905, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325199985, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325201093, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325202323, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325203559, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325204689, "dur": 1620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325206310, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325207132, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325207215, "dur": 2047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325209262, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325209498, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_16F8EA6B67022FB5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325209608, "dur": 1462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325211108, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325211586, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325212558, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325212694, "dur": 2779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325215473, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325215784, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325215938, "dur": 7347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325223286, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325223494, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325223587, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325223739, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325223890, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325223947, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325224262, "dur": 119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325224397, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325224701, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325225091, "dur": 1427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325226571, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325227365, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325227504, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325228395, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795325228650, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325229285, "dur": 111662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325340947, "dur": 2696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325343644, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325343720, "dur": 4470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325348190, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325348279, "dur": 2687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325350967, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325351070, "dur": 3773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325354844, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325354926, "dur": 4695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325359621, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325359753, "dur": 4987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325364740, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325364960, "dur": 5841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325370829, "dur": 20190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325391020, "dur": 1806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325393317, "dur": 200, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325738046, "dur": 386, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795325393752, "dur": 344689, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1758795325753251, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758795325753241, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758795325753328, "dur": 750, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758795325754079, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795324970251, "dur": 186113, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325156371, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_2C20B8F0E17E12AF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325156723, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_1349457C77F70AD4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325157102, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_14EB62D6C267D167.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325157287, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325157392, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D7A9AF8C5E67A0AE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325157523, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325157595, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4B36DE508E234CBA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325157728, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325157830, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_F2316A31D98809E8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325157943, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325158021, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_D28966A0C9F1BC15.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325158178, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325158270, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_837685507179BFD9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325158439, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325158508, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_2E74B195F350D70F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325158626, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325158710, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_20CB703B61251BE5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325158857, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_ED8B032DF01A4A2C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325158983, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325159049, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_0E20F46DE1E1A65B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325159190, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325159265, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758795325159816, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325159896, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325159970, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758795325160348, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1758795325160540, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F5791B14F366A51E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325160737, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1758795325160846, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758795325161031, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758795325161282, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758795325161353, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325161424, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325161521, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758795325161595, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325161749, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758795325161857, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325161926, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325162013, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758795325162259, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325162330, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325162390, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325162462, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325162604, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325162740, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325162825, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325162921, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325163029, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325163166, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325163261, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325163373, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325163439, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758795325163562, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325163669, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325163768, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325163868, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325164004, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.Entities.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758795325164275, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325164364, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325165829, "dur": 1592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325167421, "dur": 1469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325168890, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325170226, "dur": 1537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325171764, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325173255, "dur": 1774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325175029, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325176452, "dur": 1615, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@287dd3fa176c/Editor/Generation/Collections/DefineCollection.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758795325176452, "dur": 3188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325179640, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325180705, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325181754, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325182851, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325183925, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325185053, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325186429, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325187726, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325188805, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325189927, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325191193, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325192293, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325193481, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325194710, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325195783, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325196918, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325197977, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325199089, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325200197, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325201312, "dur": 1260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325202572, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325203744, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325205021, "dur": 2107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325207173, "dur": 11732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758795325218907, "dur": 955, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325219962, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325220611, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325220741, "dur": 5971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758795325226713, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325226811, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325227148, "dur": 1243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758795325228391, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325228487, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795325228765, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758795325229214, "dur": 111662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325340877, "dur": 3225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1758795325344103, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325344245, "dur": 5186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1758795325349479, "dur": 4491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1758795325353970, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325354235, "dur": 5302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.PlasticSCM.Editor.Entities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1758795325359538, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325359663, "dur": 4491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1758795325364155, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795325364273, "dur": 6688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1758795325371018, "dur": 383142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795324970275, "dur": 186103, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325156385, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_641E8BB24D9C991B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325156663, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_A1002E9E9F083797.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325156755, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_646E6BF09BC34AF8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325157153, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325157247, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_05CCC7FD7CF0316D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325157624, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325157721, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B861AB185A7695D4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325157850, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325157928, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E921E8B78225857D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325158044, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325158155, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_CABAF2279C00D800.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325158290, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325158396, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_71D986BE924F9760.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325158542, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325158614, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3193DD45B90182D7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325158798, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325158883, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_A49A776348DF5E4E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325159010, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325159069, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_9A3FDE96F0266F50.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325159174, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325159255, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1758795325159389, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325159461, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325160248, "dur": 16695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325176976, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325177053, "dur": 12796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325189850, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325190193, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_A805A84D9B0C22D9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325190275, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325190370, "dur": 4211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325194632, "dur": 9907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325204539, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325204737, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_0174E068A5FA251B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325204789, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325204885, "dur": 2331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325207266, "dur": 3637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325210903, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325211067, "dur": 1417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325212525, "dur": 8497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325221022, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325221181, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325221528, "dur": 3035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325224563, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325224703, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325224764, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325225490, "dur": 1159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325226649, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325226758, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325227086, "dur": 761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325227911, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325228090, "dur": 1627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325229757, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325229870, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325230593, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795325230691, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325231001, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325232148, "dur": 158100, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325390994, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325391417, "dur": 1605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325393548, "dur": 285, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325738151, "dur": 624, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795325393932, "dur": 344849, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1758795325753249, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1758795325753338, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795324970280, "dur": 186109, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325156395, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_C57112EE168C0CEE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325156662, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_AD0F59CECD9086B5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325156741, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_6C13B72F801586DB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325157038, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325157094, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_7FB03B9F43028903.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325157259, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325157365, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7EB4E75F718193E6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325157511, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325157586, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_C1844920F06A43FF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325157713, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325157809, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0CB46753FA7C312F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325157962, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325158052, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EB7DEB5E390DBA80.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325158227, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325158336, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_07C436A676AA1A59.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325158540, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325158626, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_BBB5D930EB61518F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325158839, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325158926, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_4EB5407DF9A5D812.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325159058, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325159126, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_26633F92B9A939A2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325159241, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325159302, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_6BB7F73D7038074E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325159400, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325159497, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325159621, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325159680, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758795325159796, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325159877, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758795325160030, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758795325160558, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758795325160789, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758795325161005, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758795325161245, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325161339, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325161413, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325161511, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325161588, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325161706, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758795325161884, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325161954, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1758795325162151, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325162262, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325162347, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325162403, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325162487, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325162567, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758795325162790, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325162885, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325162996, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325163101, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325163187, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325163303, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325163407, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758795325163547, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325163683, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325163797, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325163896, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325164035, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325164164, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325165655, "dur": 1584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325167240, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325168722, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325170084, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325171604, "dur": 1440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325173044, "dur": 1769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325174813, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325176301, "dur": 1789, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@287dd3fa176c/Editor/Generation/Processors/GeneratorDerivativeUtils.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758795325176301, "dur": 3391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325179693, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325180755, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325181837, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325182937, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325183992, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325185127, "dur": 1372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325186499, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325187767, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325188834, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325189965, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325191228, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325192341, "dur": 1243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325193584, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325194778, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325195856, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325197004, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325198058, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325199176, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325200276, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325201409, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325202695, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325203844, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325205093, "dur": 1711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325206804, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325206906, "dur": 4456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758795325211362, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325211522, "dur": 1558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758795325213080, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325213236, "dur": 8060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758795325221297, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325221448, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325221707, "dur": 1058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758795325222765, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325222911, "dur": 2071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758795325224982, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325225357, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325225435, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325225495, "dur": 100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325225636, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325225689, "dur": 64, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325225800, "dur": 60, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325226195, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325226856, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325227154, "dur": 1446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758795325228601, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325228717, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325228901, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758795325229619, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325229707, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758795325230281, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325230372, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758795325230726, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795325230887, "dur": 110017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325340904, "dur": 3294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1758795325344199, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325344308, "dur": 5037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1758795325349359, "dur": 2368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1758795325351727, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325351803, "dur": 3769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1758795325355572, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325355669, "dur": 5213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1758795325360882, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325360993, "dur": 4855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1758795325365848, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325366036, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325366431, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325366493, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325366602, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325366947, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325367164, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325367273, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325367518, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325367805, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325368143, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325368243, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325368333, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325368463, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325368559, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325368703, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1758795325368766, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325368874, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325369124, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325369271, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325369384, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1758795325369443, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325369505, "dur": 709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325370214, "dur": 764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795325370995, "dur": 383139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795324970288, "dur": 186110, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325156406, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_374A8F32DB19802B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325156892, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_C49C0FC47CAFEAC5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325157128, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325157234, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_BB076BE8E91D3844.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325157382, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325157486, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_761201DEB2FFD5AE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325157603, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325157711, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_95D79878B91D771D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325157853, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325157937, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_46B2EC7768D1E935.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325158038, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325158143, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_63177FAB2D86FAFC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325158291, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325158389, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_9A9A08C6BDB0373A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325158564, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_532B9929C68EE5D8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325158729, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325158831, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_D4462A08249AAAE8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325158941, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325159022, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_56092ADF71655F79.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325159132, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325159219, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1758795325159355, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325159436, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325159575, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325159644, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_40479579ECF7C4CE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325159737, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325159818, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325159904, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325159985, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1758795325160051, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325160124, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1758795325160456, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1758795325160590, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1758795325160783, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1758795325160992, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1758795325161217, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325161317, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1758795325161564, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325161755, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325161828, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325161921, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325162010, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325162072, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1758795325162455, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325162603, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325162662, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325162764, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325162867, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325162985, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325163106, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325163196, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325163314, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1758795325163378, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325163494, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325163568, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325163679, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325163783, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325163887, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325164026, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325164121, "dur": 1530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325165651, "dur": 1582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325167233, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325168711, "dur": 1314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325170025, "dur": 1527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325171553, "dur": 1432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325172986, "dur": 1728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325174715, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325176745, "dur": 1350, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@287dd3fa176c/Editor/Generation/Targets/BuiltIn/Editor/ShaderGraph/Targets/BuiltInLitSubTarget.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758795325176246, "dur": 2528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325178774, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758795325178833, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325178989, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325179070, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325180196, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325181290, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325182336, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325183397, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325184416, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325185663, "dur": 1464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325187127, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325188197, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325189275, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325190568, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325190687, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325190764, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325191833, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325192986, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325194312, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325195386, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325196506, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325197591, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325198678, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325199761, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325200890, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325202062, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325203355, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325204458, "dur": 1533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325205991, "dur": 871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325206863, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325206945, "dur": 1028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758795325207973, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325208133, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325208574, "dur": 13153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758795325221728, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325221855, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325221918, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325222086, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325222267, "dur": 1990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758795325224257, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325224387, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325224569, "dur": 118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325224688, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325225145, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795325225851, "dur": 990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758795325226842, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325226940, "dur": 113992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325340944, "dur": 3701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1758795325344645, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325344723, "dur": 3675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1758795325348399, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325348480, "dur": 4103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1758795325352583, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325352667, "dur": 3488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1758795325356155, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325356225, "dur": 5323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1758795325361548, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325361644, "dur": 7985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1758795325369630, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325369737, "dur": 1090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325370828, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795325371150, "dur": 382973, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795324970294, "dur": 186113, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325156410, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_E409852C73E3C815.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325156633, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_F1E1BBCC6ABAE0DB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325157068, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_AE0AA47335F7F2B3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325157203, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325157302, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_BD28D25607AEE2E1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325157449, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325157538, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_29F333B2BA915C4E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325157657, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325157753, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_A83C1C8603B0CB24.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325157881, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325157956, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_463AABC76FBC4B88.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325158082, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325158196, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_CEEE8E02F8AB65CD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325158373, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325158497, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3CEA7E364D77D73C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325158626, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325158728, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11EA7240A47B953E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325158844, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325158947, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_2AA1A0FABCD57599.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325159060, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325159132, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_9663AEF5A1DB96B8.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325159249, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325159307, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_6450FF118201D284.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325159398, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325159489, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325159601, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325159668, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_4190A0BFBFAECF93.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325159738, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325159825, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325159933, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_283D5FC3BD6A67A2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325159987, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325160086, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325160167, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1758795325160397, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1758795325160560, "dur": 691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1758795325161251, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325161368, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325161436, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325161527, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325161623, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325161762, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325161841, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325161937, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325162017, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1758795325162186, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325162281, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325162415, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325162500, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325162653, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325162749, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325162824, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325162894, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325163015, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325163160, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325163257, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325163368, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325163427, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1758795325163538, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325163616, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1758795325163676, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325163782, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325163881, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1758795325163967, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325164067, "dur": 1572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325165640, "dur": 1564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325167204, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325168701, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325169882, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325171434, "dur": 1451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325172886, "dur": 1724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325174610, "dur": 1588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325176198, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325176839, "dur": 1273, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@287dd3fa176c/Editor/Drawing/Views/GraphEditorView.cs"}}, {"pid": 12345, "tid": 10, "ts": 1758795325176839, "dur": 2842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325179681, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325180744, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325181790, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325182903, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325183954, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325185075, "dur": 1384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325186459, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325187737, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325188812, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325189936, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325191180, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325192257, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325193435, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325194700, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325195763, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325196898, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325197966, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325199076, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325200167, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325201294, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325202529, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325203716, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325204948, "dur": 1779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325206777, "dur": 2427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758795325209205, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325209414, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_66DE4C1C9A03AD08.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325209510, "dur": 1655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325211209, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325211681, "dur": 929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758795325212610, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325212805, "dur": 10146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758795325222952, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325223180, "dur": 1520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758795325224701, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325224843, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795325225557, "dur": 1404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758795325226961, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325227068, "dur": 113824, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325340895, "dur": 3199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1758795325344095, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325344231, "dur": 5487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1758795325349718, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325349789, "dur": 4920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1758795325354710, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325354804, "dur": 3973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1758795325358777, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325358833, "dur": 4813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1758795325363646, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325363754, "dur": 6418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1758795325370241, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795325371013, "dur": 383149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758795325762653, "dur": 941, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 37977, "tid": 647, "ts": 1758795325777210, "dur": 1497, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 37977, "tid": 647, "ts": 1758795325778744, "dur": 1437, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 37977, "tid": 647, "ts": 1758795325773870, "dur": 6852, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}