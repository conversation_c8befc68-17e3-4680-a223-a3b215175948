{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 37977, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 37977, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 37977, "tid": 633, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 37977, "tid": 633, "ts": 1758795206420150, "dur": 260, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 37977, "tid": 633, "ts": 1758795206420434, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 37977, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 37977, "tid": 1, "ts": 1758795206166915, "dur": 6207, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 37977, "tid": 1, "ts": 1758795206173127, "dur": 47276, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 37977, "tid": 1, "ts": 1758795206220405, "dur": 32190, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 37977, "tid": 633, "ts": 1758795206420439, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 37977, "tid": 38654705664, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206166845, "dur": 9491, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206176338, "dur": 242736, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206176351, "dur": 251, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206176623, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206176626, "dur": 490, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206177121, "dur": 13, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206177136, "dur": 15801, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206192964, "dur": 4, "ph": "X", "name": "ProcessMessages 2484", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206192974, "dur": 33, "ph": "X", "name": "ReadAsync 2484", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193097, "dur": 2, "ph": "X", "name": "ProcessMessages 1958", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193100, "dur": 27, "ph": "X", "name": "ReadAsync 1958", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193132, "dur": 2, "ph": "X", "name": "ProcessMessages 2261", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193149, "dur": 21, "ph": "X", "name": "ReadAsync 2261", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193176, "dur": 1, "ph": "X", "name": "ProcessMessages 1163", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193181, "dur": 42, "ph": "X", "name": "ReadAsync 1163", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193226, "dur": 1, "ph": "X", "name": "ProcessMessages 1262", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193228, "dur": 45, "ph": "X", "name": "ReadAsync 1262", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193287, "dur": 3, "ph": "X", "name": "ProcessMessages 1143", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193292, "dur": 23, "ph": "X", "name": "ReadAsync 1143", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193318, "dur": 1, "ph": "X", "name": "ProcessMessages 1273", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193320, "dur": 18, "ph": "X", "name": "ReadAsync 1273", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193346, "dur": 23, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193371, "dur": 35, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193407, "dur": 1, "ph": "X", "name": "ProcessMessages 1394", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193410, "dur": 26, "ph": "X", "name": "ReadAsync 1394", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193438, "dur": 1, "ph": "X", "name": "ProcessMessages 1104", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193439, "dur": 23, "ph": "X", "name": "ReadAsync 1104", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193464, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193465, "dur": 23, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193489, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193491, "dur": 35, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193527, "dur": 1, "ph": "X", "name": "ProcessMessages 1013", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193530, "dur": 34, "ph": "X", "name": "ReadAsync 1013", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193565, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193567, "dur": 32, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193601, "dur": 1, "ph": "X", "name": "ProcessMessages 1167", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193607, "dur": 31, "ph": "X", "name": "ReadAsync 1167", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193640, "dur": 1, "ph": "X", "name": "ProcessMessages 1041", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193644, "dur": 19, "ph": "X", "name": "ReadAsync 1041", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193665, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193667, "dur": 29, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193699, "dur": 44, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193746, "dur": 2, "ph": "X", "name": "ProcessMessages 1167", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193754, "dur": 69, "ph": "X", "name": "ReadAsync 1167", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193825, "dur": 2, "ph": "X", "name": "ProcessMessages 1397", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193827, "dur": 23, "ph": "X", "name": "ReadAsync 1397", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193852, "dur": 1, "ph": "X", "name": "ProcessMessages 1404", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193854, "dur": 26, "ph": "X", "name": "ReadAsync 1404", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193887, "dur": 1, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193889, "dur": 21, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193911, "dur": 1, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193913, "dur": 35, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193950, "dur": 1, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193951, "dur": 20, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193974, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206193976, "dur": 28, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194006, "dur": 1, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194007, "dur": 21, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194048, "dur": 31, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194080, "dur": 1, "ph": "X", "name": "ProcessMessages 1308", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194082, "dur": 20, "ph": "X", "name": "ReadAsync 1308", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194107, "dur": 1, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194112, "dur": 41, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194154, "dur": 1, "ph": "X", "name": "ProcessMessages 892", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194155, "dur": 25, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194183, "dur": 1, "ph": "X", "name": "ProcessMessages 1281", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194186, "dur": 30, "ph": "X", "name": "ReadAsync 1281", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194217, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194219, "dur": 30, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194249, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194261, "dur": 24, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194290, "dur": 1, "ph": "X", "name": "ProcessMessages 1036", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194292, "dur": 31, "ph": "X", "name": "ReadAsync 1036", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194324, "dur": 1, "ph": "X", "name": "ProcessMessages 1154", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194326, "dur": 24, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194351, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194354, "dur": 50, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194407, "dur": 3, "ph": "X", "name": "ProcessMessages 1252", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194413, "dur": 44, "ph": "X", "name": "ReadAsync 1252", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194464, "dur": 1, "ph": "X", "name": "ProcessMessages 919", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194467, "dur": 29, "ph": "X", "name": "ReadAsync 919", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194497, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194499, "dur": 30, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194530, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194532, "dur": 218, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194751, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194753, "dur": 201, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194956, "dur": 1, "ph": "X", "name": "ProcessMessages 892", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206194958, "dur": 58, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195019, "dur": 2, "ph": "X", "name": "ProcessMessages 1226", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195022, "dur": 41, "ph": "X", "name": "ReadAsync 1226", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195064, "dur": 1, "ph": "X", "name": "ProcessMessages 1064", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195066, "dur": 35, "ph": "X", "name": "ReadAsync 1064", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195103, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195110, "dur": 26, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195157, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195159, "dur": 177, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195338, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195341, "dur": 43, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195390, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195393, "dur": 229, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195628, "dur": 1, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195632, "dur": 90, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195724, "dur": 2, "ph": "X", "name": "ProcessMessages 1058", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195728, "dur": 163, "ph": "X", "name": "ReadAsync 1058", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195893, "dur": 3, "ph": "X", "name": "ProcessMessages 1451", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195925, "dur": 24, "ph": "X", "name": "ReadAsync 1451", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195950, "dur": 1, "ph": "X", "name": "ProcessMessages 1262", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206195953, "dur": 70, "ph": "X", "name": "ReadAsync 1262", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196027, "dur": 2, "ph": "X", "name": "ProcessMessages 1632", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196031, "dur": 93, "ph": "X", "name": "ReadAsync 1632", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196126, "dur": 2, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196129, "dur": 28, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196180, "dur": 3, "ph": "X", "name": "ProcessMessages 1663", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196186, "dur": 45, "ph": "X", "name": "ReadAsync 1663", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196245, "dur": 2, "ph": "X", "name": "ProcessMessages 1629", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196249, "dur": 71, "ph": "X", "name": "ReadAsync 1629", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196341, "dur": 2, "ph": "X", "name": "ProcessMessages 1426", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196352, "dur": 303, "ph": "X", "name": "ReadAsync 1426", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196658, "dur": 2, "ph": "X", "name": "ProcessMessages 1178", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196662, "dur": 116, "ph": "X", "name": "ReadAsync 1178", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196781, "dur": 5, "ph": "X", "name": "ProcessMessages 2588", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196787, "dur": 77, "ph": "X", "name": "ReadAsync 2588", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196875, "dur": 2, "ph": "X", "name": "ProcessMessages 1940", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196879, "dur": 29, "ph": "X", "name": "ReadAsync 1940", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196915, "dur": 4, "ph": "X", "name": "ProcessMessages 926", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196920, "dur": 57, "ph": "X", "name": "ReadAsync 926", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196980, "dur": 2, "ph": "X", "name": "ProcessMessages 1440", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206196983, "dur": 26, "ph": "X", "name": "ReadAsync 1440", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197011, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197014, "dur": 44, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197061, "dur": 2, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197068, "dur": 49, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197119, "dur": 11, "ph": "X", "name": "ProcessMessages 946", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197131, "dur": 44, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197177, "dur": 2, "ph": "X", "name": "ProcessMessages 1732", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197180, "dur": 41, "ph": "X", "name": "ReadAsync 1732", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197222, "dur": 5, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197232, "dur": 57, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197291, "dur": 2, "ph": "X", "name": "ProcessMessages 1035", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197304, "dur": 50, "ph": "X", "name": "ReadAsync 1035", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197356, "dur": 3, "ph": "X", "name": "ProcessMessages 1759", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197361, "dur": 56, "ph": "X", "name": "ReadAsync 1759", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197419, "dur": 2, "ph": "X", "name": "ProcessMessages 1416", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197423, "dur": 53, "ph": "X", "name": "ReadAsync 1416", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197477, "dur": 1, "ph": "X", "name": "ProcessMessages 915", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197479, "dur": 92, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197574, "dur": 1, "ph": "X", "name": "ProcessMessages 1069", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197584, "dur": 47, "ph": "X", "name": "ReadAsync 1069", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197632, "dur": 1, "ph": "X", "name": "ProcessMessages 987", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197657, "dur": 24, "ph": "X", "name": "ReadAsync 987", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197683, "dur": 4, "ph": "X", "name": "ProcessMessages 1713", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197688, "dur": 33, "ph": "X", "name": "ReadAsync 1713", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197742, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197746, "dur": 34, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197783, "dur": 1, "ph": "X", "name": "ProcessMessages 1498", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197787, "dur": 23, "ph": "X", "name": "ReadAsync 1498", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197813, "dur": 30, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197846, "dur": 1, "ph": "X", "name": "ProcessMessages 999", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197849, "dur": 62, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197913, "dur": 1, "ph": "X", "name": "ProcessMessages 996", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197915, "dur": 35, "ph": "X", "name": "ReadAsync 996", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197965, "dur": 1, "ph": "X", "name": "ProcessMessages 1567", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197967, "dur": 24, "ph": "X", "name": "ReadAsync 1567", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197993, "dur": 1, "ph": "X", "name": "ProcessMessages 931", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206197995, "dur": 28, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198028, "dur": 28, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198059, "dur": 1, "ph": "X", "name": "ProcessMessages 1011", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198062, "dur": 24, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198088, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198101, "dur": 45, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198148, "dur": 3, "ph": "X", "name": "ProcessMessages 1462", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198153, "dur": 53, "ph": "X", "name": "ReadAsync 1462", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198208, "dur": 2, "ph": "X", "name": "ProcessMessages 1520", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198210, "dur": 31, "ph": "X", "name": "ReadAsync 1520", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198242, "dur": 3, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198246, "dur": 36, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198284, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198286, "dur": 38, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198327, "dur": 4, "ph": "X", "name": "ProcessMessages 1163", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198332, "dur": 35, "ph": "X", "name": "ReadAsync 1163", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198372, "dur": 2, "ph": "X", "name": "ProcessMessages 1070", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198375, "dur": 29, "ph": "X", "name": "ReadAsync 1070", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198408, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198410, "dur": 31, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198443, "dur": 1, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198445, "dur": 37, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198484, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198492, "dur": 38, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198531, "dur": 1, "ph": "X", "name": "ProcessMessages 1400", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198534, "dur": 41, "ph": "X", "name": "ReadAsync 1400", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198578, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198581, "dur": 38, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198620, "dur": 1, "ph": "X", "name": "ProcessMessages 1213", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198622, "dur": 38, "ph": "X", "name": "ReadAsync 1213", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198662, "dur": 3, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198666, "dur": 44, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198713, "dur": 1, "ph": "X", "name": "ProcessMessages 981", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198715, "dur": 52, "ph": "X", "name": "ReadAsync 981", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198769, "dur": 1, "ph": "X", "name": "ProcessMessages 1000", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198771, "dur": 25, "ph": "X", "name": "ReadAsync 1000", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198800, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198802, "dur": 45, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198850, "dur": 2, "ph": "X", "name": "ProcessMessages 1036", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198855, "dur": 54, "ph": "X", "name": "ReadAsync 1036", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198914, "dur": 2, "ph": "X", "name": "ProcessMessages 1389", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198918, "dur": 45, "ph": "X", "name": "ReadAsync 1389", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198965, "dur": 1, "ph": "X", "name": "ProcessMessages 1329", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206198967, "dur": 64, "ph": "X", "name": "ReadAsync 1329", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199033, "dur": 2, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199035, "dur": 50, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199088, "dur": 2, "ph": "X", "name": "ProcessMessages 1705", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199100, "dur": 51, "ph": "X", "name": "ReadAsync 1705", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199158, "dur": 2, "ph": "X", "name": "ProcessMessages 1265", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199168, "dur": 39, "ph": "X", "name": "ReadAsync 1265", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199208, "dur": 1, "ph": "X", "name": "ProcessMessages 1619", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199211, "dur": 35, "ph": "X", "name": "ReadAsync 1619", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199252, "dur": 2, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199255, "dur": 38, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199295, "dur": 2, "ph": "X", "name": "ProcessMessages 1359", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199298, "dur": 27, "ph": "X", "name": "ReadAsync 1359", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199327, "dur": 1, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199329, "dur": 40, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199370, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199372, "dur": 31, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199407, "dur": 1, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199409, "dur": 55, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199465, "dur": 1, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199468, "dur": 44, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199526, "dur": 1, "ph": "X", "name": "ProcessMessages 1406", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199531, "dur": 24, "ph": "X", "name": "ReadAsync 1406", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199556, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199558, "dur": 30, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199593, "dur": 1, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199596, "dur": 40, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199650, "dur": 1, "ph": "X", "name": "ProcessMessages 1150", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199653, "dur": 27, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199691, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199693, "dur": 65, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199760, "dur": 1, "ph": "X", "name": "ProcessMessages 1546", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199762, "dur": 43, "ph": "X", "name": "ReadAsync 1546", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199807, "dur": 2, "ph": "X", "name": "ProcessMessages 1013", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199811, "dur": 44, "ph": "X", "name": "ReadAsync 1013", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199858, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199892, "dur": 35, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199929, "dur": 2, "ph": "X", "name": "ProcessMessages 2003", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199932, "dur": 36, "ph": "X", "name": "ReadAsync 2003", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199971, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206199975, "dur": 40, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200017, "dur": 1, "ph": "X", "name": "ProcessMessages 1643", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200019, "dur": 28, "ph": "X", "name": "ReadAsync 1643", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200048, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200050, "dur": 28, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200080, "dur": 1, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200081, "dur": 38, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200122, "dur": 2, "ph": "X", "name": "ProcessMessages 1001", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200129, "dur": 46, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200178, "dur": 2, "ph": "X", "name": "ProcessMessages 1267", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200182, "dur": 42, "ph": "X", "name": "ReadAsync 1267", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200225, "dur": 2, "ph": "X", "name": "ProcessMessages 1501", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200230, "dur": 89, "ph": "X", "name": "ReadAsync 1501", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200323, "dur": 2, "ph": "X", "name": "ProcessMessages 1588", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200326, "dur": 32, "ph": "X", "name": "ReadAsync 1588", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200362, "dur": 9, "ph": "X", "name": "ProcessMessages 1349", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200384, "dur": 48, "ph": "X", "name": "ReadAsync 1349", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200437, "dur": 1, "ph": "X", "name": "ProcessMessages 1102", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200447, "dur": 32, "ph": "X", "name": "ReadAsync 1102", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200481, "dur": 1, "ph": "X", "name": "ProcessMessages 1176", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200483, "dur": 36, "ph": "X", "name": "ReadAsync 1176", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200550, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200552, "dur": 29, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200584, "dur": 2, "ph": "X", "name": "ProcessMessages 1192", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200589, "dur": 50, "ph": "X", "name": "ReadAsync 1192", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200641, "dur": 2, "ph": "X", "name": "ProcessMessages 999", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200644, "dur": 43, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200688, "dur": 1, "ph": "X", "name": "ProcessMessages 1021", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200691, "dur": 40, "ph": "X", "name": "ReadAsync 1021", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200734, "dur": 1, "ph": "X", "name": "ProcessMessages 1479", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200736, "dur": 89, "ph": "X", "name": "ReadAsync 1479", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200831, "dur": 3, "ph": "X", "name": "ProcessMessages 2186", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200837, "dur": 39, "ph": "X", "name": "ReadAsync 2186", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200878, "dur": 1, "ph": "X", "name": "ProcessMessages 1005", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200885, "dur": 38, "ph": "X", "name": "ReadAsync 1005", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200924, "dur": 1, "ph": "X", "name": "ProcessMessages 1220", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200940, "dur": 25, "ph": "X", "name": "ReadAsync 1220", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200968, "dur": 2, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206200974, "dur": 45, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206201107, "dur": 2, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206201111, "dur": 48, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206201166, "dur": 2, "ph": "X", "name": "ProcessMessages 882", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206201170, "dur": 133, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206201319, "dur": 2, "ph": "X", "name": "ProcessMessages 1681", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206201322, "dur": 45, "ph": "X", "name": "ReadAsync 1681", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206201369, "dur": 1, "ph": "X", "name": "ProcessMessages 1501", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206201372, "dur": 187, "ph": "X", "name": "ReadAsync 1501", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206201560, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206201571, "dur": 26, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206201603, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206201653, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206201655, "dur": 213, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206201874, "dur": 34, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206201912, "dur": 148, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206202062, "dur": 332, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206202397, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206202401, "dur": 25, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206202439, "dur": 311, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206202751, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206202772, "dur": 257, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206203032, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206203035, "dur": 36, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206203072, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206203074, "dur": 155, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206203232, "dur": 803, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206204050, "dur": 22, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206204074, "dur": 2, "ph": "X", "name": "ProcessMessages 2117", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206204086, "dur": 117, "ph": "X", "name": "ReadAsync 2117", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206204207, "dur": 342, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206204552, "dur": 4, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206204557, "dur": 67, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206204625, "dur": 2, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206204628, "dur": 142, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206204773, "dur": 26, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206204801, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206204803, "dur": 189, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206204997, "dur": 26, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206205028, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206205031, "dur": 34, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206205067, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206205069, "dur": 148, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206205221, "dur": 65, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206205288, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206205290, "dur": 203, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206205496, "dur": 1, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206205498, "dur": 277, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206205782, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206205783, "dur": 22, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206205808, "dur": 198, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206206007, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206206018, "dur": 24, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206206045, "dur": 237, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206206291, "dur": 45, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206206338, "dur": 139, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206206482, "dur": 4, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206206486, "dur": 60, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206206549, "dur": 2, "ph": "X", "name": "ProcessMessages 1047", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206206553, "dur": 75, "ph": "X", "name": "ReadAsync 1047", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206206631, "dur": 217, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206206849, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206206850, "dur": 199, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206207072, "dur": 1, "ph": "X", "name": "ProcessMessages 1170", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206207074, "dur": 29, "ph": "X", "name": "ReadAsync 1170", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206207105, "dur": 1, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206207109, "dur": 66, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206207185, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206207187, "dur": 182, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206207378, "dur": 33, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206207414, "dur": 1, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206207416, "dur": 39, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206207457, "dur": 1, "ph": "X", "name": "ProcessMessages 974", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206207460, "dur": 289, "ph": "X", "name": "ReadAsync 974", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206207750, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206207753, "dur": 178, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206207934, "dur": 113, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206208049, "dur": 2, "ph": "X", "name": "ProcessMessages 2382", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206208052, "dur": 74, "ph": "X", "name": "ReadAsync 2382", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206208128, "dur": 317, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206208447, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206208449, "dur": 19, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206208494, "dur": 269, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206208764, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206208766, "dur": 23, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206208791, "dur": 21, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206208821, "dur": 223, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209048, "dur": 83, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209154, "dur": 119, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209278, "dur": 2, "ph": "X", "name": "ProcessMessages 2213", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209281, "dur": 60, "ph": "X", "name": "ReadAsync 2213", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209342, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209344, "dur": 149, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209495, "dur": 1, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209497, "dur": 32, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209531, "dur": 2, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209536, "dur": 35, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209572, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209574, "dur": 82, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209659, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209660, "dur": 173, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209834, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206209836, "dur": 179, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210018, "dur": 31, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210056, "dur": 2, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210059, "dur": 36, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210099, "dur": 221, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210322, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210324, "dur": 32, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210358, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210361, "dur": 209, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210575, "dur": 1, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210577, "dur": 29, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210609, "dur": 168, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210779, "dur": 1, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210781, "dur": 31, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210818, "dur": 176, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210995, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206210996, "dur": 27, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206211030, "dur": 177, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206211208, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206211214, "dur": 32, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206211267, "dur": 141, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206211412, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206211423, "dur": 202, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206211629, "dur": 27, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206211659, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206211661, "dur": 39, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206211703, "dur": 1, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206211705, "dur": 258, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206211964, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206211966, "dur": 85, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206212056, "dur": 66, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206212125, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206212126, "dur": 197, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206212325, "dur": 1, "ph": "X", "name": "ProcessMessages 929", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206212329, "dur": 178, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206212512, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206212514, "dur": 33, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206212551, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206212554, "dur": 154, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206212711, "dur": 68, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206212780, "dur": 1, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206212783, "dur": 309, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206213093, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206213096, "dur": 33, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206213131, "dur": 271, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206213404, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206213405, "dur": 41, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206213449, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206213454, "dur": 244, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206213699, "dur": 1, "ph": "X", "name": "ProcessMessages 968", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206213701, "dur": 179, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206213882, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206213884, "dur": 51, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206213935, "dur": 2, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206213939, "dur": 167, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206214107, "dur": 1, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206214109, "dur": 299, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206214409, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206214411, "dur": 34, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206214449, "dur": 189, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206214639, "dur": 1, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206214641, "dur": 201, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206214846, "dur": 71, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206214922, "dur": 153, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206215076, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206215079, "dur": 173, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206215255, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206215258, "dur": 161, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206215423, "dur": 2, "ph": "X", "name": "ProcessMessages 984", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206215426, "dur": 48, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206215477, "dur": 1, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206215479, "dur": 33, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206215515, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206215518, "dur": 299, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206215819, "dur": 1, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206215821, "dur": 39, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206215861, "dur": 1, "ph": "X", "name": "ProcessMessages 996", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206215864, "dur": 36, "ph": "X", "name": "ReadAsync 996", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206215903, "dur": 253, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206216161, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206216163, "dur": 26, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206216212, "dur": 1, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206216219, "dur": 18, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206216240, "dur": 1, "ph": "X", "name": "ProcessMessages 969", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206216244, "dur": 230, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206216480, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206216484, "dur": 184, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206216671, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206216676, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206216753, "dur": 175, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206216932, "dur": 38, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206216974, "dur": 5, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217023, "dur": 67, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217095, "dur": 6, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217102, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217154, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217160, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217201, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217222, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217281, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217406, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217445, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217446, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217537, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217544, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217592, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217627, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217689, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217691, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217731, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217733, "dur": 49, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217794, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217808, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217905, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217907, "dur": 67, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217975, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206217977, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218019, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218024, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218070, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218103, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218151, "dur": 54, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218208, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218280, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218285, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218353, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218356, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218398, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218400, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218476, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218513, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218590, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218636, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218694, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218703, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218727, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218729, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218816, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218822, "dur": 72, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218904, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218906, "dur": 72, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206218981, "dur": 59, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219042, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219046, "dur": 135, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219183, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219227, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219230, "dur": 37, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219269, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219283, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219323, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219343, "dur": 78, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219424, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219426, "dur": 58, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219487, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219523, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219525, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219618, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219620, "dur": 106, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219728, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219730, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219780, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219783, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219865, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219905, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219907, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219951, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206219954, "dur": 73, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220029, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220031, "dur": 117, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220151, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220154, "dur": 147, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220302, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220305, "dur": 103, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220409, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220411, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220455, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220465, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220532, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220534, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220579, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220581, "dur": 203, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220787, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220790, "dur": 53, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220844, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220846, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220882, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220884, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206220967, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221010, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221015, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221067, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221069, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221113, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221160, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221162, "dur": 48, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221212, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221215, "dur": 80, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221296, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221298, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221343, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221346, "dur": 67, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221419, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221424, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221470, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221519, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221521, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221556, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221557, "dur": 84, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221643, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221644, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221692, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221738, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221740, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221784, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221786, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221827, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221832, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221865, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221937, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221938, "dur": 34, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206221974, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222022, "dur": 40, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222065, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222103, "dur": 108, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222213, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222215, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222260, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222262, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222296, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222298, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222334, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222371, "dur": 56, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222436, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222439, "dur": 48, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222488, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222490, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222536, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222578, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222580, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222614, "dur": 50, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222687, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222807, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222809, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222856, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222873, "dur": 43, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222919, "dur": 39, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206222999, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223001, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223048, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223050, "dur": 52, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223104, "dur": 85, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223191, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223193, "dur": 39, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223235, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223293, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223296, "dur": 68, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223366, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223368, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223408, "dur": 10, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223420, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223464, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223466, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223507, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223551, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223553, "dur": 37, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223592, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223594, "dur": 86, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223691, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223693, "dur": 51, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223747, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223790, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223792, "dur": 38, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206223835, "dur": 13770, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206237608, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206237609, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206237793, "dur": 2617, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206240431, "dur": 182, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206240619, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206240725, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206240727, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206240863, "dur": 3378, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206244252, "dur": 21407, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206265665, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206265669, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206265741, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206265743, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206265770, "dur": 402, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206266175, "dur": 606, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206266784, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206266788, "dur": 90, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206266882, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206266941, "dur": 205, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206267149, "dur": 671, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206267822, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206267824, "dur": 611, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206268438, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206268440, "dur": 67, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206268512, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206268514, "dur": 284, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206268802, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206268804, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206268872, "dur": 56, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206268931, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206269036, "dur": 150, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206269190, "dur": 281, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206269741, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206269743, "dur": 62, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206269807, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206269809, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206269890, "dur": 251, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206270143, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206270145, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206270217, "dur": 301, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206270527, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206270578, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206270794, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206270797, "dur": 344, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206271144, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206271195, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206271317, "dur": 218, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206271537, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206271538, "dur": 364, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206271904, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206271906, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206271964, "dur": 177, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206272143, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206272286, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206272444, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206272446, "dur": 356, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206272805, "dur": 213, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206273032, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206273087, "dur": 306, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206273396, "dur": 152, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206273551, "dur": 172, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206273725, "dur": 284, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206274012, "dur": 262, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206274276, "dur": 139, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206274418, "dur": 266, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206274686, "dur": 209, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206274897, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206274898, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206274951, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206275041, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206275042, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206275088, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206275210, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206275262, "dur": 535, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206275798, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206275800, "dur": 285, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206276088, "dur": 114, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206276206, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206276252, "dur": 4026, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206280282, "dur": 15, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206280298, "dur": 28, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206280328, "dur": 197, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206280528, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206280535, "dur": 213, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206280749, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206280751, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206280795, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206280797, "dur": 300, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206281100, "dur": 252, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206281355, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206281433, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206281477, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206281552, "dur": 306, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206281863, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206281900, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206281901, "dur": 108, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206282011, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206282013, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206282143, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206282144, "dur": 141, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206282288, "dur": 509, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206282805, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206282929, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206282972, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206282973, "dur": 221, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206283197, "dur": 236, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206283435, "dur": 237, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206283675, "dur": 95043, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206378725, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206378728, "dur": 55, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206378793, "dur": 49, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206378845, "dur": 41, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206379101, "dur": 31, "ph": "X", "name": "ReadAsync 4557", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206379135, "dur": 23, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206379161, "dur": 29, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206379192, "dur": 30, "ph": "X", "name": "ProcessMessages 6840", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206379223, "dur": 5249, "ph": "X", "name": "ReadAsync 6840", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206384475, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206384477, "dur": 211, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206384691, "dur": 199, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206384894, "dur": 910, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206385806, "dur": 680, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206386491, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206386494, "dur": 304, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206386802, "dur": 143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206386948, "dur": 344, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206387295, "dur": 2060, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206389373, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206389375, "dur": 351, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206389729, "dur": 916, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206390726, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206390812, "dur": 669, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206391485, "dur": 836, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206392324, "dur": 780, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206393109, "dur": 308, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206393420, "dur": 881, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206394303, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206394305, "dur": 990, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206395298, "dur": 279, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206395580, "dur": 492, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206396075, "dur": 1093, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206397171, "dur": 408, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206397583, "dur": 277, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206397864, "dur": 320, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206398187, "dur": 469, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206398664, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206398670, "dur": 1441, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206400122, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206400124, "dur": 505, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206400632, "dur": 1313, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206401948, "dur": 165, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206402118, "dur": 370, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206402491, "dur": 1740, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206404233, "dur": 646, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206404882, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206404884, "dur": 1337, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206406225, "dur": 330, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206406558, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206406720, "dur": 50, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206406774, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206406822, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206406885, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206406932, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206406982, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407032, "dur": 184, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407219, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407317, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407384, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407385, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407493, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407494, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407591, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407593, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407630, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407724, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407726, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407768, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407874, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407875, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206407930, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408043, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408044, "dur": 71, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408119, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408174, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408215, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408251, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408315, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408379, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408453, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408520, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408522, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408568, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408569, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408606, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408652, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408654, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408749, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408751, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408794, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408849, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408891, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408939, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206408991, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409093, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409130, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409178, "dur": 111, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409293, "dur": 116, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409427, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409429, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409484, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409488, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409531, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409575, "dur": 8, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409585, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409646, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409707, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409803, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409846, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206409940, "dur": 98, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206410040, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206410103, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206410105, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206410147, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206410231, "dur": 332, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206410567, "dur": 757, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206411326, "dur": 96, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206411425, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206411590, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206411596, "dur": 102, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206411700, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206411702, "dur": 253, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206411957, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206411959, "dur": 91, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206412052, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206412054, "dur": 285, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 37977, "tid": 38654705664, "ts": 1758795206412342, "dur": 6725, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 37977, "tid": 633, "ts": 1758795206420448, "dur": 1510, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 37977, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 37977, "tid": 34359738368, "ts": 1758795206165353, "dur": 87252, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 37977, "tid": 34359738368, "ts": 1758795206252608, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 37977, "tid": 34359738368, "ts": 1758795206252610, "dur": 67, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 37977, "tid": 633, "ts": 1758795206421965, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 37977, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 37977, "tid": 30064771072, "ts": 1758795206144050, "dur": 275258, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 37977, "tid": 30064771072, "ts": 1758795206144293, "dur": 20868, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 37977, "tid": 30064771072, "ts": 1758795206419312, "dur": 111, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 37977, "tid": 30064771072, "ts": 1758795206419322, "dur": 58, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 37977, "tid": 30064771072, "ts": 1758795206419425, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 37977, "tid": 633, "ts": 1758795206421973, "dur": 15, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1758795206176442, "dur": 1697, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758795206178144, "dur": 14852, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758795206193055, "dur": 52, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1758795206193108, "dur": 105, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758795206193219, "dur": 23621, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758795206216849, "dur": 195618, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758795206412664, "dur": 3587, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1758795206193147, "dur": 23709, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206216861, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1758795206217033, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206217148, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_AD0F59CECD9086B5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206217273, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206217344, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_72E6AB956861F353.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206217479, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206217575, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_6BADCFBF033360D0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206217753, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206217852, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CF5F61BCAC5D1F90.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206218014, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206218111, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F56BB6A2C38EBCEC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206218290, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206218374, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_8B47C372A80AB1D5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206218541, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206218642, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_2F7C47F2C923EECA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206218793, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206218904, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_7E945FCC8473B7C7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206219070, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206219155, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3260E1D96E1E22C0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206219297, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206219409, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_5F70C3E0135C03E6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206219538, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206219615, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5BA053E42555A2B5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206219742, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206219827, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1758795206219995, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206220075, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206220199, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206220285, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1758795206220430, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206220535, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206220619, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1758795206220816, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206220904, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1758795206221115, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206221182, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206221333, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1758795206221462, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206221545, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206221656, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1758795206221800, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206221885, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206222024, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206222158, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206222221, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1758795206222346, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206222436, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206222497, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206222586, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206222675, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206222769, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206222860, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206222971, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206223101, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206223178, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206223270, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206223376, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206223503, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206223606, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206223687, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206223787, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206223884, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206223988, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206224102, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206225390, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206226622, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206227823, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206228956, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206230024, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206231192, "dur": 1391, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206232583, "dur": 1374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206233957, "dur": 1438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206235395, "dur": 1407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206236802, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206238528, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206239990, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206241325, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206242259, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206243318, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206244425, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206245513, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206246649, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206247767, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206249022, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206250194, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206251467, "dur": 1371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206252839, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206254268, "dur": 1564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206255833, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206257103, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206258188, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206259321, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206260454, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206261390, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206262350, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206263400, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206264570, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206265187, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206265515, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206265887, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206266047, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206266569, "dur": 941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206267510, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206267563, "dur": 1770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758795206269334, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206269612, "dur": 857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206270506, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206270921, "dur": 2240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758795206273161, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206273511, "dur": 1485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758795206274996, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206275453, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Editor.ref.dll_2C1A528DE6C1BDE8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206275550, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206275653, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206276142, "dur": 425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206276606, "dur": 1982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758795206278588, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206278738, "dur": 1965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206280704, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206280831, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758795206281472, "dur": 1691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206283168, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758795206283358, "dur": 99442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206382804, "dur": 4451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1758795206387256, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206387346, "dur": 6117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1758795206393463, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206393549, "dur": 4338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1758795206397887, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206397993, "dur": 4815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1758795206402810, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206402940, "dur": 7879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1758795206410896, "dur": 1078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758795206412029, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206193154, "dur": 23710, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206216868, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1758795206217044, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206217159, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_A1002E9E9F083797.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206217273, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206217333, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_FF1638973A990706.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206217478, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206217562, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_7D46FF134A659955.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206217732, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206217835, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_8FC1791ACEE3E790.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206217974, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206218091, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_C0AB20643B6FF208.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206218259, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206218352, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_968EEE9C3279D1EC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206218496, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206218635, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_C69304A59A7A1B74.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206218794, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206218891, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_1879345459B1F65E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206219070, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206219163, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_21D14335F6F8B5A4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206219298, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206219424, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_D4462A08249AAAE8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206219555, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206219653, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_0E20F46DE1E1A65B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206219764, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206219874, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1758795206220027, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206220115, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206220996, "dur": 19591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758795206240587, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206240764, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_7423F2FBC6CF80C4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206240885, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206240971, "dur": 3565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206244589, "dur": 21346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758795206266027, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206266147, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758795206266522, "dur": 1048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206267606, "dur": 1085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758795206268692, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206268854, "dur": 1402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206270257, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206270349, "dur": 1086, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758795206271436, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206271544, "dur": 1278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758795206272822, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206273003, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758795206273582, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758795206274718, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206274846, "dur": 2691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1758795206277818, "dur": 1231, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206378956, "dur": 607, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206279354, "dur": 100229, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1758795206380772, "dur": 3636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1758795206384409, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206384517, "dur": 5118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1758795206389635, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206389735, "dur": 3897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1758795206393632, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206393986, "dur": 4919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1758795206398905, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206398996, "dur": 5803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1758795206404800, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206404896, "dur": 7050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1758795206411998, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758795206412052, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206193165, "dur": 23708, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206216876, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_320B820FE8FC5AE8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206216928, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206217007, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_C6011564DA201A52.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206217145, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206217242, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_A3723152F274B049.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206217365, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206217448, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_C49C0FC47CAFEAC5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206217588, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206217724, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_BB076BE8E91D3844.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206217871, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206217968, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_29F333B2BA915C4E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206218138, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206218250, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_E1CEC1A55253DF7A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206218396, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206218509, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_734ECF7F5E8D9BFB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206218680, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206218782, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_D53BC0BA2A1258D1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206218942, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206219045, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3CEA7E364D77D73C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206219183, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206219266, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_20CB703B61251BE5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206219421, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206219503, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_A49A776348DF5E4E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206219599, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206219682, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_9A3FDE96F0266F50.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206219809, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206219900, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758795206220279, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206220345, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_37F04A890A90A5DB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206220483, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206220588, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758795206220836, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206220925, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1758795206221240, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1758795206221346, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206221415, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1758795206221585, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206221683, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206221770, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206221839, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206221933, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206222039, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206222184, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206222287, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206222375, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758795206222577, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206222648, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206222746, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206222853, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206222963, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206223081, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206223160, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206223241, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206223357, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206223465, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206223890, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758795206224056, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206224159, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206225411, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206226646, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206227840, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206229003, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206230077, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206231267, "dur": 1414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206232681, "dur": 1358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206234039, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206235477, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206236877, "dur": 1735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206238612, "dur": 1439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206240052, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206241396, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206242321, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206243394, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206244476, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206245573, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206246677, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206247791, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206249019, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206250216, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206251472, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206252867, "dur": 1410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206254277, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206255817, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206257081, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206258144, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206259280, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206260409, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206261345, "dur": 954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206262299, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206263342, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206264471, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206265423, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206265888, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206266054, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206266563, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206266917, "dur": 2285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758795206269202, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206269390, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206269508, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206270213, "dur": 1649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758795206271862, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206272053, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206272261, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206272330, "dur": 2732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758795206275063, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206275189, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206275411, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206275610, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206276367, "dur": 1597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758795206277965, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206278084, "dur": 965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758795206279083, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758795206279850, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206279980, "dur": 3192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206283172, "dur": 97625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206380804, "dur": 4041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1758795206384845, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206384968, "dur": 5996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1758795206390965, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206391049, "dur": 5363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1758795206396414, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206396524, "dur": 4423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1758795206400947, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206401041, "dur": 3491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1758795206404532, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206404627, "dur": 7325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1758795206411952, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758795206412038, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206193172, "dur": 23711, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206216927, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206217021, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_3BE3DE4E4FF3C20F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206217168, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206217246, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_6C13B72F801586DB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206217370, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206217454, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_5AFB90E5B3938F6A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206217632, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206217733, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_05CCC7FD7CF0316D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206217871, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206217967, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_67D1F298EAAF8408.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206218137, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206218247, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_A83C1C8603B0CB24.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206218389, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206218484, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_463AABC76FBC4B88.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206218665, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206218770, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_CEEE8E02F8AB65CD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206218942, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206219053, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_2E74B195F350D70F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206219191, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206219282, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_E5C0679566569A75.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206219427, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206219521, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_4EB5407DF9A5D812.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206219622, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206219716, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_26633F92B9A939A2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206219847, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206219980, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206220048, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2A5CE0F0450FED37.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206220198, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206220274, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1758795206220420, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206220511, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206221056, "dur": 16617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758795206237673, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206237919, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_BDDAB6C3AC1C4D86.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206238017, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206238123, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206239654, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206240994, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206241116, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206241209, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206242173, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206243198, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206244315, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206245393, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206246514, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206247597, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206248812, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206250006, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206251248, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206252634, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206254305, "dur": 211, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 4, "ts": 1758795206254516, "dur": 1291, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 4, "ts": 1758795206255807, "dur": 681, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 4, "ts": 1758795206254033, "dur": 2456, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206256489, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206257616, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206258679, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206259843, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206260853, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206261804, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206262791, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206263910, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206264425, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206265703, "dur": 81, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206265784, "dur": 70, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206265895, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206266010, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206266597, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206267145, "dur": 2105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758795206269250, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206269392, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_16F8EA6B67022FB5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206269482, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206269577, "dur": 831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206270456, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206270800, "dur": 2411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758795206273212, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206273385, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206273896, "dur": 2263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758795206276160, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206276307, "dur": 1817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.Entities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758795206278124, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206278238, "dur": 867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206279142, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758795206279936, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206280047, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206280243, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758795206280988, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758795206281154, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758795206281775, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206281862, "dur": 1318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206283180, "dur": 97594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206380781, "dur": 3336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1758795206384118, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206384229, "dur": 5204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1758795206389434, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206389520, "dur": 5144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1758795206394664, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206394749, "dur": 5703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.PlasticSCM.Editor.Entities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1758795206400452, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206400553, "dur": 4152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1758795206404705, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206404922, "dur": 6894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1758795206411816, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758795206411916, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206193179, "dur": 23718, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206216900, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_ACDD54761A8A8AD1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206216972, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206217068, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_2338B6331E4A589D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206217203, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206217277, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_788EC0AD0DD1252A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206217401, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206217481, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_AE0AA47335F7F2B3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206217661, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206217768, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_BD28D25607AEE2E1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206217900, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206217997, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_C1844920F06A43FF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206218143, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206218271, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0CB46753FA7C312F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206218401, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206218542, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_D28966A0C9F1BC15.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206218696, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206218810, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_837685507179BFD9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206218969, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206219086, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_56E9249B00D2323D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206219219, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206219322, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11EA7240A47B953E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206219487, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206219554, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_2AA1A0FABCD57599.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206219671, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206219761, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_9663AEF5A1DB96B8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206219880, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206219960, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_6450FF118201D284.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206220053, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206220151, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206220280, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206220382, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1758795206220519, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206220603, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1758795206220846, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206220937, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1758795206221175, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206221244, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1758795206221397, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206221523, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1758795206221598, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206221712, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1758795206221954, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206222017, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206222079, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206222152, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206222213, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206222303, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206222407, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206222480, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206222596, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206222684, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206222785, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206222898, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206223004, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206223111, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1758795206223447, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206223560, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206223662, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206223758, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206223860, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206223956, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206224058, "dur": 1320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206225379, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206226624, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206227825, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206228857, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206229927, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206231082, "dur": 1378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206232460, "dur": 1377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206233838, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206235272, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206236694, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206238420, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206239919, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206241243, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206242193, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206243228, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206244366, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206245457, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206246559, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206247671, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206248899, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206250086, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206251317, "dur": 1364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206252681, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206254125, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206255702, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206256976, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206258061, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206259180, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206260309, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206261265, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206262234, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206263278, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206264438, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206265655, "dur": 106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206265762, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206265903, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206266035, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206266544, "dur": 1062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206267655, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758795206268465, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206268642, "dur": 977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206269672, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206270553, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206270869, "dur": 1630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758795206272499, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206272621, "dur": 1693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758795206274314, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206274465, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206274695, "dur": 2063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758795206276758, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206276973, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206277560, "dur": 1595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758795206279155, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206279285, "dur": 1716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206281002, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758795206281185, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758795206281767, "dur": 1397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206283164, "dur": 97667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206380835, "dur": 4005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1758795206384841, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206384934, "dur": 4702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1758795206389637, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206389770, "dur": 6127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1758795206395897, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206395982, "dur": 6336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1758795206402318, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206402392, "dur": 5455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1758795206407847, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206407957, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758795206408009, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206408114, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206408250, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758795206408411, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206408527, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758795206408580, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206408687, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206408750, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206408817, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206408940, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206409002, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206409068, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206409127, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206409190, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206409254, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206409448, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206409599, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206409791, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206409933, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206410115, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206410248, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206410409, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206410585, "dur": 1092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758795206411738, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206193187, "dur": 23718, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206216909, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_2C20B8F0E17E12AF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206216989, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206217091, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_621CCEC35B13C3FF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206217222, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206217292, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_646E6BF09BC34AF8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206217418, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206217497, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_7FB03B9F43028903.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206217663, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206217784, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7EB4E75F718193E6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206217904, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206218031, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4B36DE508E234CBA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206218199, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206218320, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_F2316A31D98809E8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206218458, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206218588, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EB7DEB5E390DBA80.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206218744, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206218852, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_FD04B64CA8B1F690.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206219028, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206219119, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_FFC4A5DF3CDD2051.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206219256, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206219375, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BB987306F50BED91.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206219516, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206219581, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_910BD9D1CE0E2BD1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206219707, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206219810, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_730EC33EED2E94C6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206219970, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_57F26D0ACBE8DD19.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206220093, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206220202, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206220292, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_927C166CA6DDB07E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206220412, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206220493, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206220577, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_283D5FC3BD6A67A2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206220656, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1758795206220814, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206220886, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1758795206221104, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758795206221308, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206221370, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206221480, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206221589, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206221704, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206221791, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206221874, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206221943, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206222015, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206222112, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206222169, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206222253, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206222319, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206222421, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206222520, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206222602, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206222702, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206222804, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206222914, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206223022, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206223134, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206223206, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206223315, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206223416, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206223545, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206223634, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206223729, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206223832, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206223936, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206224032, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206224145, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206225416, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206226650, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206227850, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206229016, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206230095, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206231253, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206232646, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206234022, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206235450, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206236846, "dur": 1781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206238627, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206240061, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206241410, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206242348, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206243411, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206244495, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206245620, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206246733, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206247865, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206249116, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206250327, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206251607, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206253031, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206254458, "dur": 1528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206255986, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206257215, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206258294, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206259444, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206260512, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206261459, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206262395, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206263466, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206264038, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206264908, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206265447, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206265659, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206265727, "dur": 90, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206265817, "dur": 66, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206265883, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206266050, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206266553, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206266898, "dur": 1923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758795206268821, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206269204, "dur": 900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206270149, "dur": 980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206271136, "dur": 1619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758795206272756, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206273038, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206273816, "dur": 2027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758795206275845, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206276030, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206276491, "dur": 1726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758795206278217, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206278361, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206279174, "dur": 972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758795206280209, "dur": 2946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206283160, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758795206283328, "dur": 97878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206381348, "dur": 5441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1758795206386789, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206386890, "dur": 4962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1758795206391852, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206391937, "dur": 6240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1758795206398177, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206398265, "dur": 3567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1758795206401832, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206401918, "dur": 4947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1758795206406866, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206406990, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206407052, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206407386, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206407447, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206407637, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206407851, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206407923, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206408000, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206408121, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206408317, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206408515, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1758795206408574, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206408695, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206408827, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206408948, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206409076, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206409200, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206409376, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206409494, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206409604, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206409713, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206409860, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206410046, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206410169, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206410239, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1758795206410293, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206410500, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206410605, "dur": 1281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758795206411928, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206193194, "dur": 23720, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206216918, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_641E8BB24D9C991B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206216986, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206217080, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_139664EF1148BEA3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206217222, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206217302, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_CEAF7E5E7A4F62D9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206217425, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206217533, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_14EB62D6C267D167.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206217706, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206217837, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D7A9AF8C5E67A0AE.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206217984, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206218095, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_05C8CFED34CB8739.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206218275, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206218355, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_FD774B970C96B0D0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206218509, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206218621, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_A9F3FB8E6C0780FC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206218772, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206218883, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_07C436A676AA1A59.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206219065, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206219149, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_532B9929C68EE5D8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206219298, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206219396, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A18767039B605D8A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206219537, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206219599, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_102C078AEE4D5C34.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206219742, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206219822, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B964A68D830AC6BF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206219996, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2F018D732F244A87.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206220131, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206220224, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206220312, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_49F2D3B3C857FB89.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206220391, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206220484, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1758795206220685, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1758795206220809, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206220872, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206220960, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1758795206221345, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206221428, "dur": 474, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206221905, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1758795206222048, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206222137, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206222198, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1758795206222343, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206222440, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206222524, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206222617, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206222718, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206222821, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206222934, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206223043, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206223146, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206223219, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206223323, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206223451, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206223554, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206223654, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206223746, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206223845, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206223944, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206224059, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206224184, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206225407, "dur": 1233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206226640, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206227862, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206229022, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206230097, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206231259, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206232634, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206234008, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206235439, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206236827, "dur": 1734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206238561, "dur": 1456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206240017, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206241366, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206242295, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206243350, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206244457, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206245561, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206246691, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206247775, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206249012, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206250225, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206251496, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206252900, "dur": 1432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206254333, "dur": 1541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206255874, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206257126, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206258197, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206259358, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206260461, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206261405, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206262358, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206263404, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206264292, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206265570, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206265801, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206265890, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206266041, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206266533, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206267293, "dur": 1526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795206268820, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206269043, "dur": 1209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206270304, "dur": 1376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795206271680, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206271830, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206272311, "dur": 1736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795206274047, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206274160, "dur": 2964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795206277124, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206277326, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206277413, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206277819, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206278669, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206278761, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795206279620, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206279755, "dur": 1959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206281714, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758795206281920, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758795206282222, "dur": 944, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206283166, "dur": 97657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206380829, "dur": 4217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1758795206385046, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206385148, "dur": 6230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1758795206391379, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206391463, "dur": 3754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1758795206395218, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206395307, "dur": 5001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1758795206400308, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206400435, "dur": 5794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1758795206406230, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758795206406454, "dur": 5797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1758795206412302, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206193201, "dur": 23720, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206216924, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_C57112EE168C0CEE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206217029, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206217125, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_F1E1BBCC6ABAE0DB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206217265, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206217338, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_AE483740772154F6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206217489, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206217601, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B4E6D214D3E2A1D4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206217782, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206217875, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1DB14EE0893E74EF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206218029, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206218127, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_95D79878B91D771D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206218289, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206218393, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E921E8B78225857D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206218573, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206218680, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_63177FAB2D86FAFC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206218832, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206218943, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_9A9A08C6BDB0373A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206219105, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206219200, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3193DD45B90182D7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206219334, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206219438, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_ED8B032DF01A4A2C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206219547, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206219634, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_56092ADF71655F79.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206219748, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206219848, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1758795206220054, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206220161, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206220292, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206220400, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206220504, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1758795206220763, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758795206220915, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206221032, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758795206221140, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206221209, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1758795206221364, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206221436, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206221532, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206221639, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206221743, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206221833, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206221918, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206221993, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206222104, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758795206222223, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206222482, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206222579, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206222666, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206222772, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206222878, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206222981, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206223086, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206223172, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206223257, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206223344, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206223429, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206223561, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206223642, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206223727, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206223820, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206223923, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206224022, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206224150, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206225414, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206226638, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206227807, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206228959, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206230039, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206231194, "dur": 1382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206232576, "dur": 1374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206233950, "dur": 1436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206235386, "dur": 1393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206236779, "dur": 1721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206238500, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206239983, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206241319, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206242253, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206243307, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206244431, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206245518, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206246623, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206247731, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206248977, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206250172, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206251453, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206252846, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206254261, "dur": 1552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206255813, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206257047, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206258112, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206259254, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206260361, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206261312, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206262272, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206263310, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206264477, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206265167, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206265488, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206265886, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206266030, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206266536, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206267152, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206267215, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758795206268113, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206268300, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206268607, "dur": 5244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758795206273853, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206274031, "dur": 2283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758795206276314, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206276762, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206276849, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1758795206277036, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758795206277637, "dur": 2853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758795206280523, "dur": 2651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206283174, "dur": 97639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206380820, "dur": 6321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1758795206387143, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206387256, "dur": 5246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1758795206392502, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206392728, "dur": 4754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1758795206397482, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206397580, "dur": 7654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1758795206405234, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206405333, "dur": 6307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1758795206411699, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758795206412308, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206193207, "dur": 23723, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206216933, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_374A8F32DB19802B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206217058, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206217172, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_1349457C77F70AD4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206217287, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206217386, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_85BC3F815A006E37.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206217522, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206217645, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_BF21A61A4CC82206.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206217810, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206217903, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_23AD0A7361E5AF91.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206218060, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206218163, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B861AB185A7695D4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206218338, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206218432, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_46B2EC7768D1E935.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206218603, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206218711, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_CABAF2279C00D800.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206218861, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206218990, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_71D986BE924F9760.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206219144, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206219233, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_BBB5D930EB61518F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206219402, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206219499, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_3A024D3CFAEFB3CE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206219605, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206219691, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_712FA009D6907B2B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206219830, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206219919, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_6F2DFF6A42ECB2AA.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206220006, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206220088, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_F6EE2176487706CE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206220224, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206220304, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_40479579ECF7C4CE.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206220418, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1758795206220623, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1758795206220843, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206220936, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1758795206221156, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1758795206221298, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206221388, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206221496, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206221599, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206221721, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206221806, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1758795206221990, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206222116, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1758795206222264, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206222374, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206222464, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206222543, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206222636, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206222728, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206222836, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206222946, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206223061, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206223149, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206223232, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206223337, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206223445, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206223544, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206223630, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206223717, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206223817, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206223913, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206224007, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1758795206224060, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206224168, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206225426, "dur": 1235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206226661, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206227878, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206229031, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206230081, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206231269, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206232660, "dur": 1398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206234058, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206235504, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206236891, "dur": 1741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206238632, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206240077, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206241404, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206242328, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206243399, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206244485, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206245605, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206246719, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206247844, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206249074, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206250277, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206251578, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206252994, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206254441, "dur": 1526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206255967, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206257204, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206258286, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206259453, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206260532, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206261502, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206262424, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206263475, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206264032, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206264541, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206265121, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206265489, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206265719, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206265804, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206265895, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206266015, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206266524, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206267187, "dur": 2557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206269744, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206269934, "dur": 1003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206270978, "dur": 1602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206272580, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206272795, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206273426, "dur": 4101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206277527, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206277807, "dur": 1052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206278907, "dur": 945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206279852, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206280006, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206280246, "dur": 720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206280997, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206281175, "dur": 1298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206282513, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206282640, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206283154, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758795206283269, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206283540, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206283797, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206284040, "dur": 96762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206380810, "dur": 4380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206385190, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206385288, "dur": 4798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206390086, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206390184, "dur": 3594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206393779, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206393970, "dur": 4572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206398542, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206398623, "dur": 3805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206402429, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206402511, "dur": 5340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1758795206407852, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206408073, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206408145, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758795206408197, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206408311, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206408416, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206408648, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206408756, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206408899, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206409011, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206409136, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206409214, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.Entities.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1758795206409268, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206409368, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206409558, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206409671, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206409798, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206409966, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1758795206410018, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206410116, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1758795206410173, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206410363, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206410567, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206410930, "dur": 1091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758795206412070, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206193214, "dur": 23723, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206216937, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_E409852C73E3C815.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206217091, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206217206, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_2CF19CB4A1AF158C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206217321, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206217419, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_51F928846D19D7E2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206217564, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206217683, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_611725854B2C7A29.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206217853, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206217946, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_761201DEB2FFD5AE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206218106, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206218231, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_5341A2509D5ED266.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206218388, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206218475, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_6DFD6E15EC2CCADA.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206218659, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206218759, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_AD829E7041D38787.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206218916, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206219042, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_99C535C3D6E374C3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206219172, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206219262, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_0B92978C8F2A41F2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206219421, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206219514, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_11639AB4BFA07D5F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206219614, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206219705, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D8293B7A6760759A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206219824, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206219942, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_6BB7F73D7038074E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206220034, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206220136, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206220258, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206220343, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_4190A0BFBFAECF93.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206220447, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206220545, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1758795206220745, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206220818, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1758795206220919, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206221196, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206221285, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1758795206221364, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206221451, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206221568, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206221674, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206221760, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206221854, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206221922, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206222004, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206222110, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206222165, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206222236, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206222335, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206222418, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1758795206222576, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206222657, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206222755, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206222867, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206222964, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206223079, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206223170, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206223242, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206223360, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206223463, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206223583, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206223670, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206223766, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206223870, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206223966, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206224063, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206225396, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206226643, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206227829, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206228996, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206230073, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206231246, "dur": 1384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206232631, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206234000, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206235454, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206236849, "dur": 1739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206238588, "dur": 1442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206240030, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206241387, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206242311, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206243391, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206244474, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206245569, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206246687, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206247784, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206249001, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206250184, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206251464, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206252859, "dur": 1442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206254301, "dur": 1549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206255850, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206257106, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206258195, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206259328, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206260441, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206261379, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206262339, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206263387, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206264576, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206265146, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206265601, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206265755, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206265892, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206266085, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206266531, "dur": 723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206267254, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206267335, "dur": 3814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758795206271149, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206271274, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206271608, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206272003, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206272372, "dur": 4184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758795206276556, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206276995, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206277611, "dur": 2785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758795206280397, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206280702, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206280836, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758795206281708, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206281905, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758795206282273, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758795206282350, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758795206282675, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206283169, "dur": 97614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206380791, "dur": 5596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1758795206386387, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206386487, "dur": 4677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1758795206391164, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206391249, "dur": 4420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1758795206395669, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206395758, "dur": 4519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1758795206400278, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206400412, "dur": 6176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1758795206406588, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758795206406672, "dur": 5682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1758795206412396, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758795206418141, "dur": 641, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 37977, "tid": 633, "ts": 1758795206422237, "dur": 97923, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 37977, "tid": 633, "ts": 1758795206520226, "dur": 536, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 37977, "tid": 633, "ts": 1758795206420417, "dur": 100364, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}