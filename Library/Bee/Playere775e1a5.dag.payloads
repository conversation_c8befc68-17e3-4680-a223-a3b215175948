Payload for "WriteText Library/Bee/artifacts/csharpactions/boot.config_x4e9.info"

{"System.Object":null,"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.PlayerBuildProgramBase","methodName":"WriteBootConfigAction","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/boot.config"],"inputs":["Library/PlayerDataCache/Android/Data/boot.config","Library/PlayerDataCache/Android/Data/boot.config","Library/PlayerDataCache/Android/Data/data.unity3d","Library/PlayerDataCache/Android/Data/RuntimeInitializeOnLoads.json","Library/PlayerDataCache/Android/Data/ScriptingAssemblies.json","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AIModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ARModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AccessibilityModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AndroidJNIModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AnimationModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AssetBundleModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AudioModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ClothModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ContentLoadModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CoreModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CrashReportingModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DSPGraphModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DirectorModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GIModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GameCenterModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GridModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HierarchyCoreModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HotReloadModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.IMGUIModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.IdentifiersModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ImageConversionModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputForUIModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputLegacyModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InsightsModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.JSONSerializeModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.LocalizationModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.MarshallingModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.MultiplayerModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ParticleSystemModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PerformanceReportingModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.Physics2DModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PhysicsModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PropertiesModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ScreenCaptureModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SharedInternalsModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteMaskModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteShapeModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.StreamingModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubstanceModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubsystemsModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TLSModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainPhysicsModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreFontEngineModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreTextEngineModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextRenderingModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TilemapModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIElementsModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UmbraModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsCommonModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityConnectModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityConsentModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityCurlModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityTestProtocolModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAudioModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestTextureModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestWWWModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VFXModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VRModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VehiclesModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VideoModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.WindModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.XRModule.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.AI.Navigation.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll","/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll","/Users/<USER>/Desktop/LocationService/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll","/Users/<USER>/Desktop/LocationService/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.IO.Hashing/System.IO.Hashing.dll","/Users/<USER>/Desktop/LocationService/Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll","/Users/<USER>/Desktop/LocationService/Library/PackageCache/com.unity.burst@f7a407abf4d5/Unity.Burst.Unsafe.dll","/Users/<USER>/Desktop/LocationService/Library/PackageCache/com.unity.collections@d49facba0036/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Accessibility.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Primitives.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Registry.AccessControl.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Registry.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.AppContext.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Buffers.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.Concurrent.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.NonGeneric.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.Specialized.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.Annotations.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.EventBasedAsync.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.Primitives.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.TypeConverter.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Console.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Data.Common.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Data.SqlClient.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Contracts.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Debug.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.FileVersionInfo.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Process.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.StackTrace.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TextWriterTraceListener.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Tools.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TraceEvent.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TraceSource.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Tracing.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Drawing.Primitives.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Dynamic.Runtime.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.Calendars.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.Extensions.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.Compression.ZipFile.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.AccessControl.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.DriveInfo.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.Primitives.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.Watcher.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.IsolatedStorage.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.MemoryMappedFiles.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.Pipes.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.UnmanagedMemoryStream.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Expressions.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Parallel.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Queryable.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Memory.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.AuthenticationManager.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Cache.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.HttpListener.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Mail.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.NameResolution.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.NetworkInformation.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Ping.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Primitives.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Requests.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Security.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.ServicePoint.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Sockets.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Utilities.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebHeaderCollection.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebSockets.Client.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebSockets.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ObjectModel.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.DispatchProxy.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.ILGeneration.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.Lightweight.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Extensions.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Primitives.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.TypeExtensions.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.Reader.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.ReaderWriter.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.ResourceManager.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.Writer.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.CompilerServices.VisualC.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Extensions.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Handles.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.RuntimeInformation.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.WindowsRuntime.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Loader.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Numerics.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Formatters.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Json.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Primitives.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Xml.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.AccessControl.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Claims.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Algorithms.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Cng.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Csp.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.DeriveBytes.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encoding.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.Aes.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.ECDiffieHellman.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.ECDsa.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Hashing.Algorithms.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Hashing.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.OpenSsl.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Pkcs.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Primitives.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.ProtectedData.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.RSA.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.RandomNumberGenerator.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.X509Certificates.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Principal.Windows.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Principal.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.SecureString.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Duplex.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Http.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.NetTcp.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Primitives.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Security.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceProcess.ServiceController.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.CodePages.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.Extensions.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.RegularExpressions.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.AccessControl.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Overlapped.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.Extensions.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.Parallel.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Thread.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.ThreadPool.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Timer.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ValueTuple.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.ReaderWriter.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XDocument.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.XDocument.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.XmlDocument.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XmlDocument.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XmlSerializer.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.Xsl.Primitives.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/netstandard.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.CJK.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.MidEast.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.Other.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.Rare.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.West.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/I18N.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Microsoft.CSharp.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Data.Sqlite.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Data.Tds.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Security.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ComponentModel.Composition.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ComponentModel.DataAnnotations.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Configuration.Install.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Configuration.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Core.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.DataSetExtensions.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Entity.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Linq.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.OracleClient.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Services.Client.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Services.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Design.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.DirectoryServices.Protocols.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.DirectoryServices.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Drawing.Design.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Drawing.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.EnterpriseServices.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.IO.Compression.FileSystem.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.IO.Compression.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.IdentityModel.Selectors.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.IdentityModel.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Json.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Management.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Messaging.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.Http.WebRequest.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.Http.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Numerics.Vectors.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Numerics.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Reflection.Context.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Caching.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.DurableInstancing.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Remoting.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Serialization.Formatters.Soap.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Serialization.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Security.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Activation.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Discovery.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Internals.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Routing.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Web.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceProcess.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Transactions.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.ApplicationServices.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.DynamicData.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Extensions.Design.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Extensions.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.RegularExpressions.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Services.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.Forms.DataVisualization.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.Forms.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xaml.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.Linq.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.Serialization.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.dll","/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/mscorlib.dll","Library/Bee/Playere775e1a5-inputdata.json"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Assembly-CSharp-FeaturesChecked.txt_ki5r.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Assembly-CSharp.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Mono.Security-FeaturesChecked.txt_pcel.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Mono.Security.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/mscorlib-FeaturesChecked.txt_pwj8.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/mscorlib.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Configuration-FeaturesChecked.txt_vfp9.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/System.Configuration.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Core-FeaturesChecked.txt_k2yd.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/System.Core.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System-FeaturesChecked.txt_v90b.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/System.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/System.Xml-FeaturesChecked.txt_wnnz.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/System.Xml.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Burst-FeaturesChecked.txt_5voi.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.Burst-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.Burst.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Burst.Unsafe-FeaturesChecked.txt_9x4w.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.Burst.Unsafe-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.Burst.Unsafe.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Collections-FeaturesChecked.txt_imay.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.Collections-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.Collections.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Collections.LowLevel.ILSupport-FeaturesChecked.txt_orex.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.Collections.LowLevel.ILSupport-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.Collections.LowLevel.ILSupport.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.InputSystem-FeaturesChecked.txt_ivdb.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.InputSystem.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.InputSystem.ForUI-FeaturesChecked.txt_1qsl.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.InputSystem.ForUI.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.Mathematics-FeaturesChecked.txt_bwkn.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.Mathematics-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.Mathematics.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipeline.Universal.ShaderLibrary-FeaturesChecked.txt_erxn.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.RenderPipeline.Universal.ShaderLibrary-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipeline.Universal.ShaderLibrary.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt_9rh7.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.Core.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Core.Runtime.Shared-FeaturesChecked.txt_024x.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime.Shared-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.Core.Runtime.Shared.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.GPUDriven.Runtime-FeaturesChecked.txt_gz0s.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.GPUDriven.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.GPUDriven.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Universal.Runtime-FeaturesChecked.txt_7fk5.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Universal.Runtime-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.Universal.Runtime.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/Unity.TextMeshPro-FeaturesChecked.txt_c7od.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/Unity.TextMeshPro.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AIModule-FeaturesChecked.txt_jr17.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AndroidJNIModule-FeaturesChecked.txt_x3ln.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AndroidJNIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AnimationModule-FeaturesChecked.txt_3sdh.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AnimationModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AudioModule-FeaturesChecked.txt_tbfa.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AudioModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.CoreModule-FeaturesChecked.txt_uimb.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.CoreModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine-FeaturesChecked.txt_x1ug.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.GridModule-FeaturesChecked.txt_5p9t.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.GridModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt_rzjr.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.HierarchyCoreModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.IMGUIModule-FeaturesChecked.txt_ufk2.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.IMGUIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputForUIModule-FeaturesChecked.txt_awp5.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.InputForUIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputLegacyModule-FeaturesChecked.txt_y5my.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.InputLegacyModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputModule-FeaturesChecked.txt_xblj.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.InputModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.JSONSerializeModule-FeaturesChecked.txt_vjsc.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.JSONSerializeModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.Physics2DModule-FeaturesChecked.txt_45hf.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.Physics2DModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.PhysicsModule-FeaturesChecked.txt_2l1t.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.PhysicsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.PropertiesModule-FeaturesChecked.txt_m9z5.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.PropertiesModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SharedInternalsModule-FeaturesChecked.txt_sm9d.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SharedInternalsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SpriteShapeModule-FeaturesChecked.txt_zrrh.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SpriteShapeModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SubsystemsModule-FeaturesChecked.txt_vzqa.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SubsystemsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TerrainModule-FeaturesChecked.txt_wybo.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TerrainModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt_4rfo.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextCoreFontEngineModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt_el85.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextCoreTextEngineModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextRenderingModule-FeaturesChecked.txt_ighi.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextRenderingModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TilemapModule-FeaturesChecked.txt_xct4.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TilemapModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UI-FeaturesChecked.txt_v0bu.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UI.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UIElementsModule-FeaturesChecked.txt_aabo.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UIElementsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UIModule-FeaturesChecked.txt_zatd.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UIModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt_z4m3.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UnityAnalyticsCommonModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt_cjxw.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UnityAnalyticsModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityConsentModule-FeaturesChecked.txt_80hc.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.UnityConsentModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UnityConsentModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt_jk15.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UnityWebRequestModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VFXModule-FeaturesChecked.txt_r8mm.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.VFXModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.VFXModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VRModule-FeaturesChecked.txt_nz9x.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.VRModule.dll"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityEngine.XRModule-FeaturesChecked.txt_xks3.info"

{"PlayerBuildProgramLibrary.FeatureExtractor+Data":{"ignoreSystemDlls":true,"featuresToCheck":[{"referenceName":"UnityEngine.WebCamTexture"},{"referenceName":"UnityEngine.Microphone"},{"referenceName":"UnityEngine.Networking"},{"referenceName":"System.Net.Sockets"},{"referenceName":"System.Net.WebRequest"},{"referenceName":"UnityEngine.Ping"},{"referenceName":"UnityEngine.Networking.UnityWebRequest"},{"referenceName":"UnityEngine.Input::set_multiTouchEnabled"},{"referenceName":"UnityEngine.Handheld::Vibrate"},{"referenceName":"UnityEngine.Application::get_internetReachability"},{"referenceName":"UnityEngine.Input::get_location"},{"referenceName":"UnityEngine.Input::get_acceleration"},{"referenceName":"UnityEngine.Input::GetAccelerationEvent"},{"referenceName":"UnityEngine.Input::get_accelerationEvents"},{"referenceName":"UnityEngine.Input::get_accelerationEventCount"},{"referenceName":"UnityEngine.Input::get_touches"},{"referenceName":"UnityEngine.Input::GetTouch"},{"referenceName":"UnityEngine.Input::get_touchCount"},{"referenceName":"UnityEngine.Input::get_multiTouchEnabled"},{"referenceName":"UnityEngine.Android.DiagnosticsReporting::CallReportFullyDrawn"},{"referenceName":"UnityEngine.Android.AndroidGame::get_GameMode"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_ProjectLevelBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherBuildGradle"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_UnityLibraryManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_LauncherManifest"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleProperties"},{"referenceName":"UnityEditor.Android.AndroidProjectFiles::get_GradleSettings"},{"referenceName":"UnityEngine.Android.AndroidGame::SetGameState"},{"referenceName":"UnityEngine.Android.ApplicationExitInfoProvider::GetHistoricalProcessExitInfo"}]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.FeatureExtractor","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.XRModule.dll"],"targetDirectories":[]}}    

Payload for "GenerateNativePluginsForAssemblies Library/Bee/artifacts/Android/AsyncPluginsFromLinker"

{"PlayerBuildProgramLibrary.Data.GenerateNativePluginsForAssembliesArgs":{"PluginOutputFolder":"Library/Bee/artifacts/Android/AsyncPluginsFromLinker","SymbolOutputFolder":"Library/Bee/artifacts/Android/AsyncPluginsFromLinkerSymbols","Assemblies":["Library/Bee/artifacts/Android/ManagedStripped/Assembly-CSharp.dll","Library/Bee/artifacts/Android/ManagedStripped/Mono.Security.dll","Library/Bee/artifacts/Android/ManagedStripped/mscorlib.dll","Library/Bee/artifacts/Android/ManagedStripped/System.Configuration.dll","Library/Bee/artifacts/Android/ManagedStripped/System.Core.dll","Library/Bee/artifacts/Android/ManagedStripped/System.dll","Library/Bee/artifacts/Android/ManagedStripped/System.Xml.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.Burst.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.Burst.Unsafe.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.Collections.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.Collections.LowLevel.ILSupport.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.InputSystem.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.InputSystem.ForUI.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.Mathematics.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipeline.Universal.ShaderLibrary.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.Core.Runtime.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.Core.Runtime.Shared.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.GPUDriven.Runtime.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.RenderPipelines.Universal.Runtime.dll","Library/Bee/artifacts/Android/ManagedStripped/Unity.TextMeshPro.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AIModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AndroidJNIModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AnimationModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AudioModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.CoreModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.GridModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.HierarchyCoreModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.IMGUIModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.InputForUIModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.InputLegacyModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.InputModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.JSONSerializeModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.Physics2DModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.PhysicsModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.PropertiesModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SharedInternalsModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SpriteShapeModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SubsystemsModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TerrainModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextCoreFontEngineModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextCoreTextEngineModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextRenderingModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TilemapModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UI.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UIElementsModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UIModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UnityAnalyticsCommonModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UnityAnalyticsModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UnityConsentModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UnityWebRequestModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.VFXModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.VRModule.dll","Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.XRModule.dll"]}}    

Payload for "WriteText Library/Bee/artifacts/Android/dqdht/_dummy_for_header_discovery"

//dummy for header discovery    

Payload for "WriteText Library/Bee/artifacts/Android/zz99l/_dummy_for_header_discovery"

//dummy for header discovery    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/d48p0rrld72v0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/UnityAdsStubs.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/char-conversions.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/il2cpp-api.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/il2cpp-benchmark-support.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/il2cpp-mono-api.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/il2cpp-runtime-stats.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/mnlnwkfn8ctb0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/gc/BoehmGC.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/gc/GCHandle.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/gc/GarbageCollector.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/gc/NullGC.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/gc/WriteBarrier.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/gc/WriteBarrierValidation.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/kvxc57m54kbl0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/Mono/Runtime.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/Mono/RuntimeClassHandle.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/Mono/RuntimeGPtrArrayHandle.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/Mono/RuntimeMarshal.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/Mono/SafeStringMarshal.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/6w4zoaan1ij60.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Diagnostics/Debugger.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Diagnostics/StackFrame.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Diagnostics/StackTrace.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/na753dzbin290.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Globalization/CalendarData.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Globalization/CompareInfo.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Globalization/CultureData.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Globalization/CultureInfo.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Globalization/RegionInfo.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/cc2na8gafsbd0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.IO/BrokeredFileSystem.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.IO/DriveInfo.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.IO/MonoIO.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.IO/Path.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/xgpqgivrb1wj0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/Assembly.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/CustomAttributeData.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/EventInfo.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/ParameterInfo.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimeMethodInfo.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/xgpqgivrb1wj1.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/AssemblyName.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/FieldInfo.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/Module.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/MonoMethodInfo.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimeAssembly.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimeEventInfo.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimeModule.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimeParameterInfo.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/xgpqgivrb1wj2.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/MethodBase.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/MonoMethod.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimeConstructorInfo.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimeFieldInfo.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Reflection/RuntimePropertyInfo.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/zghv7akqzxtt0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.InteropServices/GCHandle.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.InteropServices/Marshal.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.InteropServices/RuntimeInformation.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/pjfdzfiga3q50.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.Remoting.Messaging/AsyncResult.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Runtime.Remoting.Messaging/MonoMethodMessage.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/eivuir3nrei20.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Security.Principal/WindowsIdentity.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Security.Principal/WindowsImpersonationContext.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Security.Principal/WindowsPrincipal.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/bimbzg8um6p20.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Text/EncodingHelper.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Text/Normalization.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/6vsxr4oefhi90.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/InternalThread.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/Monitor.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/Mutex.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/NativeEventCalls.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/OSSpecificSynchronizationContext.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/Thread.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/Timer.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System.Threading/WaitHandle.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/s8wu7skssfrw0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/ArgIterator.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/RuntimeFieldHandle.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/Type.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/s8wu7skssfrw1.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/AppDomain.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/Array.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/CurrentSystemTimeZone.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/DateTime.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/NumberFormatter.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/Object.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/RuntimeMethodHandle.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/s8wu7skssfrw2.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/Buffer.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/GC.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/MonoCustomAttrs.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/RuntimeType.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/RuntimeTypeHandle.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/s8wu7skssfrw3.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/CLRConfig.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/ConsoleDriver.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/Delegate.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/Enum.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/Environment.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/Exception.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/Number.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/String.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/TypedReference.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/mscorlib/System/ValueType.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/0j19xt41pcq80.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/System/System.Diagnostics/DefaultTraceListener.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/System/System.Diagnostics/FileVersionInfo.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/System/System.Diagnostics/Process.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/System/System.Diagnostics/Stopwatch.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/ny38ucgf4xc90.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/System/System.Net.NetworkInformation/LinuxNetworkInterface.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/System/System.Net.NetworkInformation/MacOsIPInterfaceProperties.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/zkqz1zrwp40n0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/System/System.Net.Sockets/Socket.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/icalls/System/System.Net.Sockets/SocketException.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/53n37n86f9n90.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/CustomAttributeCreator.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/CustomAttributeDataReader.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/FieldLayout.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/GenericMetadata.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/GenericSharing.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/Il2CppGenericContextCompare.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/Il2CppGenericMethodHash.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/Il2CppMethodSpecOrGenericMethod.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/Il2CppMethodSpecOrGenericMethodCompare.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/Il2CppMethodSpecOrGenericMethodHash.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/Il2CppSignature.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/53n37n86f9n91.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/ArrayMetadata.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/GenericMethod.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/Il2CppGenericContextHash.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/Il2CppGenericMethodCompare.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/Il2CppTypeCompare.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/53n37n86f9n92.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/Il2CppGenericClassCompare.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/Il2CppGenericClassHash.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/Il2CppGenericInstCompare.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/Il2CppGenericInstHash.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/metadata/Il2CppTypeHash.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/0jv8ovgiu5zs0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/mono/ThreadPool/ThreadPoolMonitorThread.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/mono/ThreadPool/ThreadPoolWorkerThread.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/mono/ThreadPool/threadpool-ms-io-poll.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/mono/ThreadPool/threadpool-ms-io.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/mono/ThreadPool/threadpool-ms.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/qgzba4164xe40.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Android/ConsoleExtension.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Android/Initialize.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Android/Locale.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Android/StackTrace.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/zqilaso4hc4k0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/c-api/Error.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/c-api/File.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/c-api/Locale.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/c-api/Thread.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/zqilaso4hc4k1.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/c-api/Allocator.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/c-api/Directory.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/c-api/Environment.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/c-api/Path.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/c-api/Socket.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/c-api/Time.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/nfznws4i7rs40.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_errno.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_io.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_networking.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_random.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_sizecheck.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_time.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_uid.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/pal_unused.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/iuufwgi1rtf20.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/CrashHelpers.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Error.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Event.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/FastReaderReaderWriterLock.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/LibraryLoader.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Messages.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Semaphore.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Thread.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/iuufwgi1rtf21.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/ConditionVariable.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Image.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Mutex.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Path.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/ReaderWriterLock.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Socket.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/StackTrace.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/2baawk0qn9tr0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/BrokeredFileSystem.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/Initialize.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/WaitObject.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/2baawk0qn9tr1.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/Assert.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/CpuInfo.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/Debug.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/Handle.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/LibraryLoader.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/MemoryMappedFile.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/SystemCertificates.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/Thread.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/WindowsRuntime.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/2baawk0qn9tr2.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/COM.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/CrashHelpers.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/Environment.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/File.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/MarshalStringAlloc.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/SocketBridge.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Generic/SocketImpl.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/79hwrenxfyrk0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/OSX/Image.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/OSX/Process.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/OSX/SystemCertificates.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/OSX/Thread.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/2ay74kwh3iok0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/CrashHelpers.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/Error.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/Locale.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/PosixHelpers.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/Thread.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/ThreadImpl.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/2ay74kwh3iok1.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/Directory.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/Encoding.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/FileSystemWatcher.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/MarshalAlloc.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/SocketImpl.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/SystemCertificates.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/2ay74kwh3iok2.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/CpuInfo.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/Cryptography.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/File.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/LastError.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/LibraryLoader.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/MemoryMappedFile.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/2ay74kwh3iok3.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/ConditionVariableImpl.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/Console.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/Environment.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/Image.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/Memory.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/NativeMethods.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/Path.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/Process.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/StackTrace.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/Time.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Posix/TimeZone.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/u6kfugrkk2jm0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/Assert.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/CrashHelpers.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/File.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/LibraryLoader.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/MarshalStringAlloc.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/u6kfugrkk2jm1.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/Console.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/Encoding.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/Image.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/Locale.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/Path.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/u6kfugrkk2jm2.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/COM.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/Cryptography.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/Debug.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/Directory.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/DllMain.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/FileSystemWatcher.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/Initialize.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/MemoryMappedFile.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/NativeMethods.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/Process.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/SystemCertificates.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/Thread.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/WindowsHelpers.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/u6kfugrkk2jm3.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/MarshalAlloc.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/Memory.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/SocketImpl.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/StackTrace.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/ThreadImpl.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/WindowsRuntime.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/u6kfugrkk2jm4.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/ConditionVariableImpl.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/CpuInfo.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/Environment.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/LastError.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/SynchronizationContext.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/Time.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/Win32/TimeZone.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/d7ibs2ucdiaj0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/WinRT/BrokeredFileSystem.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/WinRT/Environment.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/WinRT/File.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/WinRT/Initialize.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/WinRT/Locale.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/WinRT/Process.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/WinRT/Win32ApiSharedEmulation.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/WinRT/Win32ApiWinRTEmulation.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/7d423tr5abvk0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/Output.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/Runtime.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/mono-structs.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/sha1.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/7d423tr5abvk1.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/Exception.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/Il2CppError.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/Logging.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/Memory.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/MemoryMappedFile.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/MemoryPool.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/MemoryRead.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/7d423tr5abvk2.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/DirectoryUtils.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/Environment.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/MarshalingUtils.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/MemoryPoolAddressSanitizer.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/PathUtils.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/utils/StringUtils.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/paw80a2iv8qm0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm-utils/BlobReader.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm-utils/DebugSymbolReader.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm-utils/Debugger.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm-utils/Finally.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm-utils/NativeSymbol.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm-utils/VmStringUtils.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm-utils/VmThreadUtils.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/878liqou2kzx0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm-utils/icalls/mscorlib/System/Math.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm-utils/icalls/mscorlib/System/MathF.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/doq9szltk95y0.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/ClassInlines.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/ComObjectBase.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Module.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Path.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/String.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/VisualizerHelpers.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/WindowsRuntime.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/doq9szltk95y1.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Array.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Assembly.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/MarshalAlloc.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Object.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/PlatformInvoke.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Reflection.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/WaitHandle.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/WeakReference.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/doq9szltk95y2.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/COM.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/COMEntryPoints.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Enum.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Exception.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/InternalCalls.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Liveness.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/MetadataAlloc.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Profiler.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/RCW.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/doq9szltk95y3.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/AndroidRuntime.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/CCW.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/ClassLibraryPAL.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Domain.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/GlobalMetadata.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/LastError.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/MemoryInformation.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Method.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Monitor.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Random.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/ScopedThreadAttacher.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Thread.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/doq9szltk95y4.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/AssemblyName.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/CCWBase.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/GenericClass.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/GenericContainer.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Image.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Parameter.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Property.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Type.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/doq9szltk95y5.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Field.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Il2CppHStringReference.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/MetadataLoader.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Runtime.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/StackTrace.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/ThreadPoolMs.cpp"
    

Payload for "MakeLump Library/Bee/artifacts/Android/87lik/doq9szltk95y6.lump.cpp"

//Generated lump file. generated by Bee.NativeProgramSupport.Lumping
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Class.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/Event.cpp"
#include "/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/vm/MetadataCache.cpp"
    

Payload for "WriteText Library/Bee/artifacts/Android/87lik/_dummy_for_header_discovery"

//dummy for header discovery    

Payload for "WriteText Library/Bee/artifacts/Android/d8kzr/_dummy_for_header_discovery"

//dummy for header discovery    

Payload for "WriteText Library/Bee/artifacts/csharpactions/IntermediateFiles.txt_ri9j.info"

{"AndroidPlayerBuildProgram.Actions.ActionGenerateProjectFiles+Arguments":{"ProjectPath":"/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle","Architectures":"ARM64","BuildSystem":"Gradle","GradleProjectCreateInfo":{"ArtifactsPath":"Library/Bee/artifacts/Android","EnvironmentVariableInputs":["UNITY_THISISABUILDMACHINE:"],"HostPlatform":"OSX","ApplicationType":"APK","BuildType":"Release","AndroidSDKPath":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/SDK","AndroidNDKPath":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK","AndroidJavaPath":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/OpenJDK","PreferredHeapSizeForJVM":4096,"GradleVersion":"8.11","ProjectFiles":{"UnityLibraryBuildGradle":{"RelativeDestinationPath":"unityLibrary/build.gradle","CanBeModifiedByUser":true},"LauncherBuildGradle":{"RelativeDestinationPath":"launcher/build.gradle","CanBeModifiedByUser":true},"LauncherSetupUnitySymbolsGradle":{"SourcePath":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/launcher/setupSymbols.gradle","RelativeDestinationPath":"launcher/setupSymbols.gradle","CanBeModifiedByUser":false},"SharedKeepUnitySymbolsGradle":{"SourcePath":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/shared/keepUnitySymbols.gradle","RelativeDestinationPath":"shared/keepUnitySymbols.gradle","CanBeModifiedByUser":false},"SharedCommonGradle":{"SourcePath":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/shared/common.gradle","RelativeDestinationPath":"shared/common.gradle","CanBeModifiedByUser":false},"ProjectLevelBuildGradle":{"RelativeDestinationPath":"build.gradle","CanBeModifiedByUser":true},"GradleProperties":{"RelativeDestinationPath":"gradle.properties","CanBeModifiedByUser":true},"UnityProguard":{"SourcePath":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/UnityProGuardTemplate.txt","RelativeDestinationPath":"unityLibrary/proguard-unity.txt","CanBeModifiedByUser":true},"ProguardUser":{"RelativeDestinationPath":"unityLibrary/proguard-user.txt","CanBeModifiedByUser":true},"GradleSettings":{"RelativeDestinationPath":"settings.gradle","CanBeModifiedByUser":true},"LocalProperties":{"RelativeDestinationPath":"local.properties","CanBeModifiedByUser":true}},"AdditionalLibrariesRelativePaths":[],"AdditionalUserInputs":[],"AdditionalUserOutputs":{"AdditionalManifests":[],"AdditionalBuildGradleFiles":[],"AdditionalGradleSettings":[],"AdditionalGradleProperties":[],"AdditionalFilesWithContents":[]},"UserCopyData":{"FilesToCopy":[],"DirectoriesToCopy":[]},"AdditionalUserData":[],"BuildTools":"34.0.0","TargetSDKVersion":36,"MinSDKVersion":23,"PackageName":"com.location.pryze","Architectures":"ARM64","BuildApkPerCpuArchitecture":false,"DebugSymbols":{"Level":"None","Format":"5"},"VersionCode":1,"VersionName":"0.1.0","Minify":1,"NoCompressOptions":{"RelativeFilePaths":[],"FileExtensions":[]},"UseCustomKeystore":false,"KeystorePath":"","KeystoreName":"","KeystorePassword":"","KeystoreAliasName":"","KeystoreAliasPassword":"","ScriptingImplementation":"IL2CPP","AndroidLibraries":[],"AARFiles":[],"BuiltinJavaSourcePaths":["com/unity3d/player/UnityPlayerGameActivity.java"],"JavaSourcePaths":[],"KotlinSourcePaths":[],"PlayerPackage":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer","PlayerPackageTools":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools","SymlinkSources":false,"InstallIntoBuildsFolder":false,"UnityPath":"","UnityProjectPath":"/Users/<USER>/Desktop/LocationService","OverrideCMakeIntermdiateDirectory":true,"Dependencies":[],"ApplicationEntry":"GameActivity","JarFiles":["classes.jar"],"UseOptimizedFramePacing":false,"ReportGooglePlayAppDependencies":false,"UnityVersion":"6000.2.2f1"}},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"AndroidPlayerBuildProgram.Actions.ActionGenerateProjectFiles","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/AndroidPlayerBuildProgram.exe","targets":["Library/Bee/artifacts/Android/IntermediateFiles.txt","Library/Bee/artifacts/Android/Gradle/build.gradle","Library/Bee/artifacts/Android/Gradle/unityLibrary/build.gradle","Library/Bee/artifacts/Android/Gradle/launcher/build.gradle","Library/Bee/artifacts/Android/Gradle/gradle.properties","Library/Bee/artifacts/Android/Gradle/local.properties","Library/Bee/artifacts/Android/Gradle/settings.gradle","Library/Bee/artifacts/Android/Gradle/build.gradle.xml","Library/Bee/artifacts/Android/Gradle/unityLibrary/build.gradle.xml","Library/Bee/artifacts/Android/Gradle/launcher/build.gradle.xml","Library/Bee/artifacts/Android/Gradle/gradle.properties.xml","Library/Bee/artifacts/Android/Gradle/local.properties.xml","Library/Bee/artifacts/Android/Gradle/settings.gradle.xml","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/setupSymbols.gradle","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/shared/keepUnitySymbols.gradle","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/shared/common.gradle","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/CMakeLists.txt","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/GAToUnityCallbacks.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/MacroEnd.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/MacroHeaderBegin.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/MacroSourceBegin.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAApplication.cpp","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAApplication.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAConfiguration.cpp","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAConfiguration.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGADebug.cpp","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGADebug.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAEntry.cpp","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAEvents.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInput.cpp","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInput.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInputKeyEvent.cpp","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInputMotionEvent.cpp","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGASoftKeyboard.cpp","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGASoftKeyboard.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGASoftKeyboardCallbacks.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGATypes.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAVersion.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGACallbacks.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGAConfigurationCallbacks.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGAKeyEventCallbacks.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGAMotionEventCallbacks.h","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/FramePacing/CMakeLists.txt","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/CMakeLists.txt","Library/Bee/artifacts/Android/Gradle/unityLibrary/proguard-unity.txt","/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/gradle/wrapper/gradle-wrapper.properties"],"inputs":["/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/launcher/setupSymbols.gradle","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/shared/keepUnitySymbols.gradle","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/shared/common.gradle","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/UnityProGuardTemplate.txt","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/CMakeLists.txt","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/GAToUnityCallbacks.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/MacroEnd.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/MacroHeaderBegin.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/MacroSourceBegin.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/ReadMe.txt","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAApplication.cpp","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAApplication.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAConfiguration.cpp","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAConfiguration.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGADebug.cpp","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGADebug.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAEntry.cpp","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAEvents.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInput.cpp","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInput.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInputKeyEvent.cpp","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInputMotionEvent.cpp","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGASoftKeyboard.cpp","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGASoftKeyboard.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGASoftKeyboardCallbacks.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGATypes.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAVersion.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGACallbacks.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGAConfigurationCallbacks.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGAKeyEventCallbacks.h","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGAMotionEventCallbacks.h"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/IntermediateLauncherManifestDiag.txt_4oyd.info"

{"AndroidPlayerBuildProgram.Actions.GenerateManifests+Arguments":{"Configuration":{"TargetSDKVersion":36,"LauncherManifestTemplatePath":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Apk/LauncherManifest.xml","LauncherManifestTemplateUsed":false,"LibraryManifestTemplatePath":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Apk/UnityManifest.xml","LibraryManifestCustomTemplateUsed":false,"LauncherManifestPath":"launcher/src/main/AndroidManifest.xml","LibraryManifestPath":"unityLibrary/src/main/AndroidManifest.xml","TVCompatibility":false,"AppCategory":"game","BannerEnabled":true,"PreferredInstallLocation":"PreferExternal","TextureSupport":"Generic","GamepadSupportLevel":"SupportsDPad","SupportedAspectRatioMode":1,"MaxAspectRatio":2.4,"MinAspectRatio":1,"ForceInternetPermission":false,"UseLowAccuracyLocation":false,"ForceSDCardPermission":false,"PreserveFramebufferAlpha":false,"DefaultInterfaceOrientation":"AutoRotation","AllowedAutorotateToPortrait":true,"AllowedAutorotateToPortraitUpsideDown":true,"AllowedAutorotateToLandscapeLeft":true,"AllowedAutorotateToLandscapeRight":true,"SplashScreenScale":"Center","RenderOutsideSafeArea":true,"GraphicsDevices":["Vulkan","OpenGLES3"],"OpenGLRequireES31":false,"OpenGLRequireES31AEP":false,"OpenGLRequireES32":false,"StartInFullscreen":true,"DefaultWindowWidth":1920,"DefaultWindowHeight":1080,"MinimumWindowWidth":400,"MinimumWindowHeight":300,"ResizeableActivity":true,"FullScreenMode":"FullScreenWindow","AutoRotationBehavior":"User","StripEngineCode":true,"ApplicationEntry":"GameActivity","JavaFileNames":["UnityPlayerGameActivity.java"],"EnableOnBackInvokedCallback":true},"Services":{"EnableUnityConnect":true,"EnablePerformanceReporting":false,"EnableAnalytics":false,"EnableCrashReporting":true,"EnableInsights":true},"ProjectPath":"/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle","ArtifactsPath":"Library/Bee/artifacts/Android/Manifest","FeatureChecklist":["Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Burst-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Burst.Unsafe-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Collections-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Collections.LowLevel.ILSupport-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Mathematics-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.RenderPipeline.Universal.ShaderLibrary-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime.Shared-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.GPUDriven.Runtime-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Universal.Runtime-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UnityConsentModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.VFXModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt"],"Development":false,"UsingObb":false,"UsingMTE":false,"GradleResourcesInformation":{"TargetSDKVersion":36,"RoundIconsAvailable":false,"RoundIconsSupported":true,"AdaptiveIconsSupported":true,"AdaptiveIconsAvailable":false},"LauncherManifestDiagnosticsPath":"Library/Bee/artifacts/Android/Manifest/IntermediateLauncherManifestDiag.txt","LibraryManifestDiagnosticsPath":"Library/Bee/artifacts/Android/Manifest/IntermediateLibraryManifestDiag.txt","APIRequiringInternetPermission":["UnityEngine.Networking","System.Net.Sockets","System.Net.WebRequest","UnityEngine.Ping","UnityEngine.Networking.UnityWebRequest"]},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"AndroidPlayerBuildProgram.Actions.GenerateManifests","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/AndroidPlayerBuildProgram.exe","targets":["Library/Bee/artifacts/Android/Manifest/IntermediateLauncherManifestDiag.txt","Library/Bee/artifacts/Android/Manifest/IntermediateLibraryManifestDiag.txt","Library/Bee/artifacts/Android/Manifest/launcher/src/main/AndroidManifest.xml","Library/Bee/artifacts/Android/Manifest/unityLibrary/src/main/AndroidManifest.xml"],"inputs":["Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Burst-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Burst.Unsafe-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Collections-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Collections.LowLevel.ILSupport-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.Mathematics-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.RenderPipeline.Universal.ShaderLibrary-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime.Shared-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.GPUDriven.Runtime-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Universal.Runtime-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UnityConsentModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.VFXModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt","Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Apk/LauncherManifest.xml","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Apk/UnityManifest.xml"],"targetDirectories":[]}}    

Payload for "WriteText /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v21/styles.xml"

<?xml version="1.0" encoding="utf-8"?>
<resources>
  <style name="BaseUnityTheme" parent="android:Theme.Material.Light.NoActionBar.Fullscreen">
  </style>
</resources>    

Payload for "WriteText /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v31/styles.xml"

<?xml version="1.0" encoding="utf-8"?>
<resources>
  <style name="BaseUnityTheme" parent="android:Theme.Holo.Light.NoActionBar.Fullscreen">
    <item name="android:windowSplashScreenAnimatedIcon">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenBackground">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenIconBackgroundColor">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenAnimationDuration">0</item>
    <item name="android:windowBackground">@android:color/black</item>
  </style>
  <style name="BaseUnityGameActivityTheme" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:windowSplashScreenAnimatedIcon">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenBackground">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenIconBackgroundColor">@color/staticSplashScreenBackgroundColor</item>
    <item name="android:windowSplashScreenAnimationDuration">0</item>
    <item name="android:windowBackground">@android:color/black</item>
  </style>
</resources>    

Payload for "WriteText /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/styles.xml"

<?xml version="1.0" encoding="utf-8"?>
<resources>
  <style name="UnityThemeSelector" parent="BaseUnityTheme">
    <item name="android:windowBackground">@android:color/black</item>
  </style>
  <style name="BaseUnityTheme" parent="android:Theme.Holo.Light.NoActionBar.Fullscreen" />
  <style name="UnityThemeSelector.Translucent" parent="@style/UnityThemeSelector">
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:windowBackground">@android:color/transparent</item>
  </style>
  <style name="BaseUnityGameActivityTheme" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:windowBackground">@android:color/black</item>
  </style>
  <style name="BaseUnityGameActivityTheme.Translucent" parent="@style/BaseUnityGameActivityTheme">
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:windowBackground">@android:color/transparent</item>
  </style>
</resources>    

Payload for "WriteText /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/colors.xml"

<?xml version="1.0" encoding="utf-8"?>
<resources>
  <color name="staticSplashScreenBackgroundColor">#231F20</color>
</resources>    

Payload for "WriteText /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/values/strings.xml"

<?xml version="1.0" encoding="utf-8"?>
<resources>
  <string name="app_name">LocationService</string>
</resources>    

Payload for "WriteText Library/Bee/artifacts/csharpactions/UnityClassRegistration.cpp_36dw.info"

{"PlayerBuildProgramLibrary.ClassRegistrationGenerator+Arguments":{"linkerOutput":"Library/Bee/artifacts/Android/ManagedStripped/UnityLinkerToEditorData.json","editorOutput":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/UnityLinkerInputs/EditorToUnityLinkerData.json","moduleAssetsPath":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/modules.asset","outputClassRegistration":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/UnityClassRegistration.cpp","enableStripping":true},"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"PlayerBuildProgramLibrary.ClassRegistrationGenerator","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll","targets":["/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/UnityClassRegistration.cpp"],"inputs":["Library/Bee/artifacts/Android/ManagedStripped/UnityLinkerToEditorData.json","/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/UnityLinkerInputs/EditorToUnityLinkerData.json","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/modules.asset"],"targetDirectories":[]}}    

Payload for "WriteText Library/Bee/artifacts/csharpactions/unity_app_guid_y6e1.info"

{"System.Object":null,"Bee.TundraBackend.CSharpActionInvocationInformation":{"typeFullName":"AndroidPlayerBuildProgram.Actions.GuidGenerator","methodName":"Run","assemblyLocation":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/AndroidPlayerBuildProgram.exe","targets":["/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/unity_app_guid"],"inputs":["Library/PlayerDataCache/Android/Data/data.unity3d","Library/PlayerDataCache/Android/Data/RuntimeInitializeOnLoads.json","Library/PlayerDataCache/Android/Data/ScriptingAssemblies.json","Library/Bee/artifacts/Android/boot.config","/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/data/Metadata/global-metadata.dat","/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/data/Resources/mscorlib.dll-resources.dat","/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Data/Resources/unity default resources"],"targetDirectories":[]}}    

Payload for "WriteText /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/resources/META-INF/com.android.games.engine.build_fingerprint"

6000.2.2f1;IL2CPP;Release;StripEngineCode:1;OptimizedFramePacing:0;AppEntry:2;LTO:0    

Payload for "ModifyAndroidProjectCallback /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml (+8 others)"

{"AndroidPlayerBuildProgram.Data.AndroidProjectFileArgs":{"ProjectPath":"/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle","ArtifactsPath":"Library/Bee/artifacts/Android","LauncherManifestPath":"launcher/src/main/AndroidManifest.xml","LibraryManifestPath":"unityLibrary/src/main/AndroidManifest.xml","ProjectFiles":{"UnityLibraryBuildGradle":{"RelativeDestinationPath":"unityLibrary/build.gradle","CanBeModifiedByUser":true},"LauncherBuildGradle":{"RelativeDestinationPath":"launcher/build.gradle","CanBeModifiedByUser":true},"LauncherSetupUnitySymbolsGradle":{"SourcePath":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/launcher/setupSymbols.gradle","RelativeDestinationPath":"launcher/setupSymbols.gradle","CanBeModifiedByUser":false},"SharedKeepUnitySymbolsGradle":{"SourcePath":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/shared/keepUnitySymbols.gradle","RelativeDestinationPath":"shared/keepUnitySymbols.gradle","CanBeModifiedByUser":false},"SharedCommonGradle":{"SourcePath":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/shared/common.gradle","RelativeDestinationPath":"shared/common.gradle","CanBeModifiedByUser":false},"ProjectLevelBuildGradle":{"RelativeDestinationPath":"build.gradle","CanBeModifiedByUser":true},"GradleProperties":{"RelativeDestinationPath":"gradle.properties","CanBeModifiedByUser":true},"UnityProguard":{"SourcePath":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/UnityProGuardTemplate.txt","RelativeDestinationPath":"unityLibrary/proguard-unity.txt","CanBeModifiedByUser":true},"ProguardUser":{"RelativeDestinationPath":"unityLibrary/proguard-user.txt","CanBeModifiedByUser":true},"GradleSettings":{"RelativeDestinationPath":"settings.gradle","CanBeModifiedByUser":true},"LocalProperties":{"RelativeDestinationPath":"local.properties","CanBeModifiedByUser":true}},"AdditionalLibrariesBuildGradlePaths":[],"AdditionalUserOutputs":{"AdditionalManifests":[],"AdditionalBuildGradleFiles":[],"AdditionalGradleSettings":[],"AdditionalGradleProperties":[],"AdditionalFilesWithContents":[]},"AdditionalUserData":[]}}    

