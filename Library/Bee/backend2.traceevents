{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1758794732753970, "dur":49626, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794732803599, "dur":309, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794732803968, "dur":80, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1758794732804048, "dur":114, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794732804169, "dur":13465, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794732817640, "dur":149901, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794732967594, "dur":71, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794732967743, "dur":3123, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1758794732804101, "dur":13550, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732817871, "dur":128, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"../../../../Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer" }}
,{ "pid":12345, "tid":1, "ts":1758794732818000, "dur":155, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"../../../../Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1758794732818156, "dur":85, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer" }}
,{ "pid":12345, "tid":1, "ts":1758794732818241, "dur":253, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":1, "ts":1758794732818494, "dur":224, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":1, "ts":1758794732818718, "dur":200, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":1, "ts":1758794732818970, "dur":105, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity" }}
,{ "pid":12345, "tid":1, "ts":1758794732819102, "dur":1079, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":1, "ts":1758794732820245, "dur":65, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1758794732820360, "dur":61, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1758794732820448, "dur":76, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1758794732820524, "dur":3757, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732824597, "dur":82, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1758794732824765, "dur":138, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1758794732825039, "dur":101, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1758794732825140, "dur":62, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1758794732817653, "dur":7611, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732827259, "dur":181, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll" }}
,{ "pid":12345, "tid":1, "ts":1758794732827442, "dur":224, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" }}
,{ "pid":12345, "tid":1, "ts":1758794732827667, "dur":552, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":1, "ts":1758794732828251, "dur":381, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":1, "ts":1758794732828663, "dur":170, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1758794732828890, "dur":450, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1758794732829396, "dur":131, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1758794732829558, "dur":140, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1758794732829727, "dur":401, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1758794732830155, "dur":226, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":1, "ts":1758794732830382, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":1, "ts":1758794732830470, "dur":305, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1758794732830776, "dur":187, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":1, "ts":1758794732830997, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1758794732825265, "dur":6514, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker /Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/unitylinker_xy1a.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1758794732831779, "dur":263, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732833400, "dur":4214, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"IL2CPP_CodeGen /Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/il2cpp_conv_p0au.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1758794732837614, "dur":3524, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732847276, "dur":293, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Mathematics__11.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732847576, "dur":202, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":1, "ts":1758794732847779, "dur":196, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732847975, "dur":120, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732847268, "dur":827, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4x8f3k51qq4f.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732848174, "dur":163, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Core.Runtime.Shared_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1758794732848345, "dur":97, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732848127, "dur":315, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/txmsm8t0c36w.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732848514, "dur":275, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__57.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732848791, "dur":76, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732848869, "dur":69, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":1, "ts":1758794732848942, "dur":336, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732848488, "dur":791, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ptccmyjdjo3e.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732849431, "dur":189, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__53.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732849625, "dur":87, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":1, "ts":1758794732849757, "dur":170, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732849352, "dur":575, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xaivqqwm6lnd.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732850201, "dur":128, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Mathematics__1.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732850449, "dur":192, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732850196, "dur":446, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7mdff9r32ovd.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732850969, "dur":263, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__111.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732851249, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732850950, "dur":509, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4bkddxh26adq.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732851598, "dur":286, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__2.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732851892, "dur":186, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732851527, "dur":551, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/28n6cr2lkvg4.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732852240, "dur":253, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__76.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732852554, "dur":376, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732852996, "dur":132, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":1, "ts":1758794732853130, "dur":67, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732852147, "dur":1050, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yaqis5memovh.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732853279, "dur":170, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.CoreModule__4.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732853461, "dur":125, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732853204, "dur":382, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/h0pjaixuqzit.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732853738, "dur":118, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIModule_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1758794732853623, "dur":265, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yql6izznhgrw.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732853888, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732854044, "dur":652, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732854739, "dur":125, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732854865, "dur":114, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732854010, "dur":969, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/d043elomzg7m.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732855174, "dur":554, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__1.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732855750, "dur":153, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732855943, "dur":276, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732855102, "dur":1118, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/103psbmngtzo.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732856314, "dur":237, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Core.Runtime__7.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732856559, "dur":55, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732856284, "dur":330, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xg630qy25e3b.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732856726, "dur":130, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppCCalculateFieldValues1.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732856976, "dur":425, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732856686, "dur":715, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r4wqkkuddtph.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732857592, "dur":291, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Universal.Runtime__9.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732857893, "dur":178, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732858072, "dur":356, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732857563, "dur":865, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jrm8fb5vz2hy.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732858581, "dur":185, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__69.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732858775, "dur":281, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732858572, "dur":484, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ybhszg6qtx04.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732859409, "dur":272, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__26.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732859691, "dur":341, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732859371, "dur":661, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mi7fmrdriueb.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732860201, "dur":126, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.GPUDriven.Runtime__1.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732860340, "dur":100, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732860171, "dur":270, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aexpk3uugrko.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732860521, "dur":248, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__14.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732860787, "dur":80, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732860867, "dur":676, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732860447, "dur":1097, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rjavhw7r964s.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732861593, "dur":192, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__3.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732861793, "dur":375, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732861573, "dur":596, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mgheigjg1qwh.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732862343, "dur":394, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__95.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732862768, "dur":122, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732862243, "dur":647, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rzmwt6neoaio.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732863063, "dur":218, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__94.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732863300, "dur":189, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732863019, "dur":470, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6jj8dx9glm64.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732863714, "dur":277, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Burst__1.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732863997, "dur":95, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732863676, "dur":416, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hmr1mfe423ql.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732864307, "dur":86, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.SharedInternalsModule_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1758794732864401, "dur":115, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732864283, "dur":234, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tv33wjtja3xp.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732864692, "dur":324, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__31.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732865043, "dur":296, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732864568, "dur":771, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gm9tjx1j58ll.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732865474, "dur":179, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Universal.Runtime.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732865668, "dur":421, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732866089, "dur":281, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732865470, "dur":900, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/snthugkw8m13.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732866570, "dur":52, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppMetadataUsage.c" }}
,{ "pid":12345, "tid":1, "ts":1758794732866695, "dur":236, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732866421, "dur":511, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xbg8hqhzhbyf.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732867262, "dur":123, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__4.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732867399, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732867225, "dur":378, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7vk2xpxd4e80.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732867686, "dur":233, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Burst__2.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732867960, "dur":596, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732867658, "dur":898, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/21up2os15r24.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732868694, "dur":320, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__10.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732869046, "dur":321, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732868644, "dur":723, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/19obi75nww6f.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732869453, "dur":418, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__1.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732870004, "dur":384, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732869424, "dur":964, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/epcmh55z55lu.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732870639, "dur":150, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppGenericInstDefinitions.c" }}
,{ "pid":12345, "tid":1, "ts":1758794732870817, "dur":193, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732871014, "dur":158, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732870575, "dur":597, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ajif13wjq8q3.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732871259, "dur":364, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.InputSystem__3.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732871630, "dur":71, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":1, "ts":1758794732871703, "dur":149, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732871224, "dur":629, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qyvxr3z2tdpi.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732872008, "dur":193, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Mathematics__2.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732872235, "dur":83, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":1, "ts":1758794732872363, "dur":197, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732872560, "dur":62, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732871935, "dur":687, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s2g3ui605c7w.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732872778, "dur":213, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__72.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732873006, "dur":685, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732872753, "dur":938, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qze9rrxm879j.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732873791, "dur":286, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__77.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732874083, "dur":207, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":1, "ts":1758794732874305, "dur":227, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732873786, "dur":746, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jqt67e1bpc70.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732874550, "dur":653, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__2.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732875207, "dur":75, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":1, "ts":1758794732875321, "dur":166, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732875488, "dur":124, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732874543, "dur":1069, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4lmqyeu91tcy.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732875674, "dur":176, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/System.Xml.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732875901, "dur":56, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732875983, "dur":87, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":1, "ts":1758794732876099, "dur":533, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732875638, "dur":996, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7zhcg1itjppe.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732876805, "dur":75, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.TerrainModule_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1758794732876910, "dur":164, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732877165, "dur":89, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732877254, "dur":104, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732876739, "dur":621, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/len2zxk00ryw.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732877423, "dur":374, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.CoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1758794732877390, "dur":547, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/dyfzvls8dgw8.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732877997, "dur":247, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__8.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732878249, "dur":121, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732878394, "dur":326, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":1, "ts":1758794732878782, "dur":171, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732877966, "dur":987, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f7vby3m5hvty.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732879069, "dur":489, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UI__2.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732879608, "dur":343, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732879033, "dur":918, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/63wj2ps9s752.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732880104, "dur":528, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__39.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732880638, "dur":79, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":1, "ts":1758794732880719, "dur":426, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732880061, "dur":1084, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xc0e5czbl4xn.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732881248, "dur":128, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UnityAnalyticsModule_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1758794732881393, "dur":57, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732881191, "dur":259, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f67nn14t23ez.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732881450, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732881591, "dur":222, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipeline.Universal.ShaderLibrary_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1758794732881939, "dur":142, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":1, "ts":1758794732882082, "dur":145, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732882227, "dur":195, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732881523, "dur":899, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ldmqoin3zdsu.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732882557, "dur":281, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__89.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732882857, "dur":89, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732882462, "dur":484, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cghugrloy7yu.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732883035, "dur":213, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__20.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732883254, "dur":155, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":1, "ts":1758794732883418, "dur":245, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732883019, "dur":644, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p89ogogeoege.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732883793, "dur":243, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.IMGUIModule.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732884059, "dur":81, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732884140, "dur":113, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732883688, "dur":565, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7wv8ox7unjo3.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732884294, "dur":338, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppGenericMethodDefinitions.c" }}
,{ "pid":12345, "tid":1, "ts":1758794732884270, "dur":402, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zul9mfoop6qp.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732884794, "dur":689, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__16.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732885514, "dur":69, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732885583, "dur":60, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732884706, "dur":937, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u5ewzoeic8ie.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732885777, "dur":248, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Collections.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732886065, "dur":319, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732885750, "dur":634, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bs1ymn3o3fmn.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732886599, "dur":125, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__50.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732886736, "dur":404, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732886593, "dur":547, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/q7q2asntw7iv.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732887262, "dur":406, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__18.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732887687, "dur":125, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732887176, "dur":636, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/13u1dti6mf8e.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732888175, "dur":405, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__125.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732888587, "dur":165, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":1, "ts":1758794732888753, "dur":139, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732888153, "dur":740, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jpv5lywanwxi.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732889073, "dur":606, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Core.Runtime__8.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732889689, "dur":97, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732889012, "dur":774, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/iw9rg0c082ev.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732889831, "dur":345, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.IMGUIModule__1.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732890182, "dur":222, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732889794, "dur":610, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wlqxobo1hfxd.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732890547, "dur":229, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__104.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732890782, "dur":108, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":1, "ts":1758794732890894, "dur":194, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732890483, "dur":605, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4cbnk80eekid.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732891264, "dur":245, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.InputSystem__4.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732891517, "dur":76, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732891180, "dur":413, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r6fecjg1xf6x.o" }}
,{ "pid":12345, "tid":1, "ts":1758794732891647, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732891817, "dur":65, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Universal.Runtime__4.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732891883, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732892103, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732892265, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732892397, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732892488, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732892562, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UnityConsentModule.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732892647, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__7.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732892786, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732892902, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732893007, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732893345, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732893454, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732893572, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732893662, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732893753, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732893871, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732893968, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732894029, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__106.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732894146, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732894326, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732894438, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732894532, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732894648, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732894732, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732894911, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732895043, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732895198, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732895271, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__40.cpp" }}
,{ "pid":12345, "tid":1, "ts":1758794732895325, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732895455, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732895744, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732895856, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732895955, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732896120, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732896245, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732896360, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732896452, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732896538, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppToEditorData.json" }}
,{ "pid":12345, "tid":1, "ts":1758794732896593, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732896724, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732896809, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732896925, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732897048, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732897142, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732897246, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732897364, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732897496, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732897622, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732897704, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Symbols/LineNumberMappings.json" }}
,{ "pid":12345, "tid":1, "ts":1758794732897764, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732897882, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732898017, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732898119, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732898193, "dur":14619, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794732912812, "dur":54736, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794732804104, "dur":13569, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794732817678, "dur":539, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794732818217, "dur":1165, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794732819383, "dur":1192, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794732820575, "dur":1240, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794732821815, "dur":743, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794732822558, "dur":2788, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794732825402, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794732825515, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794732825614, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794732825728, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794732825832, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794732825996, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794732827265, "dur":184, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794732827450, "dur":226, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794732827677, "dur":566, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794732828291, "dur":361, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794732828681, "dur":166, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794732828880, "dur":453, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794732829388, "dur":132, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794732829553, "dur":139, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794732829720, "dur":401, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794732830148, "dur":226, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794732830375, "dur":87, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794732830463, "dur":306, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794732830772, "dur":165, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794732830967, "dur":88, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794732826097, "dur":5309, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/Android/boot.config" }}
,{ "pid":12345, "tid":2, "ts":1758794732831553, "dur":72, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794732831847, "dur":71489, "ph":"X", "name": "AddBootConfigGUID",  "args": { "detail":"Library/Bee/artifacts/Android/boot.config" }}
,{ "pid":12345, "tid":2, "ts":1758794732905477, "dur":81, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/boot.config" }}
,{ "pid":12345, "tid":2, "ts":1758794732905474, "dur":84, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/boot.config" }}
,{ "pid":12345, "tid":2, "ts":1758794732905048, "dur":520, "ph":"X", "name": "CopyFiles",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/boot.config" }}
,{ "pid":12345, "tid":2, "ts":1758794732905569, "dur":61955, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732804106, "dur":13576, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732817685, "dur":450, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732818135, "dur":1139, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732819274, "dur":1227, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732820501, "dur":1173, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732821675, "dur":953, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732822629, "dur":2743, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732825378, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732825478, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732825568, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732825684, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732825792, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732825917, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732826070, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/symbols/arm64-v8a/libmain.so" }}
,{ "pid":12345, "tid":3, "ts":1758794732826200, "dur":61, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/symbols/arm64-v8a/libmain.so" }}
,{ "pid":12345, "tid":3, "ts":1758794732826297, "dur":98, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/ids.xml" }}
,{ "pid":12345, "tid":3, "ts":1758794732826395, "dur":93, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/ids.xml" }}
,{ "pid":12345, "tid":3, "ts":1758794732826488, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732826545, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/freeformwindow.xml" }}
,{ "pid":12345, "tid":3, "ts":1758794732826630, "dur":131, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/freeformwindow.xml" }}
,{ "pid":12345, "tid":3, "ts":1758794732826788, "dur":86, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_foreground.png" }}
,{ "pid":12345, "tid":3, "ts":1758794732826874, "dur":112, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_foreground.png" }}
,{ "pid":12345, "tid":3, "ts":1758794732827020, "dur":6387, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732833408, "dur":1374, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ICallRegistrationGenerator /Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/UnityICallRegistration.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732834814, "dur":125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Compile UnityICallRegistration Library/Bee/artifacts/Android/libunity/arm64-v8a/UnityICallRegistration.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732834939, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732835110, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732835175, "dur":305, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1758794732835517, "dur":380, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Collections.LowLevel.ILSupport-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1758794732835942, "dur":281, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1758794732836266, "dur":165, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1758794732836481, "dur":243, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1758794732836725, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732836790, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1758794732837083, "dur":215, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732837303, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.TextMeshPro.pdb" }}
,{ "pid":12345, "tid":3, "ts":1758794732837359, "dur":185, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732837577, "dur":226, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732837808, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.CoreModule.pdb" }}
,{ "pid":12345, "tid":3, "ts":1758794732837912, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732838022, "dur":116, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.pdb" }}
,{ "pid":12345, "tid":3, "ts":1758794732838138, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732838301, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.IMGUIModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1758794732838354, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732838508, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732838613, "dur":92, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AndroidJNIModule.pdb" }}
,{ "pid":12345, "tid":3, "ts":1758794732838705, "dur":274, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732839009, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732839081, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIModule.pdb" }}
,{ "pid":12345, "tid":3, "ts":1758794732839155, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732839238, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PhysicsModule.pdb" }}
,{ "pid":12345, "tid":3, "ts":1758794732839314, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732839433, "dur":183, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732839712, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732839833, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732839958, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732840032, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SharedInternalsModule.pdb" }}
,{ "pid":12345, "tid":3, "ts":1758794732840093, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732840210, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732840285, "dur":6984, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732847322, "dur":381, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__12.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732847710, "dur":140, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":3, "ts":1758794732847855, "dur":72, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732847271, "dur":656, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5vmqh7cw2gcu.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732848094, "dur":182, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine_CodeGen.c" }}
,{ "pid":12345, "tid":3, "ts":1758794732848063, "dur":280, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ubzgm091wiog.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732848406, "dur":306, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.TextCoreTextEngineModule_CodeGen.c" }}
,{ "pid":12345, "tid":3, "ts":1758794732848376, "dur":391, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pmfefhapb52f.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732849063, "dur":285, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.TextCoreTextEngineModule__3.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732849379, "dur":184, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732849035, "dur":528, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/g5a79bapga6m.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732849756, "dur":276, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__21.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732850061, "dur":131, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732850192, "dur":110, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732849708, "dur":594, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/trsi63911phu.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732850384, "dur":295, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__9.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732850680, "dur":197, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732850878, "dur":87, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":3, "ts":1758794732850968, "dur":193, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732850334, "dur":828, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1u9foxy56b1n.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732851313, "dur":194, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/System.Core.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732851534, "dur":730, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732851277, "dur":987, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1icrij047zb8.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732852322, "dur":400, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__20.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732852735, "dur":60, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732852293, "dur":502, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3tdmhlodwye5.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732852802, "dur":89, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3tdmhlodwye5.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732852899, "dur":221, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__116.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732853128, "dur":70, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732852894, "dur":304, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x2yatklrnfa7.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732853284, "dur":96, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__2.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732853391, "dur":58, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732853208, "dur":241, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/z4faxekx8txd.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732853477, "dur":362, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__84.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732853875, "dur":619, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732853459, "dur":1035, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jp2w03hzh31d.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732854558, "dur":373, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__8.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732854942, "dur":81, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732854520, "dur":503, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/dbyvsb2jsm6d.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732855115, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/29zi7y4ebjmg.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732855412, "dur":441, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__87.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732855864, "dur":226, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732855321, "dur":769, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qstpg2qsrh6v.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732856404, "dur":379, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732856790, "dur":110, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":3, "ts":1758794732856942, "dur":106, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732857048, "dur":428, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732856295, "dur":1182, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/z6an9ivo6bom.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732857649, "dur":390, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__21.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732858192, "dur":232, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732857577, "dur":847, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aoddwxubr2rx.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732858706, "dur":106, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.InputForUIModule_CodeGen.c" }}
,{ "pid":12345, "tid":3, "ts":1758794732858815, "dur":98, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732859066, "dur":231, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732858592, "dur":718, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/974f49w0ty6m.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732859438, "dur":179, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__36.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732859624, "dur":88, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":3, "ts":1758794732859743, "dur":107, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732859319, "dur":531, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/q98lpeqibqt5.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732859960, "dur":284, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__36.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732860259, "dur":140, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732859902, "dur":498, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9corzk58ldj2.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732860466, "dur":231, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Assembly-CSharp.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732860711, "dur":52, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732860430, "dur":333, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1q0c5wm3j0a1.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732860813, "dur":121, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Mono.Security.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732861077, "dur":420, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732860784, "dur":713, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/25o9dtjr1kpg.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732861739, "dur":194, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__7.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732861943, "dur":337, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732861711, "dur":569, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ezbvdo66dtcj.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732862280, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732862451, "dur":426, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__1.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732862884, "dur":62, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":3, "ts":1758794732862952, "dur":50, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732862368, "dur":634, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0st9hroku03i.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732863038, "dur":400, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__4.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732863449, "dur":124, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732863014, "dur":559, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/q99uy3o58j5w.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732863767, "dur":426, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Core.Runtime__6.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732864199, "dur":104, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":3, "ts":1758794732864310, "dur":159, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732863654, "dur":815, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/c12flhk52qrs.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732864670, "dur":131, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__18.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732864845, "dur":230, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732865108, "dur":177, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732864567, "dur":718, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gt20zd9djybd.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732865756, "dur":149, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__74.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732865909, "dur":76, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":3, "ts":1758794732865987, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732865737, "dur":465, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cn2dsgsify2i.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732866302, "dur":111, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UnityWebRequestModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732866546, "dur":454, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732866226, "dur":774, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ca48qj518shc.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732867314, "dur":162, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__32.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732867486, "dur":129, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732867237, "dur":379, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/iyxcp00qx977.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732867634, "dur":556, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__11.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732868223, "dur":227, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":3, "ts":1758794732868508, "dur":84, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732868592, "dur":616, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732867628, "dur":1580, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mpszxgklzkiz.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732869320, "dur":259, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__60.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732869600, "dur":66, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732869247, "dur":419, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x9qljfdazciq.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732869944, "dur":359, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__86.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732870312, "dur":71, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":3, "ts":1758794732870384, "dur":79, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732870464, "dur":247, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732869921, "dur":790, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5cq10scvotuc.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732870772, "dur":142, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__4.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732870921, "dur":98, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":3, "ts":1758794732871070, "dur":310, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732870739, "dur":641, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ls3ex3etfkqc.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732871590, "dur":188, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__13.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732871807, "dur":267, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732871542, "dur":532, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8cg4x5eh6aby.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732872247, "dur":305, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppReversePInvokeWrapperTable.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732872563, "dur":134, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732872697, "dur":132, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732872216, "dur":613, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4zop2dr65kxu.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732872872, "dur":203, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__25.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732873125, "dur":240, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732872852, "dur":513, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/izomwbk34idv.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732873365, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732873509, "dur":271, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.InputModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732873792, "dur":132, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732873440, "dur":484, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ahfohfcqfuws.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732873940, "dur":199, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__101.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732874152, "dur":111, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732873935, "dur":328, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x9ju49rl45dv.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732874335, "dur":359, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__7.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732874731, "dur":530, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732874280, "dur":981, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f7lbo839zl8o.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732875489, "dur":135, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__120.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732875640, "dur":551, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732875480, "dur":714, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qyg85atl6r01.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732876313, "dur":713, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__29.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732877063, "dur":468, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732876233, "dur":1299, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aij3rj7thltw.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732877665, "dur":409, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__91.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732878075, "dur":97, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732878202, "dur":170, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":3, "ts":1758794732878397, "dur":91, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732878488, "dur":128, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732877579, "dur":1037, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jobntsymauwq.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732878841, "dur":249, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.TextRenderingModule_CodeGen.c" }}
,{ "pid":12345, "tid":3, "ts":1758794732878740, "dur":442, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fandkq8bpzmc.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732879380, "dur":90, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppUnresolvedIndirectCallStubs.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732879476, "dur":113, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":3, "ts":1758794732879632, "dur":318, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732879323, "dur":627, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7iku3ulizo3i.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732880079, "dur":373, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__13.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732880463, "dur":155, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732880048, "dur":570, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qbqexgrt54nd.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732880723, "dur":269, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.CoreModule__5.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732881028, "dur":135, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732881250, "dur":63, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732881313, "dur":415, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732880715, "dur":1013, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2ft7prbstnuc.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732881850, "dur":454, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__23.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732882340, "dur":375, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732881765, "dur":950, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wb1hwmp7xskw.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732882928, "dur":255, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__44.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732883189, "dur":130, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":3, "ts":1758794732883326, "dur":134, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732882923, "dur":537, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bol0xcl364z1.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732883541, "dur":267, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Core.Runtime__9.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732883816, "dur":156, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732883501, "dur":471, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j4t80ftyc5va.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732884052, "dur":421, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__80.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732884487, "dur":157, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732884644, "dur":477, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732884045, "dur":1078, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r67f5vu840lc.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732885124, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732885297, "dur":522, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Collections__1.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732885829, "dur":368, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732885203, "dur":994, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8msf86j326ug.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732886339, "dur":127, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__35.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732886471, "dur":160, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":3, "ts":1758794732886639, "dur":462, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732886239, "dur":862, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p1o54y9j7ncq.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732887223, "dur":260, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.VFXModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732887484, "dur":199, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732887686, "dur":93, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732887142, "dur":637, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8oyz4mga5ebp.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732887939, "dur":209, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__25.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732888159, "dur":251, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732887931, "dur":480, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/123r6hztf62r.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732888612, "dur":211, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__12.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732888833, "dur":107, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732888591, "dur":349, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o1gi3jjsrl4l.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732889142, "dur":334, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.GPUDriven.Runtime.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732889515, "dur":105, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":3, "ts":1758794732889621, "dur":95, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":3, "ts":1758794732889719, "dur":275, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732889046, "dur":948, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t0rsmtfjqw62.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732890104, "dur":164, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__5.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732890349, "dur":286, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732890059, "dur":576, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9bfzspbfu9km.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732890918, "dur":500, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.TextCoreFontEngineModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732891436, "dur":157, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732890894, "dur":699, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7l8b5cj6psb4.o" }}
,{ "pid":12345, "tid":3, "ts":1758794732891644, "dur":201, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732891848, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Universal.Runtime__2.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732891902, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732892053, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Universal.Runtime__1.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732892112, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732892194, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732892319, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732892420, "dur":192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732892616, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732892722, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732892884, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732893079, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732893269, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732893377, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732893494, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732893622, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732893737, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732893814, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__102.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732893871, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732893971, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732894097, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732894215, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732894347, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732894440, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732894570, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732894678, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732894862, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732894947, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732895060, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732895164, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732895403, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732895498, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732895680, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732895819, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732895985, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__66.cpp" }}
,{ "pid":12345, "tid":3, "ts":1758794732896171, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732896279, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732896386, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732896481, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732896586, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732896686, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732896768, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732896866, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732896993, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732897098, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732897206, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732897307, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732897432, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732897554, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732897717, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732897829, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732897927, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732898061, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732898139, "dur":6724, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732904864, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GuidGenerator /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/unity_app_guid" }}
,{ "pid":12345, "tid":3, "ts":1758794732904943, "dur":74, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794732905285, "dur":59786, "ph":"X", "name": "GuidGenerator",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/unity_app_guid" }}
,{ "pid":12345, "tid":3, "ts":1758794732965141, "dur":2399, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732804109, "dur":13613, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732817727, "dur":922, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732818650, "dur":1232, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732819882, "dur":1163, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732821045, "dur":1369, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732822415, "dur":2880, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732825362, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732825424, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732825504, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732825606, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732825723, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732825823, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732826004, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/java/com/unity3d/player/UnityPlayerGameActivity.java" }}
,{ "pid":12345, "tid":4, "ts":1758794732826100, "dur":152, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/java/com/unity3d/player/UnityPlayerGameActivity.java" }}
,{ "pid":12345, "tid":4, "ts":1758794732826252, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732826321, "dur":82, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v31/styles.xml" }}
,{ "pid":12345, "tid":4, "ts":1758794732826434, "dur":107, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/colors.xml" }}
,{ "pid":12345, "tid":4, "ts":1758794732826555, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon.xml" }}
,{ "pid":12345, "tid":4, "ts":1758794732826654, "dur":130, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon.xml" }}
,{ "pid":12345, "tid":4, "ts":1758794732826854, "dur":133, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":4, "ts":1758794732826987, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732827062, "dur":6378, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732833441, "dur":897, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1758794732834338, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732834418, "dur":513, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1758794732834931, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732834989, "dur":263, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1758794732835252, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732835320, "dur":400, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1758794732835720, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732835786, "dur":294, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1758794732836114, "dur":252, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1758794732836395, "dur":235, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1758794732836630, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732836696, "dur":277, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1758794732836973, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732837030, "dur":471, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/Android/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":4, "ts":1758794732837502, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732837708, "dur":190, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/lib_burst_generated.so" }}
,{ "pid":12345, "tid":4, "ts":1758794732837898, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732837955, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Burst.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758794732838055, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732838188, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.IMGUIModule.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758794732838318, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732838427, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1758794732838496, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732838593, "dur":92, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputForUIModule.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758794732838686, "dur":298, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732839009, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732839083, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AIModule.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758794732839159, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732839259, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TilemapModule.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758794732839332, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732839455, "dur":179, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732839662, "dur":77, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipeline.Universal.ShaderLibrary.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758794732839739, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732839844, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732839961, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732840051, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.Core.Runtime.Shared.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758794732840124, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732840214, "dur":7064, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732847298, "dur":472, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__34.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732847782, "dur":245, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732848027, "dur":354, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732847278, "dur":1103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/np0mzvadfnq3.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732848381, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732848460, "dur":229, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__56.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732848694, "dur":164, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":4, "ts":1758794732848869, "dur":234, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732848441, "dur":662, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4d9n6o5fg0rv.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732849186, "dur":372, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Mathematics__3.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732849562, "dur":131, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732849696, "dur":162, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732849140, "dur":718, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ta1jfymz9eoc.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732850056, "dur":121, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Universal.Runtime__7.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732850191, "dur":110, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732850036, "dur":265, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x3y3fma0scpz.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732850358, "dur":455, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__117.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732850814, "dur":143, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732850968, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732850330, "dur":876, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yajrd01prhrm.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732851282, "dur":155, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__73.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732851445, "dur":78, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732851532, "dur":416, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732851246, "dur":702, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r7ypc7dlcarz.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732852006, "dur":121, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.InputSystem__8.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732852139, "dur":206, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732851972, "dur":373, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jpav7q3raimz.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732852402, "dur":658, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__29.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732853061, "dur":432, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732853745, "dur":462, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732852377, "dur":1830, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tly81pmks8it.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732854208, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732854325, "dur":143, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__58.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732854476, "dur":141, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732854281, "dur":336, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/q1ws6d0bnqku.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732855184, "dur":111, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__32.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732855303, "dur":417, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732855106, "dur":614, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/11ciz6fh8wx4.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732855916, "dur":271, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__66.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732856197, "dur":305, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732855891, "dur":613, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bg96ecvwi5qq.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732856629, "dur":242, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/__Generated_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1758794732856889, "dur":130, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732857020, "dur":129, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732856574, "dur":576, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a26eulqsk8su.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732857223, "dur":253, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Core.Runtime.Shared.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732857618, "dur":74, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732857692, "dur":119, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732857190, "dur":621, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ph8swey6cu1c.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732858208, "dur":79, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732858287, "dur":229, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732857977, "dur":539, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6xyq91rkxq8s.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732858575, "dur":876, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__12.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732859459, "dur":122, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732858569, "dur":1012, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/74vndqyi28ti.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732859710, "dur":351, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.TextCoreTextEngineModule.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732860072, "dur":79, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732860162, "dur":121, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732859699, "dur":584, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xjvsevmcdzo7.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732860393, "dur":468, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__14.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732860881, "dur":71, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732861050, "dur":567, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732860362, "dur":1255, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ap4m38doybxk.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732861889, "dur":129, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UI__3.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732862023, "dur":525, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732861805, "dur":744, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rj07hz66vvhm.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732862730, "dur":438, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__63.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732863183, "dur":95, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732862589, "dur":690, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4ciezdm38qm8.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732863321, "dur":311, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.SpriteShapeModule.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732863644, "dur":390, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732863295, "dur":739, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cts3omlz06mc.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732864283, "dur":649, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.CoreModule__1.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732864943, "dur":51, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732864276, "dur":718, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/axyqujo1gleb.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732865172, "dur":344, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppRgctxTable.c" }}
,{ "pid":12345, "tid":4, "ts":1758794732865088, "dur":447, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/z7k5lsua5gpz.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732865671, "dur":293, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__119.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732865972, "dur":83, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732865663, "dur":392, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/45p38iukxtep.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732866110, "dur":54, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1758794732866281, "dur":73, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":4, "ts":1758794732866357, "dur":63, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732866420, "dur":286, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732866081, "dur":625, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sn3a7kzc6no2.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732866706, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732866802, "dur":252, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__30.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732867064, "dur":131, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732867195, "dur":241, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732866776, "dur":660, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fepdzhll1645.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732867658, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lq0jm1yc11j4.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732867945, "dur":317, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Core.Runtime__5.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732868271, "dur":575, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732867850, "dur":996, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/btiz0136yoad.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732868915, "dur":498, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__7.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732869425, "dur":102, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732868866, "dur":662, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/svu7c2nmpyuu.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732869920, "dur":455, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__112.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732870383, "dur":78, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732870466, "dur":403, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732869901, "dur":968, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5j4txcy0adu5.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732871090, "dur":594, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UnityConsentModule.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732871692, "dur":349, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732871018, "dur":1023, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vi0p83w3r8s5.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732872265, "dur":225, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Mathematics__6.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732872499, "dur":212, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732872717, "dur":138, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732872210, "dur":645, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0dscvwvsjtsu.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732873051, "dur":330, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__19.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732873529, "dur":259, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732873008, "dur":781, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mjypt2w3p3jy.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732873946, "dur":331, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.TextCoreTextEngineModule__1.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732874397, "dur":160, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732873940, "dur":618, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aa4uethuayik.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732874700, "dur":387, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__93.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732875097, "dur":244, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732874635, "dur":706, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5bzrthtihkpp.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732875515, "dur":215, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__16.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732875739, "dur":323, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732875492, "dur":570, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/d7xzhl2ngxxr.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732876135, "dur":477, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__9.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732876638, "dur":98, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732876100, "dur":636, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0mzzdd12kj5h.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732876957, "dur":288, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Mathematics__7.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732877260, "dur":131, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732876951, "dur":440, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ltv0a69pmh4d.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732877487, "dur":287, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__100.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732877825, "dur":337, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732877438, "dur":724, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cug6o0do1h2b.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732878263, "dur":96, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.PropertiesModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1758794732878255, "dur":147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/l1vvewd9lg2c.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732878654, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ohnagimwtadq.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732878809, "dur":577, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.InputSystem.ForUI.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732879390, "dur":80, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732878737, "dur":733, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6tj8avqhmsmv.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732879603, "dur":424, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__23.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732880170, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732879569, "dur":812, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ffgn6s4x8fat.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732880829, "dur":249, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.VRModule.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732881086, "dur":398, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732880800, "dur":684, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aldop8yhkavo.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732881617, "dur":451, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__33.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732882081, "dur":72, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732882153, "dur":244, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732881532, "dur":865, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3d1049eo0xjr.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732882397, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732882528, "dur":250, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppCCalculateTypeValues.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732882794, "dur":214, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":4, "ts":1758794732883018, "dur":139, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732882493, "dur":664, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mrnzj4zknblz.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732883502, "dur":381, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__17.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732883892, "dur":130, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732883396, "dur":626, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0valtd27idrz.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732884314, "dur":293, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.InputSystem__2.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732884645, "dur":471, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732884274, "dur":842, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5is9bul5qpdz.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732885255, "dur":246, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UI_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1758794732885508, "dur":238, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":4, "ts":1758794732885749, "dur":138, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732885152, "dur":736, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/y3eiz4hg7nkz.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732885965, "dur":266, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__34.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732886350, "dur":257, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":4, "ts":1758794732886612, "dur":365, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732885932, "dur":1045, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4llhppvm4z06.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732887093, "dur":312, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Universal.Runtime__4.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732887421, "dur":89, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732887044, "dur":466, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/q07a0n16iykh.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732887703, "dur":137, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__59.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732887855, "dur":95, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732887690, "dur":260, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bh0gmne9pewd.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732888227, "dur":97, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.SubsystemsModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1758794732888167, "dur":193, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yys0oizdh97s.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732888501, "dur":492, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.TextMeshPro__7.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732889004, "dur":283, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732888462, "dur":826, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4dq1ygd3rb0h.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732889362, "dur":248, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__65.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732889615, "dur":180, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":4, "ts":1758794732889798, "dur":470, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732889330, "dur":938, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/q05iz2gzfhf7.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732890347, "dur":216, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.InputSystem__6.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732890569, "dur":68, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":4, "ts":1758794732890638, "dur":51, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732890297, "dur":392, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x1mq718v3rt0.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732890704, "dur":172, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.CoreModule__2.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732890889, "dur":249, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732890699, "dur":439, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vc2eat2yj9pu.o" }}
,{ "pid":12345, "tid":4, "ts":1758794732891138, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732891442, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732891524, "dur":339, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732891869, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732892045, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732892231, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732892432, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732892770, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732892902, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732892996, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732893115, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732893199, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732893266, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.Core.Runtime__7.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732893320, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732893434, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732893505, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1758794732893556, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732893666, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732893777, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732893915, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732894039, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732894149, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732894267, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732894369, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1758794732894419, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732894581, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732894769, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732894967, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732895138, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732895238, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732895337, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732895474, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732895572, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732895636, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__82.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732895697, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732895800, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732895913, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732896017, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732896188, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732896299, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732896504, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732896569, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__8.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732896689, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732896773, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732896848, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__7.cpp" }}
,{ "pid":12345, "tid":4, "ts":1758794732896900, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732897016, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732897116, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732897217, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732897329, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732897449, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732897611, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732897717, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732897806, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732897908, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732898030, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794732898098, "dur":71, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Managed/Metadata/global-metadata.dat" }}
,{ "pid":12345, "tid":4, "ts":1758794732898241, "dur":69274, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732804119, "dur":13642, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732817764, "dur":1109, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732818874, "dur":1218, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732820092, "dur":1142, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732821235, "dur":1340, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732822575, "dur":2735, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732825355, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732825421, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732825498, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732825598, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732825706, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732825811, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732826476, "dur":885, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/IntermediateLauncherManifestDiag.txt_4oyd.info" }}
,{ "pid":12345, "tid":5, "ts":1758794732827361, "dur":6047, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732833439, "dur":431, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1758794732833911, "dur":598, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1758794732834555, "dur":679, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1758794732835269, "dur":349, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1758794732835671, "dur":372, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1758794732836086, "dur":361, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1758794732836448, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732836568, "dur":240, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1758794732836808, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732836881, "dur":205, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1758794732837152, "dur":192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732837366, "dur":187, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732837638, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732837720, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732837835, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794732837894, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732838034, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.Burst.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794732838101, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732838249, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AudioModule.pdb" }}
,{ "pid":12345, "tid":5, "ts":1758794732838371, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732838560, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732838739, "dur":252, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732839017, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732839151, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732839267, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732839384, "dur":238, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732839627, "dur":81, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.HierarchyCoreModule.pdb" }}
,{ "pid":12345, "tid":5, "ts":1758794732839708, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732839775, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreFontEngineModule.pdb" }}
,{ "pid":12345, "tid":5, "ts":1758794732839854, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732839982, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732840070, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/analytics.json" }}
,{ "pid":12345, "tid":5, "ts":1758794732840144, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732840233, "dur":7052, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732847342, "dur":499, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__1.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732847853, "dur":128, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732847981, "dur":196, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732847286, "dur":891, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3d29dzdbyrvi.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732848285, "dur":331, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Universal.Runtime__2.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732848631, "dur":82, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732848210, "dur":503, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4ck6zy6ac8ri.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732848732, "dur":296, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__48.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732849038, "dur":335, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732848729, "dur":644, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jzw9xqavkllz.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732849743, "dur":226, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.InputSystem__11.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732849970, "dur":216, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732850195, "dur":141, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732849720, "dur":616, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vgy4ltxabr7q.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732850337, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732850439, "dur":500, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__33.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732850940, "dur":85, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732851063, "dur":184, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":5, "ts":1758794732851249, "dur":170, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732850410, "dur":1009, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mhg6eguebl9n.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732851565, "dur":647, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732852248, "dur":309, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732851525, "dur":1032, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ya9owz419kyc.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732852653, "dur":336, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.TextMeshPro__1.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732853023, "dur":100, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732853127, "dur":71, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732852586, "dur":612, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/q77f3jvg8irh.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732853709, "dur":565, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__37.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732854283, "dur":377, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732853504, "dur":1156, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/usrffuj6o1ul.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732854869, "dur":211, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/System__2.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732855136, "dur":462, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732854853, "dur":746, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qal9tse8fd6c.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732855888, "dur":382, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__30.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732856283, "dur":294, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732855870, "dur":707, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/waa8ob6uqjf9.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732856697, "dur":275, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__55.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732856978, "dur":396, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732856674, "dur":700, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jy5zqfridcgc.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732857389, "dur":414, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.InputSystem__7.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732857805, "dur":164, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732857975, "dur":91, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732857382, "dur":684, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/y966rhpu7tag.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732858120, "dur":152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hclgh07tibqy.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732858347, "dur":140, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.CoreModule.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732858497, "dur":89, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732858587, "dur":261, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732858317, "dur":531, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x81q4l4dwhen.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732858931, "dur":353, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__110.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732859299, "dur":185, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732858899, "dur":586, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jwgpxn343m16.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732859769, "dur":355, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__15.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732860135, "dur":160, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732859743, "dur":553, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9pb8jf0k9j7u.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732860961, "dur":733, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__19.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732861715, "dur":280, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732860941, "dur":1054, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3fjkjwhdfgu1.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732862052, "dur":173, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.InputLegacyModule.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732862229, "dur":510, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732862020, "dur":719, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vqkufcbe5n49.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732862809, "dur":130, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__109.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732862949, "dur":232, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732863181, "dur":82, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732862772, "dur":492, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x3pk0v43uofu.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732863683, "dur":576, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Core.Runtime.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732864342, "dur":143, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732864485, "dur":300, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732863646, "dur":1139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4rf8swd1cpon.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732865124, "dur":340, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__107.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732865481, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732865074, "dur":461, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bg2f0697nymv.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732865727, "dur":112, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__45.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732865862, "dur":228, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732866215, "dur":50, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732866265, "dur":343, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732865670, "dur":938, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w3oodmz09sbl.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732866678, "dur":507, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__43.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732867192, "dur":304, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732866642, "dur":854, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/q8hci3653goi.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732867792, "dur":67, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732867860, "dur":185, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732867636, "dur":409, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/dizyll7b7w0i.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732868133, "dur":310, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__106.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732868482, "dur":97, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":5, "ts":1758794732868645, "dur":430, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732868071, "dur":1004, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/q0yrkxvalabc.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732869155, "dur":336, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__24.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732869498, "dur":113, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732869611, "dur":58, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732869098, "dur":571, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hr45bw340gwf.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732869993, "dur":450, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__75.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732870458, "dur":143, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732869926, "dur":675, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x2517p6fime2.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732870671, "dur":308, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__20.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732870987, "dur":147, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732870617, "dur":517, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8bpuquv4y15a.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732871188, "dur":337, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__97.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732871536, "dur":50, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732871168, "dur":418, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/dna1tviav0an.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732871845, "dur":181, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__19.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732872034, "dur":165, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732871806, "dur":393, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/h1mhktpcn0ss.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732872333, "dur":83, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppGenericAdjustorThunkTable.c" }}
,{ "pid":12345, "tid":5, "ts":1758794732872295, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kss1ui8c7aow.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732872692, "dur":157, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__22.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732872856, "dur":145, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732873001, "dur":341, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732872686, "dur":657, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uzczqjq05ry9.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732873483, "dur":518, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__11.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732874011, "dur":142, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732874153, "dur":112, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732873390, "dur":875, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/h7gro7cuwhrt.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732874356, "dur":255, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipeline.Universal.ShaderLibrary.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732874623, "dur":508, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732874285, "dur":846, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hd8pb7sj6iw1.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732875164, "dur":312, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Universal.Runtime__3.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732875578, "dur":79, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732875657, "dur":284, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732875140, "dur":801, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5k4mken9fa9x.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732876026, "dur":67, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/System.Configuration_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1758794732876103, "dur":55, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732875986, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ezp6ljfwdhj1.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732876265, "dur":893, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__6.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732877164, "dur":905, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732876197, "dur":1872, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w4g7tmh0ewsz.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732878249, "dur":182, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Core.Runtime__3.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732878445, "dur":69, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732878176, "dur":338, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p5uu0c9asyhe.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732878527, "dur":119, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.VRModule_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1758794732878650, "dur":66, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":5, "ts":1758794732878790, "dur":74, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732878522, "dur":342, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yc8q6ahi55w2.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732878924, "dur":91, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.InputForUIModule.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732879045, "dur":53, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732879143, "dur":103, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":5, "ts":1758794732879248, "dur":359, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732878879, "dur":728, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9ebfhs3rhsl8.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732879764, "dur":138, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Assembly-CSharp_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1758794732879922, "dur":118, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732880049, "dur":75, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732879631, "dur":493, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tt8wutld9azp.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732880289, "dur":54, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.InputModule_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1758794732880357, "dur":90, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732880152, "dur":295, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mvjws8le9e6b.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732880464, "dur":330, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__17.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732880857, "dur":254, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732880458, "dur":654, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vdzck4ugga0t.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732881215, "dur":136, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Core.Runtime_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1758794732881359, "dur":87, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732881166, "dur":281, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/osq7cdxy6hoi.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732881484, "dur":193, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4ks380hc1tav.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732881800, "dur":413, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__13.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732882248, "dur":92, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732882343, "dur":427, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732881755, "dur":1015, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p90ynzzqvnl1.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732882788, "dur":192, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__68.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732882981, "dur":72, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732883058, "dur":122, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732882781, "dur":399, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x3wbf1pspvjp.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732883214, "dur":167, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__82.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732883391, "dur":415, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732883191, "dur":615, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x1es0en3ad9e.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732883833, "dur":297, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.PhysicsModule.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732884165, "dur":88, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732883814, "dur":439, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zmj9a83w776h.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732884278, "dur":269, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__15.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732884554, "dur":87, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":5, "ts":1758794732884643, "dur":411, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732884273, "dur":781, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/czdjx5lpcvgc.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732885054, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732885171, "dur":538, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__5.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732885727, "dur":216, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732885117, "dur":826, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6762cdtqi81v.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732885943, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732886131, "dur":280, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.TextRenderingModule.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732886412, "dur":188, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732886603, "dur":203, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732886037, "dur":770, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3jj4bl45dgab.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732886807, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732886965, "dur":579, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Universal.Runtime__1.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732887562, "dur":68, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732886873, "dur":757, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jxgikpsd9jgz.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732887873, "dur":123, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__3.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732887998, "dur":84, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732888090, "dur":61, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732887850, "dur":301, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/norus0gjnjsn.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732888289, "dur":236, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__16.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732888525, "dur":81, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732888628, "dur":301, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":5, "ts":1758794732888932, "dur":124, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":5, "ts":1758794732889056, "dur":408, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732888163, "dur":1301, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fg7wd4nagpfg.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732889890, "dur":575, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.InputSystem.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732890474, "dur":177, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732889800, "dur":851, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fwjzvb94vps0.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732890806, "dur":263, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__38.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732891082, "dur":216, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732890800, "dur":498, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f8u3u3x3rp64.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732891437, "dur":180, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.PhysicsModule_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1758794732891710, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732891431, "dur":333, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/b9f294t3spyy.o" }}
,{ "pid":12345, "tid":5, "ts":1758794732891841, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732891982, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732892153, "dur":164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732892322, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UnityAnalyticsModule.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732892379, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732892485, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732892591, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732892705, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732892760, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732892862, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732893075, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732893180, "dur":188, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732893398, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732893520, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732893642, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732893743, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732893819, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__122.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732893871, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732893965, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732894156, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732894264, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732894376, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732894469, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__4.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732894607, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732894728, "dur":187, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732894959, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732895108, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732895215, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732895340, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732895449, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732895555, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732895634, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__92.cpp" }}
,{ "pid":12345, "tid":5, "ts":1758794732895685, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732895800, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732895913, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732896127, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Symbols/il2cppFileRoot.txt" }}
,{ "pid":12345, "tid":5, "ts":1758794732896197, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732896311, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732896418, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732896538, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732896642, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732896757, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732896850, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732896967, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732897079, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732897188, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732897305, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732897416, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732897541, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732897628, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732897749, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732897859, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732898000, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732898096, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732898161, "dur":14634, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794732912819, "dur":54717, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732804126, "dur":13642, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732817771, "dur":1123, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732818894, "dur":1224, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732820119, "dur":1148, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732821268, "dur":1309, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732822577, "dur":2708, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732825316, "dur":60, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt_9rh7.info" }}
,{ "pid":12345, "tid":6, "ts":1758794732825377, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732825473, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732825561, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732825673, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732825781, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732825926, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732826080, "dur":996, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ActionGenerateProjectFiles Library/Bee/artifacts/Android/IntermediateFiles.txt (+45 others)" }}
,{ "pid":12345, "tid":6, "ts":1758794732827118, "dur":256, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732827379, "dur":6064, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732833444, "dur":671, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1758794732834175, "dur":661, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Burst-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1758794732834887, "dur":258, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1758794732835145, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732835210, "dur":77, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Generate Library/Bee/artifacts/Android/libunity/arm64-v8a/libunity.dbg.so from Library/Bee/artifacts/Android/libunity/arm64-v8a/unstripped/libunity.so Library/Bee/artifacts/Android/libunity/arm64-v8a/libunity.dbg.so" }}
,{ "pid":12345, "tid":6, "ts":1758794732835287, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732835354, "dur":791, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1758794732836145, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732836216, "dur":220, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1758794732836493, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1758794732836677, "dur":293, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1758794732837005, "dur":97, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Stripping Library/Bee/artifacts/Android/libunity/arm64-v8a/unstripped/libunity.so -> Library/Bee/artifacts/Android/libunity/arm64-v8a/stripped/libunity.so Library/Bee/artifacts/Android/libunity/arm64-v8a/stripped/libunity.so" }}
,{ "pid":12345, "tid":6, "ts":1758794732837102, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732837187, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732837336, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758794732837449, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputLegacyModule.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758794732837499, "dur":274, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732837815, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732837973, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732838102, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VFXModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1758794732838159, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732838284, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputModule.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758794732838383, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732838502, "dur":63, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityConsentModule.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758794732838565, "dur":172, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732838775, "dur":185, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732838963, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SubsystemsModule.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758794732839033, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732839086, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VRModule.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758794732839163, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732839251, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TerrainModule.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758794732839321, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732839461, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732839627, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.Core.Runtime.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758794732839681, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732839792, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732839904, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732840084, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732840160, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.GPUDriven.Runtime.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758794732840240, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732840305, "dur":6972, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732847307, "dur":603, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__40.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732847911, "dur":148, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732848066, "dur":491, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732847277, "dur":1280, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j5qj24xcu2j0.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732848890, "dur":213, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__35.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732849141, "dur":382, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732848866, "dur":658, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/843af5oaib2m.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732849694, "dur":420, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppCCalculateFieldValues2.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732850115, "dur":219, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732850412, "dur":325, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732849686, "dur":1051, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sch74zachv6w.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732850967, "dur":184, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.TerrainModule.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732851158, "dur":354, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732850944, "dur":568, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xq51l4unr2kw.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732851634, "dur":174, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.HierarchyCoreModule.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732851812, "dur":78, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732851551, "dur":339, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sjdifdv24h4m.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732852221, "dur":340, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Collections.LowLevel.ILSupport.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732852588, "dur":141, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732852735, "dur":60, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732852144, "dur":651, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bta8pd4lmxxn.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732852962, "dur":322, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.GridModule.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732853295, "dur":59, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732852920, "dur":434, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/83vqt5humsm0.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732853552, "dur":78, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732853478, "dur":153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/buys1mfqcy2h.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732853631, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732853834, "dur":574, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__3.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732854408, "dur":64, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732854476, "dur":182, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732853776, "dur":882, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p1peeo864hoh.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732854892, "dur":119, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.TextMeshPro__4.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732855017, "dur":88, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":6, "ts":1758794732855172, "dur":334, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732854845, "dur":661, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xaz8ii49hxvz.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732855575, "dur":219, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Core.Runtime__1.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732855798, "dur":90, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":6, "ts":1758794732855893, "dur":327, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732855540, "dur":680, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jaoi5tggmmub.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732856364, "dur":107, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppTypeDefinitions.c" }}
,{ "pid":12345, "tid":6, "ts":1758794732856299, "dur":214, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/52o8co87lgj5.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732856895, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/so64ncx3lx7t.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732857128, "dur":229, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__17.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732857366, "dur":198, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732857065, "dur":500, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ym1nxugljl9a.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732857565, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732857710, "dur":245, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__9.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732857969, "dur":92, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732857665, "dur":396, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/377z9kqtoaxh.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732858192, "dur":366, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UI.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732858601, "dur":246, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732858092, "dur":755, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yui3bwoxum26.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732858996, "dur":169, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__51.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732859179, "dur":59, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732858939, "dur":299, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bnuqq11qzvue.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732859819, "dur":169, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Mathematics.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732859997, "dur":173, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":6, "ts":1758794732860175, "dur":179, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732859756, "dur":599, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gj10zxafvnjo.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732860384, "dur":186, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__11.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732860578, "dur":86, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732860363, "dur":302, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3lduna5uoxs2.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732860796, "dur":772, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__7.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732861582, "dur":134, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":6, "ts":1758794732861739, "dur":67, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732861806, "dur":413, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732860777, "dur":1443, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xc1ne2xyiy9e.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732862221, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732862303, "dur":804, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__17.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732863108, "dur":266, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732863378, "dur":171, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732863549, "dur":95, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732862275, "dur":1369, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hsvfj7mh8p12.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732863744, "dur":165, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__121.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732863924, "dur":167, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732863669, "dur":422, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r60oqq9jauhh.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732864291, "dur":114, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1jrmtf1c5gxp.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732864600, "dur":271, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.InputSystem__1.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732864872, "dur":218, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732865167, "dur":584, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":6, "ts":1758794732865757, "dur":99, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732864483, "dur":1373, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4a8sre56mnwj.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732865977, "dur":102, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UnityAnalyticsCommonModule.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732866085, "dur":607, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732865971, "dur":721, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2ixyprkfig9v.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732866746, "dur":376, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppInvokerTable.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732867127, "dur":107, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":6, "ts":1758794732867290, "dur":208, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732866716, "dur":783, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/veoj3lmtx90t.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732867721, "dur":119, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__18.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732867854, "dur":70, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732867924, "dur":440, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732867683, "dur":683, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i0fw3m3fcsii.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732868525, "dur":108, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UnityAnalyticsCommonModule_CodeGen.c" }}
,{ "pid":12345, "tid":6, "ts":1758794732868671, "dur":178, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732868460, "dur":389, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6q31mahz81c5.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732868930, "dur":856, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__27.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732869795, "dur":61, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732868884, "dur":972, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4mcrw0ezb95t.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732869947, "dur":286, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.Physics2DModule.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732870268, "dur":187, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":6, "ts":1758794732870458, "dur":327, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732869914, "dur":871, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lqmkp02zocw4.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732871029, "dur":298, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732871336, "dur":385, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732871722, "dur":197, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732871009, "dur":910, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uu4f86odsid2.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732871989, "dur":291, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__96.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732872291, "dur":168, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732871961, "dur":498, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cfqk9f2bryt7.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732872698, "dur":234, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.TextMeshPro__5.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732872933, "dur":85, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732873117, "dur":367, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732872692, "dur":793, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/iq2wyri7kl7i.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732873565, "dur":365, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__10.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732873944, "dur":64, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732874008, "dur":72, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732873529, "dur":551, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j8ylhii0vqjv.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732874285, "dur":199, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ww1s4ft0lpw4.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732874650, "dur":165, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Mathematics_CodeGen.c" }}
,{ "pid":12345, "tid":6, "ts":1758794732874833, "dur":93, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732874624, "dur":302, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gkboiaypmfzr.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732874926, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732875085, "dur":191, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Burst.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732875300, "dur":203, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732875561, "dur":89, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":6, "ts":1758794732875673, "dur":107, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732875781, "dur":571, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732875001, "dur":1351, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m0gfdka5nnwy.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732876457, "dur":172, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.JSONSerializeModule_CodeGen.c" }}
,{ "pid":12345, "tid":6, "ts":1758794732876638, "dur":66, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732876383, "dur":321, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8tjzddmuanfq.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732876817, "dur":130, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.TextMeshPro__3.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732876959, "dur":1070, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732876748, "dur":1281, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cvvk38pcf9jg.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732878270, "dur":333, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__47.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732878655, "dur":640, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732878177, "dur":1119, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/irctdbuwgj4j.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732879563, "dur":400, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__46.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732879970, "dur":83, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":6, "ts":1758794732880082, "dur":163, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732879474, "dur":773, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xb94x1fzdvs0.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732880247, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732880412, "dur":283, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__16.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732880712, "dur":267, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732880359, "dur":620, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wcsr3x8j51pj.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732881041, "dur":113, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.PropertiesModule.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732881255, "dur":50, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":6, "ts":1758794732881308, "dur":53, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732881361, "dur":530, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732881012, "dur":879, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/14dud4pe82vv.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732882019, "dur":58, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732881926, "dur":174, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/n928dgrjfyq1.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732882247, "dur":80, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.AudioModule_CodeGen.c" }}
,{ "pid":12345, "tid":6, "ts":1758794732882380, "dur":117, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732882217, "dur":280, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m2th491bvgym.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732882497, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732882612, "dur":113, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppMetadataRegistration.c" }}
,{ "pid":12345, "tid":6, "ts":1758794732882745, "dur":108, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732882869, "dur":148, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":6, "ts":1758794732882573, "dur":472, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fnhejok5xwza.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732883049, "dur":74, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fnhejok5xwza.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732883357, "dur":140, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__113.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732883508, "dur":309, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732883325, "dur":492, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qz7hdmcyd15o.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732884115, "dur":141, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/System__1.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732884314, "dur":176, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":6, "ts":1758794732884492, "dur":123, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732884616, "dur":242, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732884051, "dur":807, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5uhlej0adput.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732884976, "dur":591, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.CoreModule__3.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732885572, "dur":146, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":6, "ts":1758794732885724, "dur":349, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732884889, "dur":1185, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wkmvu8ixnb79.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732886169, "dur":356, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.TextMeshPro__2.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732886526, "dur":97, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732886631, "dur":599, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732886104, "dur":1126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bnb4it9db720.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732887230, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732887567, "dur":355, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__108.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732887935, "dur":93, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732887551, "dur":478, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/comwkmq6r03a.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732888218, "dur":448, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__14.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732888679, "dur":59, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732888191, "dur":547, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/oa8j45ln6vvb.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732888783, "dur":132, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppCodeRegistration.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732888927, "dur":246, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732888750, "dur":423, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ltxir6svvg3p.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732889173, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732889282, "dur":257, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.InputSystem__5.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732889549, "dur":160, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":6, "ts":1758794732889709, "dur":124, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732889244, "dur":589, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cmj2rsuy03hh.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732889954, "dur":95, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Mathematics__4.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732890054, "dur":386, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732889896, "dur":544, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/eq47u70wc3zv.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732890522, "dur":172, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__42.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732890712, "dur":94, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732890475, "dur":331, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p0xvxnq7ze72.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732890936, "dur":406, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__118.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732891353, "dur":63, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732890897, "dur":519, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jqmft9fnu525.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732891449, "dur":147, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppCCalculateTypeValues1.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732891648, "dur":248, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732891434, "dur":462, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p9v98gd8vrb4.o" }}
,{ "pid":12345, "tid":6, "ts":1758794732891951, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732892120, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Managed/Resources/mscorlib.dll-resources.dat" }}
,{ "pid":12345, "tid":6, "ts":1758794732892182, "dur":81, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Managed/Resources/mscorlib.dll-resources.dat" }}
,{ "pid":12345, "tid":6, "ts":1758794732892295, "dur":68, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__15.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732892363, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732892455, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AudioModule_CodeGen.c" }}
,{ "pid":12345, "tid":6, "ts":1758794732892510, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732892615, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732892849, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732892966, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732893041, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UnityWebRequestModule.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732893173, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732893264, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732893370, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732893484, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732893611, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732893726, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732893838, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732893929, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__111.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732893985, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732894162, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732894322, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732894386, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__5.cpp" }}
,{ "pid":12345, "tid":6, "ts":1758794732894528, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732894635, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732894726, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732895020, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732895150, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732895405, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732895521, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732895738, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732895919, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732896113, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732896215, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732896317, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732896432, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732896539, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732896638, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732896728, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732896831, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732896936, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732897002, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/analytics.json" }}
,{ "pid":12345, "tid":6, "ts":1758794732897072, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732897172, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732897284, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732897422, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732897551, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732897812, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732897921, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732898035, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794732898203, "dur":69343, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794732804135, "dur":13641, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794732817778, "dur":1110, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794732818889, "dur":1210, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794732820099, "dur":1132, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794732821232, "dur":1350, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794732822582, "dur":2712, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794732825295, "dur":87124, "ph":"X", "name": "ComputeLeafInputSignature",  "args": { "detail":"Lib_Android_arm64 Library/Bee/artifacts/Android/87lik/il2cpp.a" }}
,{ "pid":12345, "tid":7, "ts":1758794732912432, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Link_Android_arm64 Library/Bee/artifacts/Android/d8kzr/libil2cpp.so" }}
,{ "pid":12345, "tid":7, "ts":1758794732912577, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794732912798, "dur":54731, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732804147, "dur":13638, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732817788, "dur":1120, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732818908, "dur":1223, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732820132, "dur":1178, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732821310, "dur":1290, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732822600, "dur":2702, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732825334, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732825395, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732825496, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732825589, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732825718, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732825840, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732825993, "dur":116, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/libs/unity-classes.jar" }}
,{ "pid":12345, "tid":8, "ts":1758794732826109, "dur":121, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/libs/unity-classes.jar" }}
,{ "pid":12345, "tid":8, "ts":1758794732826308, "dur":124, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v21/styles.xml" }}
,{ "pid":12345, "tid":8, "ts":1758794732826469, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":8, "ts":1758794732826571, "dur":116, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":8, "ts":1758794732826713, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_background.png" }}
,{ "pid":12345, "tid":8, "ts":1758794732826806, "dur":171, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_background.png" }}
,{ "pid":12345, "tid":8, "ts":1758794732827014, "dur":6386, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732833401, "dur":468, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ClassRegistrationGenerator /Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/UnityClassRegistration.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732833921, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Compile UnityClassRegistration Library/Bee/artifacts/Android/libunity/arm64-v8a/UnityClassRegistration.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732834005, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732834085, "dur":700, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1758794732834845, "dur":165, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1758794732835038, "dur":275, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.VFXModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1758794732835313, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732835420, "dur":196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1758794732835649, "dur":429, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.GPUDriven.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1758794732836106, "dur":195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Collections-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1758794732836301, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732836370, "dur":208, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime.Shared-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1758794732836616, "dur":186, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1758794732836838, "dur":315, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityConsentModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1758794732837154, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732837318, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732837479, "dur":306, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732837787, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.GridModule.pdb" }}
,{ "pid":12345, "tid":8, "ts":1758794732837882, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732838036, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732838172, "dur":134, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VFXModule.pdb" }}
,{ "pid":12345, "tid":8, "ts":1758794732838306, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732838406, "dur":81, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AudioModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1758794732838487, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732838590, "dur":71, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityAnalyticsModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1758794732838661, "dur":306, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732838999, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732839095, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.XRModule.pdb" }}
,{ "pid":12345, "tid":8, "ts":1758794732839168, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732839305, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732839381, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.HierarchyCoreModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1758794732839431, "dur":200, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732839687, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732839763, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreTextEngineModule.pdb" }}
,{ "pid":12345, "tid":8, "ts":1758794732839829, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732839976, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732840085, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732840193, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732840275, "dur":7016, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732847356, "dur":279, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__37.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732847641, "dur":338, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732847982, "dur":197, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732847291, "dur":888, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bpca5n4fee5o.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732848309, "dur":239, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__114.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732848558, "dur":110, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732848221, "dur":447, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r7ryx1swh3m4.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732848751, "dur":176, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__3.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732848937, "dur":402, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732848730, "dur":611, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0c0qhu0vcwet.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732849391, "dur":290, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.TextCoreTextEngineModule__2.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732849697, "dur":186, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732849372, "dur":511, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vp7iu2vrdmu3.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732850075, "dur":136, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.GPUDriven.Runtime_CodeGen.c" }}
,{ "pid":12345, "tid":8, "ts":1758794732850223, "dur":191, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732850051, "dur":363, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hgnwtelx1rag.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732850414, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732850504, "dur":369, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__10.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732850879, "dur":87, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732850969, "dur":191, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732850474, "dur":686, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i69570qxl9gj.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732851271, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cwk7ypysm028.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732851580, "dur":467, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.TextMeshPro__6.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732852057, "dur":323, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732851527, "dur":853, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jymdj7y5onoy.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732852461, "dur":192, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__49.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732852662, "dur":267, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732853000, "dur":479, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732853479, "dur":332, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732852426, "dur":1385, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4e0xd0osn9wi.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732853811, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732854086, "dur":81, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732853870, "dur":298, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/q5djvaz25n1u.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732854460, "dur":169, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__79.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732854634, "dur":232, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732854867, "dur":231, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732855165, "dur":190, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732854291, "dur":1064, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5dhb83v7v2zz.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732855355, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732855479, "dur":155, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__52.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732855642, "dur":72, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732855437, "dur":277, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cve7ahip3xbu.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732855936, "dur":176, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__103.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732856136, "dur":162, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732856331, "dur":59, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732856427, "dur":477, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732856904, "dur":290, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732855892, "dur":1303, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jx7y4ymhbw6t.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732857277, "dur":276, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Mathematics__9.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732857567, "dur":317, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732857244, "dur":641, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7gj5av4lt7wc.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732858149, "dur":239, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Mathematics__8.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732858438, "dur":156, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732858595, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732858072, "dur":738, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m1fhvlqoqjlt.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732858974, "dur":126, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__13.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732859120, "dur":211, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732859335, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732858920, "dur":631, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/n2n3jq5o2tew.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732859736, "dur":156, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__8.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732859903, "dur":145, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732859702, "dur":346, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gf6r8nk83np4.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732860153, "dur":359, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__98.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732860513, "dur":68, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732860583, "dur":297, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732860900, "dur":54, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732860954, "dur":516, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732860146, "dur":1324, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/y2dp94w7xom6.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732861710, "dur":380, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__2.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732862125, "dur":428, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732861704, "dur":850, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/l8wxx02hxof2.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732862554, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732862664, "dur":343, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.TilemapModule.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732863018, "dur":131, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732862617, "dur":532, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u50y8pe8i2gf.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732863340, "dur":197, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__90.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732863551, "dur":93, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732863297, "dur":347, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/y87ydjkp75k7.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732863726, "dur":117, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule_CodeGen.c" }}
,{ "pid":12345, "tid":8, "ts":1758794732863852, "dur":69, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732863653, "dur":304, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xo4dbmi6pfyz.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732863957, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732864332, "dur":217, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__81.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732864606, "dur":299, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732864304, "dur":601, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cmb4k5857oxv.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732865080, "dur":225, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__8.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732865314, "dur":433, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732865845, "dur":136, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732865983, "dur":133, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732865052, "dur":1064, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/is4bubcwlmly.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732866263, "dur":58, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppGenericClassTable.c" }}
,{ "pid":12345, "tid":8, "ts":1758794732866327, "dur":83, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732866153, "dur":257, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4o7izx9gkngu.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732866537, "dur":439, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__41.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732866991, "dur":91, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732866441, "dur":641, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4ku7hecaxqvj.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732867245, "dur":309, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppInteropDataTable.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732867555, "dur":81, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732867641, "dur":325, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732867225, "dur":741, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ubftpsb0k70x.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732868087, "dur":485, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/__Generated.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732868676, "dur":57, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732868787, "dur":700, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732868011, "dur":1476, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u0h0cgtsanpq.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732869598, "dur":287, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.InputSystem_CodeGen.c" }}
,{ "pid":12345, "tid":8, "ts":1758794732869898, "dur":96, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732869593, "dur":401, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v5offjmam45m.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732870048, "dur":512, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__14.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732870567, "dur":285, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732870021, "dur":831, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tsjsdfte9ymh.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732871059, "dur":335, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__78.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732871402, "dur":293, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732871695, "dur":81, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732871019, "dur":757, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/45wunof9r0ik.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732871825, "dur":295, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Universal.Runtime__8.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732872130, "dur":221, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732871805, "dur":546, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ybjk01qywe6e.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732872408, "dur":272, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__5.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732872682, "dur":82, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732872787, "dur":232, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732873086, "dur":250, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732872381, "dur":955, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sboqdzbb72jk.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732873454, "dur":406, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__71.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732873861, "dur":82, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732873944, "dur":205, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732874156, "dur":108, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732873390, "dur":874, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5jblcijp5jy0.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732874370, "dur":171, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__115.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732874547, "dur":388, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732874311, "dur":624, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cnvmdb7tkryn.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732874935, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732875073, "dur":336, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.SubsystemsModule.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732875414, "dur":88, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732875560, "dur":72, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732875634, "dur":205, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732875023, "dur":816, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qfjky63h2tvk.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732875987, "dur":90, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732876115, "dur":61, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732876209, "dur":57, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732876266, "dur":145, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732875891, "dur":520, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v9tcpryj8fyv.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732876525, "dur":299, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/System.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732876834, "dur":121, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732876437, "dur":518, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/adfw9vk83mct.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732877099, "dur":230, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__92.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732877340, "dur":259, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732877064, "dur":536, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kwv3d8elewd6.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732877732, "dur":588, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.SharedInternalsModule.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732878329, "dur":195, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732877634, "dur":890, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9qz3hjocozz6.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732878687, "dur":629, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UI__1.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732879390, "dur":79, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732878665, "dur":805, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5vc3hacbp5on.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732879668, "dur":154, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.InputSystem.ForUI_CodeGen.c" }}
,{ "pid":12345, "tid":8, "ts":1758794732879583, "dur":292, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ykod1adae3zi.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732880167, "dur":394, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__83.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732880573, "dur":151, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732880744, "dur":610, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732880061, "dur":1293, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/y9y8ku31efqu.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732881487, "dur":80, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732881389, "dur":178, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0qexho4w71el.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732881647, "dur":496, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__126.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732882157, "dur":173, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732881606, "dur":724, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kxfm6dd9qyey.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732882417, "dur":498, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__4.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732882926, "dur":88, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732883014, "dur":98, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732882381, "dur":731, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/83ui89ugq736.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732883414, "dur":157, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__6.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732883578, "dur":310, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732883385, "dur":503, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/urin8ypcxxw5.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732884020, "dur":391, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__31.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732884417, "dur":257, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732884676, "dur":582, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732883976, "dur":1283, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xiuo9qmtulwo.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732885259, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732885379, "dur":263, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.AIModule.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732885647, "dur":100, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732885749, "dur":401, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732885331, "dur":819, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2l971w55l15d.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732886150, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732886297, "dur":326, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.InputLegacyModule_CodeGen.c" }}
,{ "pid":12345, "tid":8, "ts":1758794732886633, "dur":68, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732886266, "dur":435, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bkxhs95qe1xv.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732886763, "dur":276, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppCCalculateFieldValues.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732887101, "dur":266, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732886728, "dur":640, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gtracz6mhahu.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732887566, "dur":196, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__70.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732887773, "dur":79, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732887559, "dur":293, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4br4r22q1hhl.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732887888, "dur":190, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Il2CppGenericMethodPointerTable.c" }}
,{ "pid":12345, "tid":8, "ts":1758794732887858, "dur":245, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ksh0vzcx0c3e.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732888247, "dur":207, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__67.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732888493, "dur":111, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732888627, "dur":132, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":8, "ts":1758794732888825, "dur":103, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732888168, "dur":760, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cotnzrbvm775.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732889027, "dur":372, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__102.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732889407, "dur":133, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732889006, "dur":534, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ipnikj6i7tpe.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732889712, "dur":167, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/System.Configuration.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732889895, "dur":317, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732889693, "dur":520, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ank190b97ue3.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732890385, "dur":244, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__54.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732890637, "dur":77, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732890715, "dur":95, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732890278, "dur":532, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/iqlj6zak99yw.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732890972, "dur":204, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__11.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732891201, "dur":424, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":8, "ts":1758794732891641, "dur":250, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732890909, "dur":982, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kgj21xyyzs0a.o" }}
,{ "pid":12345, "tid":8, "ts":1758794732891934, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732892079, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732892214, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__14.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732892267, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732892342, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__11.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732892393, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732892657, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__3.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732892717, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732892792, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SubsystemsModule.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732892847, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732892963, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732893175, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732893372, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732893465, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732893575, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732893674, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732893788, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732893936, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732894041, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732894169, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732894327, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732894456, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732894576, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732894692, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732894857, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732895014, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732895139, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732895214, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__28.cpp" }}
,{ "pid":12345, "tid":8, "ts":1758794732895276, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732895395, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732895502, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732895677, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732895790, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732895931, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732896104, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732896192, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732896309, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732896413, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732896519, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732896686, "dur":233, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732896952, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732897065, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732897177, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732897277, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732897402, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732897520, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732897724, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732897839, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732897965, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732898077, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732898152, "dur":14606, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794732912800, "dur":54720, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732804154, "dur":13644, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732817801, "dur":1114, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732818915, "dur":1200, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732820115, "dur":1161, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732821276, "dur":1320, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732822596, "dur":2704, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732825341, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732825444, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732825544, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732825655, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732825769, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732825895, "dur":157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732826056, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libmain.so" }}
,{ "pid":12345, "tid":9, "ts":1758794732826170, "dur":103, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libmain.so" }}
,{ "pid":12345, "tid":9, "ts":1758794732826283, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":9, "ts":1758794732826335, "dur":74, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":9, "ts":1758794732826465, "dur":107, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":9, "ts":1758794732826572, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732826630, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon_round.xml" }}
,{ "pid":12345, "tid":9, "ts":1758794732826734, "dur":97, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon_round.xml" }}
,{ "pid":12345, "tid":9, "ts":1758794732826918, "dur":73, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/RuntimeInitializeOnLoads.json" }}
,{ "pid":12345, "tid":9, "ts":1758794732826991, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732827069, "dur":6357, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732833435, "dur":485, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1758794732833972, "dur":660, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.RenderPipeline.Universal.ShaderLibrary-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1758794732834633, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732834699, "dur":405, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1758794732835148, "dur":578, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1758794732835727, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732835811, "dur":271, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Burst.Unsafe-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1758794732836117, "dur":182, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1758794732836332, "dur":288, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1758794732836664, "dur":249, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1758794732836963, "dur":220, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732837314, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732837387, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputLegacyModule.dll" }}
,{ "pid":12345, "tid":9, "ts":1758794732837439, "dur":328, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732837810, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732837943, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":9, "ts":1758794732838049, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732838184, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.ForUI.pdb" }}
,{ "pid":12345, "tid":9, "ts":1758794732838311, "dur":173, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732838535, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732838648, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732838716, "dur":81, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIElementsModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1758794732838798, "dur":166, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732838996, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732839108, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732839204, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732839307, "dur":65, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":9, "ts":1758794732839372, "dur":246, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732839694, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityAnalyticsCommonModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1758794732839767, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732839877, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732839959, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityWebRequestModule.pdb" }}
,{ "pid":12345, "tid":9, "ts":1758794732840018, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732840107, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.RenderPipelines.Universal.Runtime.pdb" }}
,{ "pid":12345, "tid":9, "ts":1758794732840183, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732840253, "dur":7035, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732847338, "dur":633, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__18.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732847982, "dur":143, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732847288, "dur":838, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j24bd4uim9ku.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732848241, "dur":608, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__64.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732848864, "dur":282, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732848151, "dur":995, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5k2ujt21ct3n.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732849146, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732849266, "dur":176, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__105.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732849450, "dur":126, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732849236, "dur":340, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5kv34nhdimzs.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732849727, "dur":176, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__124.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732849911, "dur":135, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732850058, "dur":137, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":9, "ts":1758794732850196, "dur":55, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732850251, "dur":299, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732849701, "dur":849, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/y9rh6oidk8lz.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732850612, "dur":126, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__26.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732850743, "dur":282, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":9, "ts":1758794732851059, "dur":110, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732851169, "dur":469, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732850585, "dur":1053, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3esacly076oe.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732851681, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wj9iw0lt34ql.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732851837, "dur":131, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__38.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732851976, "dur":149, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732851807, "dur":318, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w4fykxidh2g8.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732852173, "dur":52, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.TextMeshPro_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1758794732852426, "dur":50, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732852136, "dur":341, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hvdq1vxi6fql.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732852616, "dur":253, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__9.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732852876, "dur":187, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732852575, "dur":488, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ejicdriiwont.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732853645, "dur":569, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__27.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732854283, "dur":124, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732853503, "dur":904, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nqrw66tpmwvq.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732854581, "dur":253, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__15.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732854849, "dur":244, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732855093, "dur":213, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732854506, "dur":800, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bxo3noum2de0.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732855386, "dur":123, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Burst.Unsafe_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1758794732855510, "dur":51, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732855613, "dur":280, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":9, "ts":1758794732855895, "dur":242, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732855346, "dur":791, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4i4fpc4sota9.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732856270, "dur":391, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Universal.Runtime__6.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732856672, "dur":75, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732856206, "dur":541, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/covf0cw2podg.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732856919, "dur":727, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__123.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732857698, "dur":79, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732856881, "dur":896, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x170l91eg64j.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732858074, "dur":188, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.SpriteShapeModule_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1758794732858270, "dur":116, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732858058, "dur":328, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s2zk6vobh4p5.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732858625, "dur":271, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Core.Runtime__2.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732858904, "dur":192, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732858587, "dur":509, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4pr6k3udpa6u.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732859326, "dur":228, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__15.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732859558, "dur":176, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732859740, "dur":250, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732859313, "dur":677, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u038xu9cd13w.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732860148, "dur":280, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__22.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732860435, "dur":101, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732860142, "dur":394, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bwxtgdbav48c.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732860969, "dur":663, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__88.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732861664, "dur":131, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732861800, "dur":163, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732861964, "dur":340, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732860938, "dur":1366, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r0d60h7qvjnb.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732862304, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732862494, "dur":873, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__99.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732863377, "dur":115, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732862396, "dur":1096, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jihdpeb50cyp.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732863700, "dur":370, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.XRModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732864081, "dur":87, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732863677, "dur":491, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8cxtvlnxneng.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732864324, "dur":141, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Mathematics__5.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732864474, "dur":109, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732864584, "dur":252, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732864299, "dur":537, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/z57vahfufrbf.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732865196, "dur":192, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__24.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732865397, "dur":53, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732865069, "dur":381, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0ujbm2ivb4lc.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732865525, "dur":203, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__6.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732865737, "dur":70, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732865521, "dur":286, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7frjxs9omajb.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732866224, "dur":58, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732866282, "dur":192, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732866090, "dur":384, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zvkgjoq9lt0b.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732866599, "dur":319, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__10.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732866919, "dur":75, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732866995, "dur":229, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":9, "ts":1758794732867227, "dur":143, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732866517, "dur":853, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gzwa4rwvsfae.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732867429, "dur":196, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.AndroidJNIModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732867643, "dur":207, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732867400, "dur":451, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8ngacvxpmxyj.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732867851, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732867984, "dur":364, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Mathematics__10.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732868359, "dur":97, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":9, "ts":1758794732868489, "dur":95, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732868584, "dur":494, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732867930, "dur":1148, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ji5rnbr4n2sw.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732869200, "dur":518, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Universal.Runtime__5.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732869726, "dur":165, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":9, "ts":1758794732869894, "dur":424, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732869126, "dur":1193, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r8rrk3h5m01w.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732870468, "dur":385, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIElementsModule__12.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732870860, "dur":162, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":9, "ts":1758794732871032, "dur":294, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732870462, "dur":865, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2mkf4grrz52c.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732871361, "dur":97, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.InputSystem__9.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732871466, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732871338, "dur":182, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/44djn0hpd6xi.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732871534, "dur":357, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__122.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732871899, "dur":179, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732871530, "dur":548, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cm4c6znhdit0.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732872078, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732872295, "dur":322, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.Core.Runtime__4.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732872622, "dur":81, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":9, "ts":1758794732872707, "dur":197, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732872289, "dur":615, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qdfblsp9v0yt.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732873082, "dur":234, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.TextMeshPro.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732873348, "dur":50, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732873437, "dur":301, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732873010, "dur":728, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/15fqi0rhm93w.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732873870, "dur":345, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__6.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732874222, "dur":63, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":9, "ts":1758794732874345, "dur":249, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732873864, "dur":730, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tj86xes9a50z.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732874668, "dur":680, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Burst.Unsafe.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732875384, "dur":111, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732875518, "dur":115, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732874623, "dur":1010, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/84qkxl6metpj.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732875654, "dur":397, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__61.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732876062, "dur":348, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732875641, "dur":769, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yhb23ut82ez5.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732876544, "dur":186, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.AnimationModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732876740, "dur":224, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732876964, "dur":706, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732876449, "dur":1222, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/c7n0u680732u.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732877787, "dur":362, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__85.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732878165, "dur":195, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732877704, "dur":657, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kxmdljyxl5it.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732878761, "dur":478, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__28.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732879248, "dur":424, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732878673, "dur":999, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p2ffcatwexhd.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732879708, "dur":178, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/krptmlr3vl6q.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732880037, "dur":291, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib__5.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732880329, "dur":372, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732880712, "dur":507, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732880031, "dur":1188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bpdjed32b8gg.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732881219, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732881332, "dur":398, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.InputSystem__10.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732881891, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732881302, "dur":793, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a1ug6jjd82v7.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732882303, "dur":51, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.Collections_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1758794732882384, "dur":114, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732882228, "dur":270, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jjnr4cuk54hs.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732882498, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732882585, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x2r4n3ir3fp0.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732882857, "dur":139, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/System_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1758794732882847, "dur":195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ehe76sk9dl88.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732883060, "dur":250, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.RenderPipelines.GPUDriven.Runtime__2.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732883324, "dur":140, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732883055, "dur":409, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bmh64ibtkt13.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732883561, "dur":398, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Generics__62.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732883976, "dur":165, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732883494, "dur":647, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jxeqj47552ap.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732884180, "dur":163, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UIModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732884351, "dur":125, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/llvm-ar" }}
,{ "pid":12345, "tid":9, "ts":1758794732884483, "dur":50, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732884163, "dur":370, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mst6h8sgieob.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732884757, "dur":334, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UnityAnalyticsModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732885301, "dur":180, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732884696, "dur":785, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/720qpdrqpvlf.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732885585, "dur":154, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.UnityConsentModule_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1758794732885754, "dur":101, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732885578, "dur":277, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jm2tln2tbwwo.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732885906, "dur":682, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/mscorlib.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732886603, "dur":200, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732885885, "dur":918, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cqx09kpvct5h.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732886892, "dur":445, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.JSONSerializeModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732887349, "dur":65, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732887414, "dur":92, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732886851, "dur":656, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s0c7lvqjxyia.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732887690, "dur":368, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/Unity.TextMeshPro__8.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732888065, "dur":119, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732888184, "dur":262, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732887677, "dur":769, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5laijwt1vdhx.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732888619, "dur":113, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/GenericMethods__28.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732888742, "dur":136, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732888593, "dur":285, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/85ukmg8mpk79.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732888942, "dur":86, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.TilemapModule_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1758794732888928, "dur":167, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/y9rp2ifv60p9.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732889213, "dur":571, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/UnityEngine.AudioModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732889849, "dur":65, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732889914, "dur":700, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732889123, "dur":1492, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qwmvb7l66ht8.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732890813, "dur":433, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/cpp/System__3.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732891275, "dur":248, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++" }}
,{ "pid":12345, "tid":9, "ts":1758794732891535, "dur":140, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732890804, "dur":871, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bqox91s5i1hw.o" }}
,{ "pid":12345, "tid":9, "ts":1758794732891718, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732891823, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.RenderPipelines.GPUDriven.Runtime__2.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732891951, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732892056, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732892165, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732892294, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732892485, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732892622, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732892726, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.HierarchyCoreModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732892777, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732892935, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732893053, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732893167, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732893382, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732893484, "dur":177, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732893691, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732893801, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732893929, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732894056, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732894201, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732894323, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732894477, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VRModule.cpp" }}
,{ "pid":12345, "tid":9, "ts":1758794732894527, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732894628, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732894745, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732894833, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732894956, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732895124, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732895216, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732895327, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732895483, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732895578, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732895674, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732895756, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732895839, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732895936, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732896114, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732896220, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732896331, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732896438, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732896540, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732896696, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732896801, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732896913, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732897028, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732897125, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732897221, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732897337, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732897455, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732897544, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732897654, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732897829, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732897924, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732898050, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794732898118, "dur":64, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/Data/Metadata/global-metadata.dat" }}
,{ "pid":12345, "tid":9, "ts":1758794732898233, "dur":69300, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732804164, "dur":13644, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732817808, "dur":1097, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732818905, "dur":1228, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732820133, "dur":1217, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732821351, "dur":1258, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732822609, "dur":2734, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732825345, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732825449, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732825531, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732825628, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732825744, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732825863, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732826024, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732826131, "dur":179, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/resources/META-INF/com.android.games.engine.build_fingerprint" }}
,{ "pid":12345, "tid":10, "ts":1758794732826311, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732826365, "dur":91, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/styles.xml" }}
,{ "pid":12345, "tid":10, "ts":1758794732826500, "dur":83, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v30/freeformwindow.xml" }}
,{ "pid":12345, "tid":10, "ts":1758794732826583, "dur":57, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v30/freeformwindow.xml" }}
,{ "pid":12345, "tid":10, "ts":1758794732826743, "dur":109, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/app_icon.png" }}
,{ "pid":12345, "tid":10, "ts":1758794732826916, "dur":91, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/data.unity3d" }}
,{ "pid":12345, "tid":10, "ts":1758794732827007, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732827071, "dur":6342, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732833431, "dur":705, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1758794732834136, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732834231, "dur":648, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1758794732834926, "dur":306, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1758794732835233, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732835291, "dur":301, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1758794732835622, "dur":366, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Universal.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1758794732836050, "dur":449, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.Mathematics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1758794732836547, "dur":174, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1758794732836752, "dur":467, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1758794732837256, "dur":1277, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Generate Manifests Library/Bee/artifacts/Android/Manifest/IntermediateLauncherManifestDiag.txt (+3 others)" }}
,{ "pid":12345, "tid":10, "ts":1758794732838565, "dur":84, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794732840366, "dur":111820, "ph":"X", "name": "Generate",  "args": { "detail":"Manifests Library/Bee/artifacts/Android/Manifest/IntermediateLauncherManifestDiag.txt (+3 others)" }}
,{ "pid":12345, "tid":10, "ts":1758794732952306, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ModifyAndroidProjectCallback /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml (+8 others)" }}
,{ "pid":12345, "tid":10, "ts":1758794732953928, "dur":13562, "ph":"X", "name": "ModifyAndroidProjectCallback",  "args": { "detail":"/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml (+8 others)" }}
,{ "pid":12345, "tid":0, "ts":1758794732973106, "dur":1332, "ph":"X", "name": "ProfilerWriteOutput" }
,