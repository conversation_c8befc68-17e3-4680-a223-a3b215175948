{ "pid": 37977, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 37977, "tid": 1, "ts": 1758795359025213, "dur": 3690, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 37977, "tid": 1, "ts": 1758795359028907, "dur": 76056, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 37977, "tid": 1, "ts": 1758795359104966, "dur": 20040, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 37977, "tid": 668, "ts": 1758795360068856, "dur": 10, "ph": "X", "name": "", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359025067, "dur": 11264, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359036333, "dur": 1030755, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359036347, "dur": 61, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359036410, "dur": 125, "ph": "X", "name": "ProcessMessages 57", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359036537, "dur": 8595, "ph": "X", "name": "ReadAsync 57", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045150, "dur": 5, "ph": "X", "name": "ProcessMessages 2123", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045188, "dur": 123, "ph": "X", "name": "ReadAsync 2123", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045316, "dur": 4, "ph": "X", "name": "ProcessMessages 3265", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045332, "dur": 67, "ph": "X", "name": "ReadAsync 3265", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045418, "dur": 2, "ph": "X", "name": "ProcessMessages 1609", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045422, "dur": 57, "ph": "X", "name": "ReadAsync 1609", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045483, "dur": 3, "ph": "X", "name": "ProcessMessages 2105", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045488, "dur": 63, "ph": "X", "name": "ReadAsync 2105", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045556, "dur": 2, "ph": "X", "name": "ProcessMessages 1516", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045562, "dur": 56, "ph": "X", "name": "ReadAsync 1516", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045621, "dur": 3, "ph": "X", "name": "ProcessMessages 1489", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045625, "dur": 59, "ph": "X", "name": "ReadAsync 1489", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045693, "dur": 2, "ph": "X", "name": "ProcessMessages 1508", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045696, "dur": 74, "ph": "X", "name": "ReadAsync 1508", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045785, "dur": 2, "ph": "X", "name": "ProcessMessages 1080", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045789, "dur": 81, "ph": "X", "name": "ReadAsync 1080", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045875, "dur": 3, "ph": "X", "name": "ProcessMessages 2650", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359045880, "dur": 389, "ph": "X", "name": "ReadAsync 2650", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359046286, "dur": 18, "ph": "X", "name": "ProcessMessages 6827", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359046306, "dur": 53, "ph": "X", "name": "ReadAsync 6827", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359046373, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359046375, "dur": 252, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359046633, "dur": 2, "ph": "X", "name": "ProcessMessages 1766", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359046637, "dur": 33, "ph": "X", "name": "ReadAsync 1766", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359046672, "dur": 2, "ph": "X", "name": "ProcessMessages 307", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359046676, "dur": 248, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359046928, "dur": 2, "ph": "X", "name": "ProcessMessages 1913", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359046931, "dur": 39, "ph": "X", "name": "ReadAsync 1913", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359046973, "dur": 1, "ph": "X", "name": "ProcessMessages 893", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359046977, "dur": 110, "ph": "X", "name": "ReadAsync 893", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047088, "dur": 1, "ph": "X", "name": "ProcessMessages 1020", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047091, "dur": 43, "ph": "X", "name": "ReadAsync 1020", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047135, "dur": 1, "ph": "X", "name": "ProcessMessages 1839", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047140, "dur": 72, "ph": "X", "name": "ReadAsync 1839", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047215, "dur": 2, "ph": "X", "name": "ProcessMessages 1436", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047221, "dur": 222, "ph": "X", "name": "ReadAsync 1436", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047446, "dur": 5, "ph": "X", "name": "ProcessMessages 4999", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047453, "dur": 38, "ph": "X", "name": "ReadAsync 4999", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047497, "dur": 1, "ph": "X", "name": "ProcessMessages 1206", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047525, "dur": 75, "ph": "X", "name": "ReadAsync 1206", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047602, "dur": 96, "ph": "X", "name": "ProcessMessages 2073", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047700, "dur": 50, "ph": "X", "name": "ReadAsync 2073", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047752, "dur": 6, "ph": "X", "name": "ProcessMessages 3365", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047760, "dur": 54, "ph": "X", "name": "ReadAsync 3365", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047816, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047819, "dur": 148, "ph": "X", "name": "ReadAsync 633", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047970, "dur": 2, "ph": "X", "name": "ProcessMessages 1825", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359047974, "dur": 84, "ph": "X", "name": "ReadAsync 1825", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048064, "dur": 2, "ph": "X", "name": "ProcessMessages 2066", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048071, "dur": 43, "ph": "X", "name": "ReadAsync 2066", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048128, "dur": 2, "ph": "X", "name": "ProcessMessages 1285", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048132, "dur": 75, "ph": "X", "name": "ReadAsync 1285", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048209, "dur": 2, "ph": "X", "name": "ProcessMessages 2120", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048214, "dur": 92, "ph": "X", "name": "ReadAsync 2120", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048307, "dur": 2, "ph": "X", "name": "ProcessMessages 1894", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048326, "dur": 44, "ph": "X", "name": "ReadAsync 1894", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048373, "dur": 2, "ph": "X", "name": "ProcessMessages 1647", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048378, "dur": 55, "ph": "X", "name": "ReadAsync 1647", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048440, "dur": 4, "ph": "X", "name": "ProcessMessages 1416", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048445, "dur": 66, "ph": "X", "name": "ReadAsync 1416", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048513, "dur": 2, "ph": "X", "name": "ProcessMessages 1157", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048517, "dur": 142, "ph": "X", "name": "ReadAsync 1157", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048662, "dur": 4, "ph": "X", "name": "ProcessMessages 4610", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048669, "dur": 45, "ph": "X", "name": "ReadAsync 4610", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048732, "dur": 1, "ph": "X", "name": "ProcessMessages 1053", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048736, "dur": 65, "ph": "X", "name": "ReadAsync 1053", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048805, "dur": 2, "ph": "X", "name": "ProcessMessages 1978", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048808, "dur": 65, "ph": "X", "name": "ReadAsync 1978", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048877, "dur": 2, "ph": "X", "name": "ProcessMessages 1704", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048881, "dur": 45, "ph": "X", "name": "ReadAsync 1704", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048930, "dur": 1, "ph": "X", "name": "ProcessMessages 1168", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359048934, "dur": 77, "ph": "X", "name": "ReadAsync 1168", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049013, "dur": 2, "ph": "X", "name": "ProcessMessages 841", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049027, "dur": 43, "ph": "X", "name": "ReadAsync 841", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049072, "dur": 2, "ph": "X", "name": "ProcessMessages 1830", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049087, "dur": 50, "ph": "X", "name": "ReadAsync 1830", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049138, "dur": 2, "ph": "X", "name": "ProcessMessages 1375", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049141, "dur": 60, "ph": "X", "name": "ReadAsync 1375", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049203, "dur": 1, "ph": "X", "name": "ProcessMessages 1020", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049244, "dur": 166, "ph": "X", "name": "ReadAsync 1020", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049412, "dur": 1, "ph": "X", "name": "ProcessMessages 1146", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049415, "dur": 36, "ph": "X", "name": "ReadAsync 1146", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049454, "dur": 2, "ph": "X", "name": "ProcessMessages 1272", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049460, "dur": 79, "ph": "X", "name": "ReadAsync 1272", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049541, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049545, "dur": 186, "ph": "X", "name": "ReadAsync 855", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049732, "dur": 5, "ph": "X", "name": "ProcessMessages 6043", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049739, "dur": 42, "ph": "X", "name": "ReadAsync 6043", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049784, "dur": 20, "ph": "X", "name": "ProcessMessages 1208", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049810, "dur": 59, "ph": "X", "name": "ReadAsync 1208", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049871, "dur": 2, "ph": "X", "name": "ProcessMessages 2052", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049875, "dur": 64, "ph": "X", "name": "ReadAsync 2052", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049985, "dur": 1, "ph": "X", "name": "ProcessMessages 1197", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359049998, "dur": 49, "ph": "X", "name": "ReadAsync 1197", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050051, "dur": 2, "ph": "X", "name": "ProcessMessages 1792", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050059, "dur": 67, "ph": "X", "name": "ReadAsync 1792", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050130, "dur": 1, "ph": "X", "name": "ProcessMessages 1441", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050132, "dur": 63, "ph": "X", "name": "ReadAsync 1441", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050260, "dur": 11, "ph": "X", "name": "ProcessMessages 1784", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050274, "dur": 55, "ph": "X", "name": "ReadAsync 1784", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050331, "dur": 3, "ph": "X", "name": "ProcessMessages 3168", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050386, "dur": 71, "ph": "X", "name": "ReadAsync 3168", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050462, "dur": 2, "ph": "X", "name": "ProcessMessages 2230", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050475, "dur": 87, "ph": "X", "name": "ReadAsync 2230", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050568, "dur": 16, "ph": "X", "name": "ProcessMessages 1723", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050587, "dur": 63, "ph": "X", "name": "ReadAsync 1723", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050655, "dur": 57, "ph": "X", "name": "ProcessMessages 2293", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050714, "dur": 93, "ph": "X", "name": "ReadAsync 2293", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050812, "dur": 4, "ph": "X", "name": "ProcessMessages 3032", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050871, "dur": 41, "ph": "X", "name": "ReadAsync 3032", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050920, "dur": 14, "ph": "X", "name": "ProcessMessages 3444", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359050936, "dur": 71, "ph": "X", "name": "ReadAsync 3444", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051062, "dur": 2, "ph": "X", "name": "ProcessMessages 1512", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051066, "dur": 94, "ph": "X", "name": "ReadAsync 1512", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051162, "dur": 3, "ph": "X", "name": "ProcessMessages 3856", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051170, "dur": 59, "ph": "X", "name": "ReadAsync 3856", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051232, "dur": 4, "ph": "X", "name": "ProcessMessages 1715", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051264, "dur": 63, "ph": "X", "name": "ReadAsync 1715", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051330, "dur": 5, "ph": "X", "name": "ProcessMessages 472", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051336, "dur": 76, "ph": "X", "name": "ReadAsync 472", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051423, "dur": 3, "ph": "X", "name": "ProcessMessages 1877", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051427, "dur": 115, "ph": "X", "name": "ReadAsync 1877", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051547, "dur": 3, "ph": "X", "name": "ProcessMessages 1783", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051552, "dur": 35, "ph": "X", "name": "ReadAsync 1783", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051589, "dur": 2, "ph": "X", "name": "ProcessMessages 1604", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051593, "dur": 35, "ph": "X", "name": "ReadAsync 1604", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051631, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051686, "dur": 96, "ph": "X", "name": "ReadAsync 524", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051784, "dur": 4, "ph": "X", "name": "ProcessMessages 2611", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051789, "dur": 49, "ph": "X", "name": "ReadAsync 2611", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051840, "dur": 1, "ph": "X", "name": "ProcessMessages 686", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051844, "dur": 66, "ph": "X", "name": "ReadAsync 686", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051914, "dur": 2, "ph": "X", "name": "ProcessMessages 1694", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051918, "dur": 40, "ph": "X", "name": "ReadAsync 1694", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359051989, "dur": 11, "ph": "X", "name": "ProcessMessages 848", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052001, "dur": 46, "ph": "X", "name": "ReadAsync 848", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052051, "dur": 2, "ph": "X", "name": "ProcessMessages 1651", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052054, "dur": 36, "ph": "X", "name": "ReadAsync 1651", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052093, "dur": 2, "ph": "X", "name": "ProcessMessages 629", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052129, "dur": 108, "ph": "X", "name": "ReadAsync 629", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052240, "dur": 3, "ph": "X", "name": "ProcessMessages 722", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052244, "dur": 181, "ph": "X", "name": "ReadAsync 722", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052428, "dur": 5, "ph": "X", "name": "ProcessMessages 847", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052434, "dur": 25, "ph": "X", "name": "ReadAsync 847", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052461, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052464, "dur": 69, "ph": "X", "name": "ReadAsync 523", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052534, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052537, "dur": 45, "ph": "X", "name": "ReadAsync 615", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052586, "dur": 2, "ph": "X", "name": "ProcessMessages 1215", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052595, "dur": 27, "ph": "X", "name": "ReadAsync 1215", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052710, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052712, "dur": 45, "ph": "X", "name": "ReadAsync 595", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052760, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052763, "dur": 182, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052950, "dur": 2, "ph": "X", "name": "ProcessMessages 777", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359052953, "dur": 360, "ph": "X", "name": "ReadAsync 777", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359053314, "dur": 1, "ph": "X", "name": "ProcessMessages 744", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359053317, "dur": 271, "ph": "X", "name": "ReadAsync 744", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359053590, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359053593, "dur": 74, "ph": "X", "name": "ReadAsync 692", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359053673, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359053675, "dur": 166, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359053843, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359053846, "dur": 171, "ph": "X", "name": "ReadAsync 482", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054020, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054024, "dur": 92, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054122, "dur": 2, "ph": "X", "name": "ProcessMessages 858", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054125, "dur": 194, "ph": "X", "name": "ReadAsync 858", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054321, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054327, "dur": 54, "ph": "X", "name": "ReadAsync 737", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054383, "dur": 1, "ph": "X", "name": "ProcessMessages 217", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054386, "dur": 184, "ph": "X", "name": "ReadAsync 217", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054572, "dur": 1, "ph": "X", "name": "ProcessMessages 807", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054586, "dur": 27, "ph": "X", "name": "ReadAsync 807", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054616, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054618, "dur": 186, "ph": "X", "name": "ReadAsync 246", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054815, "dur": 1, "ph": "X", "name": "ProcessMessages 796", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054817, "dur": 53, "ph": "X", "name": "ReadAsync 796", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054872, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359054874, "dur": 180, "ph": "X", "name": "ReadAsync 215", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359055082, "dur": 1, "ph": "X", "name": "ProcessMessages 840", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359055084, "dur": 2802, "ph": "X", "name": "ReadAsync 840", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359057891, "dur": 6, "ph": "X", "name": "ProcessMessages 8189", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359057898, "dur": 55, "ph": "X", "name": "ReadAsync 8189", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359057958, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359057960, "dur": 113, "ph": "X", "name": "ReadAsync 673", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359058078, "dur": 1, "ph": "X", "name": "ProcessMessages 1014", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359058080, "dur": 249, "ph": "X", "name": "ReadAsync 1014", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359058333, "dur": 1, "ph": "X", "name": "ProcessMessages 921", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359058337, "dur": 221, "ph": "X", "name": "ReadAsync 921", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359058562, "dur": 2, "ph": "X", "name": "ProcessMessages 936", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359058566, "dur": 237, "ph": "X", "name": "ReadAsync 936", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359058807, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359058809, "dur": 168, "ph": "X", "name": "ReadAsync 623", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359058979, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359058992, "dur": 27, "ph": "X", "name": "ReadAsync 786", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359059022, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359059024, "dur": 173, "ph": "X", "name": "ReadAsync 132", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359059200, "dur": 2, "ph": "X", "name": "ProcessMessages 1344", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359059204, "dur": 35, "ph": "X", "name": "ReadAsync 1344", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359059241, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359059243, "dur": 244, "ph": "X", "name": "ReadAsync 533", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359059490, "dur": 2, "ph": "X", "name": "ProcessMessages 520", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359059493, "dur": 173, "ph": "X", "name": "ReadAsync 520", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359059672, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359059679, "dur": 92, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359059775, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359059880, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359059937, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359059981, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359060067, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359060068, "dur": 271, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359060355, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359060358, "dur": 92, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359060456, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359060459, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359060510, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359060547, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359060601, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359060603, "dur": 205, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359060812, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359060814, "dur": 106, "ph": "X", "name": "ReadAsync 60", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359060922, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359060925, "dur": 69, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359061051, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359061053, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359061113, "dur": 215, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359061330, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359061336, "dur": 61, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359061399, "dur": 32, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359061433, "dur": 201, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359061636, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359061706, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359061821, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359061823, "dur": 88, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359061915, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359061920, "dur": 47, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359061998, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359062002, "dur": 167, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359062177, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359062179, "dur": 245, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359062494, "dur": 6, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359062502, "dur": 125, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359062692, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359062730, "dur": 43, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359062776, "dur": 108, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359062888, "dur": 32, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359062926, "dur": 98, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359063026, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359063038, "dur": 107, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359063148, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359063151, "dur": 42, "ph": "X", "name": "ReadAsync 172", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359063197, "dur": 10, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359063211, "dur": 181, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359063409, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359063411, "dur": 31, "ph": "X", "name": "ReadAsync 140", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359063445, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359063628, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359063674, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359063682, "dur": 154, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359063838, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359063840, "dur": 78, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359063923, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064001, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064002, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064045, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064048, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064091, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064102, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064205, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064209, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064249, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064251, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064293, "dur": 147, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064443, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064449, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064489, "dur": 174, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064699, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064701, "dur": 62, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064765, "dur": 146, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064913, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359064915, "dur": 46, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065050, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065095, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065097, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065160, "dur": 63, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065225, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065287, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065362, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065380, "dur": 68, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065450, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065453, "dur": 125, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065650, "dur": 25, "ph": "X", "name": "ProcessMessages 192", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065717, "dur": 86, "ph": "X", "name": "ReadAsync 192", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065805, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065811, "dur": 60, "ph": "X", "name": "ReadAsync 236", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065872, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065877, "dur": 49, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065932, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359065936, "dur": 71, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066009, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066012, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066068, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066121, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066123, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066193, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066195, "dur": 84, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066281, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066283, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066342, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066344, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066443, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066450, "dur": 60, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066511, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066514, "dur": 45, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066600, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066602, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066651, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066653, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066696, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066720, "dur": 66, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066788, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066792, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066841, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066852, "dur": 75, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066928, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066934, "dur": 48, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066985, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359066992, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359067032, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359067034, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359067074, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359067114, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359067120, "dur": 74, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359067195, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359067197, "dur": 48, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359067249, "dur": 42, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359067294, "dur": 12, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359067309, "dur": 107, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359067418, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359067423, "dur": 87, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359067514, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359067517, "dur": 992, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359068515, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359068516, "dur": 14318, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359082838, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359082841, "dur": 2079, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359084924, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359084937, "dur": 175, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359085115, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359085118, "dur": 768, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359085888, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359085890, "dur": 199, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359086091, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359086094, "dur": 657, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359086770, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359086775, "dur": 656, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359087509, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359087513, "dur": 184, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359087707, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359087713, "dur": 164, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359087879, "dur": 11, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359087893, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359088051, "dur": 2514, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359090570, "dur": 209, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359090785, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359090788, "dur": 2153, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359092956, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359093115, "dur": 159, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359093331, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359093333, "dur": 287, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359093623, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359093764, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359093767, "dur": 369, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359094140, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359094282, "dur": 557, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359094845, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359094851, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359094976, "dur": 1492, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359096475, "dur": 1702, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359098180, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359098293, "dur": 4540, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359102837, "dur": 434, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359103274, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359103276, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359103339, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359103343, "dur": 162, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359103507, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359103514, "dur": 681, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359104202, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359104256, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359104258, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359104337, "dur": 14, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359104354, "dur": 500, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359104857, "dur": 133, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359104992, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359104994, "dur": 131, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359105129, "dur": 1156, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359106288, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359106290, "dur": 47, "ph": "X", "name": "ReadAsync 236", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359106345, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359106390, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359106396, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359106476, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359106544, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359106549, "dur": 147, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359106700, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359106760, "dur": 300, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359107061, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359107063, "dur": 82, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359107148, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359107179, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359107241, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359107275, "dur": 260, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359107536, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359107538, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359107590, "dur": 543, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359108136, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359108138, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359108182, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359108184, "dur": 103611, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359211802, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359211805, "dur": 64, "ph": "X", "name": "ReadAsync 8192", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359211871, "dur": 31, "ph": "X", "name": "ReadAsync 8192", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359211904, "dur": 22, "ph": "X", "name": "ReadAsync 8192", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359211929, "dur": 39, "ph": "X", "name": "ReadAsync 8192", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359211970, "dur": 36, "ph": "X", "name": "ReadAsync 8192", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359212017, "dur": 43, "ph": "X", "name": "ReadAsync 8192", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359212063, "dur": 31, "ph": "X", "name": "ProcessMessages 3232", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359212095, "dur": 5261, "ph": "X", "name": "ReadAsync 3232", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359217361, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359217363, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359217439, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359217548, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359217585, "dur": 188, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359217775, "dur": 644, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359218424, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359218426, "dur": 2337, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359220767, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359220769, "dur": 666, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359221439, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359221441, "dur": 679, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359222124, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359222126, "dur": 167, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359222297, "dur": 263, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359222563, "dur": 650, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359223218, "dur": 199, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359223419, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359223422, "dur": 631, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359224055, "dur": 20, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359224076, "dur": 396, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359224475, "dur": 17, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359224493, "dur": 228, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359224724, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359224768, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359224770, "dur": 196, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359224968, "dur": 11, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359224980, "dur": 282, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359225265, "dur": 16, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359225283, "dur": 43, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359225327, "dur": 175, "ph": "X", "name": "ProcessMessages 86", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359225504, "dur": 206, "ph": "X", "name": "ReadAsync 86", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359225712, "dur": 18, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359225731, "dur": 51, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359225784, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359225786, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359225823, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359225825, "dur": 200, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359226026, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359226028, "dur": 250, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359226280, "dur": 39, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359226321, "dur": 49, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359226372, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359226374, "dur": 112, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359226488, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359226490, "dur": 154, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359226646, "dur": 13, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359226661, "dur": 304, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359226967, "dur": 15, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359226983, "dur": 47, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227031, "dur": 6, "ph": "X", "name": "ProcessMessages 82", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227038, "dur": 41, "ph": "X", "name": "ReadAsync 82", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227080, "dur": 7, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227088, "dur": 124, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227214, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227217, "dur": 88, "ph": "X", "name": "ReadAsync 60", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227308, "dur": 8, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227317, "dur": 169, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227487, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227490, "dur": 276, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227767, "dur": 7, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227776, "dur": 126, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227903, "dur": 13, "ph": "X", "name": "ProcessMessages 102", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227918, "dur": 40, "ph": "X", "name": "ReadAsync 102", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227960, "dur": 9, "ph": "X", "name": "ProcessMessages 10", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359227970, "dur": 39, "ph": "X", "name": "ReadAsync 10", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228010, "dur": 12, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228023, "dur": 94, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228119, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228121, "dur": 37, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228161, "dur": 12, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228174, "dur": 292, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228468, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228471, "dur": 152, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228625, "dur": 8, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228634, "dur": 51, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228687, "dur": 8, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228696, "dur": 123, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228821, "dur": 25, "ph": "X", "name": "ProcessMessages 132", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228869, "dur": 81, "ph": "X", "name": "ReadAsync 132", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228952, "dur": 16, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359228969, "dur": 64, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359229043, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359229045, "dur": 641, "ph": "X", "name": "ReadAsync 60", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359229689, "dur": 16, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359229707, "dur": 126, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359229838, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359229842, "dur": 101, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359229945, "dur": 14, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359229960, "dur": 160, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359230122, "dur": 8, "ph": "X", "name": "ProcessMessages 78", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359230132, "dur": 42, "ph": "X", "name": "ReadAsync 78", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359230175, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359230177, "dur": 376, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359230557, "dur": 26, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359230584, "dur": 59, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359230646, "dur": 109, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359230756, "dur": 10, "ph": "X", "name": "ProcessMessages 82", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359230768, "dur": 102, "ph": "X", "name": "ReadAsync 82", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359230872, "dur": 10, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359230883, "dur": 98, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359230982, "dur": 7, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359230993, "dur": 123, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359231120, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359231184, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359231185, "dur": 225, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359231412, "dur": 7, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359231420, "dur": 327, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359231749, "dur": 11, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359231760, "dur": 161, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359231924, "dur": 14, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359231939, "dur": 79, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359232020, "dur": 30, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359232052, "dur": 70, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359232124, "dur": 22, "ph": "X", "name": "ProcessMessages 433", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359232147, "dur": 126, "ph": "X", "name": "ReadAsync 433", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359232275, "dur": 6, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359232282, "dur": 206, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359232490, "dur": 17, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359232508, "dur": 77, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359232587, "dur": 9, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359232596, "dur": 343, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359232943, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359232945, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359233076, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359233080, "dur": 1184, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359234267, "dur": 21, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359234289, "dur": 1117, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359235410, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359235413, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359235456, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359235458, "dur": 330, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359235791, "dur": 16, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359235808, "dur": 2146, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359237960, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359237964, "dur": 324788, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359562759, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359562763, "dur": 57, "ph": "X", "name": "ReadAsync 8192", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359562823, "dur": 42, "ph": "X", "name": "ReadAsync 8192", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359562867, "dur": 44, "ph": "X", "name": "ReadAsync 8192", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359562914, "dur": 28, "ph": "X", "name": "ProcessMessages 7918", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359562943, "dur": 3001, "ph": "X", "name": "ReadAsync 7918", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359565948, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359565952, "dur": 228, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359566182, "dur": 27, "ph": "X", "name": "ProcessMessages 108", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359566210, "dur": 7804, "ph": "X", "name": "ReadAsync 108", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359574019, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795359574022, "dur": 487351, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795360061381, "dur": 23, "ph": "X", "name": "ProcessMessages 5652", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795360061405, "dur": 101, "ph": "X", "name": "ReadAsync 5652", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795360061511, "dur": 2, "ph": "X", "name": "ProcessMessages 25", "args": {} },
{ "pid": 37977, "tid": 25769803776, "ts": 1758795360061514, "dur": 5565, "ph": "X", "name": "ReadAsync 25", "args": {} },
{ "pid": 37977, "tid": 668, "ts": 1758795360068873, "dur": 1012, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 37977, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 37977, "tid": 21474836480, "ts": 1758795359024901, "dur": 100151, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 37977, "tid": 21474836480, "ts": 1758795359125059, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 37977, "tid": 21474836480, "ts": 1758795359125060, "dur": 113, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 37977, "tid": 668, "ts": 1758795360069887, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 37977, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 37977, "tid": 17179869184, "ts": 1758795359004042, "dur": 1063234, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 37977, "tid": 17179869184, "ts": 1758795359004256, "dur": 20522, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 37977, "tid": 17179869184, "ts": 1758795360067287, "dur": 87, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 37977, "tid": 17179869184, "ts": 1758795360067304, "dur": 36, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 37977, "tid": 668, "ts": 1758795360069899, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1758795359036428, "dur":1478, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795359037915, "dur":7898, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795359045889, "dur":105, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795359058099, "dur":779, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb" }}
,{ "pid":12345, "tid":0, "ts":1758795359046001, "dur":14287, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795359060294, "dur":1001994, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795360062413, "dur":3115, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1758795359045929, "dur":14375, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359060309, "dur":195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":1, "ts":1758795359060505, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359060600, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_621CCEC35B13C3FF.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359060770, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359060939, "dur":231, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_CEAF7E5E7A4F62D9.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359061170, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359061265, "dur":208, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_BD28D25607AEE2E1.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359061473, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359061584, "dur":212, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_C1844920F06A43FF.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359061797, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359061929, "dur":218, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_A83C1C8603B0CB24.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359062147, "dur":191, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359062341, "dur":231, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_E5C0679566569A75.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359062572, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359062696, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B964A68D830AC6BF.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359062827, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359062927, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_0E20F46DE1E1A65B.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359063068, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359063187, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3260E1D96E1E22C0.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359063318, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359063392, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_1879345459B1F65E.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359063529, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359063616, "dur":190, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_C69304A59A7A1B74.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359063806, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359063918, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_968EEE9C3279D1EC.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359064040, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359064111, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_5DE93EB871AE54DB.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359064226, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359064333, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_C9D4E8C710DC3487.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359064475, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359064579, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_5C992D1A061FC68C.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359064692, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359064772, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_68666B651D4ED621.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359064893, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359064972, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_1844A957E02F8FD0.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359065100, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359065171, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CDA2BEF1C1523480.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359065313, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359065375, "dur":141, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_2B487051AB4C3A03.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359065519, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359065617, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_6900CF627CC382EE.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359065767, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359065839, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_C571055F00C3F184.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359065984, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359066073, "dur":160, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359066289, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359066411, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359066499, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359066568, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_283D5FC3BD6A67A2.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359066668, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Mathematics.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":1, "ts":1758795359066735, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359066796, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359066878, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359066992, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359067064, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_89A9C630F938B4E1.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359067199, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359067304, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359067389, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359067463, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359067571, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359067672, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359067754, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359067848, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359067935, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359068000, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359068083, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359068165, "dur":173, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.rsp" }}
,{ "pid":12345, "tid":1, "ts":1758795359068339, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359068418, "dur":1269, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359069687, "dur":1219, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359070906, "dur":1495, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359072401, "dur":1354, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359073756, "dur":1291, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359075047, "dur":1349, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359076396, "dur":1462, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359077858, "dur":612, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359078471, "dur":664, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359079136, "dur":1336, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359080473, "dur":1382, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359081855, "dur":1394, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359083249, "dur":1511, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359084761, "dur":1667, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359086428, "dur":746, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359087175, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359087248, "dur":3994, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1758795359091243, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359091435, "dur":94, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_2E746244A5BDAEA0.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359091529, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359091620, "dur":1287, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359092907, "dur":1437, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359094345, "dur":1264, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359095610, "dur":1297, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359096907, "dur":1440, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359098347, "dur":1343, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359099690, "dur":1251, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359100941, "dur":1296, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359102237, "dur":1287, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359103524, "dur":441, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359103990, "dur":108, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359104126, "dur":309, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359104435, "dur":1567, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359106002, "dur":290, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359106327, "dur":939, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1758795359107266, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359107357, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359107415, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359107490, "dur":456, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1758795359107974, "dur":90, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758795359108083, "dur":358, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1758795359108459, "dur":105979, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359214447, "dur":3915, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.TextMeshPro.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1758795359218362, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359218449, "dur":7094, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1758795359225543, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359225722, "dur":412, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":1, "ts":1758795359226135, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1758795359226189, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359226251, "dur":425, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1758795359226716, "dur":484, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":1, "ts":1758795359227400, "dur":498, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll" }}
,{ "pid":12345, "tid":1, "ts":1758795359228126, "dur":531, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":1, "ts":1758795359228660, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359228959, "dur":585, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":1, "ts":1758795359229545, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.pdb" }}
,{ "pid":12345, "tid":1, "ts":1758795359229602, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359229906, "dur":641, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.pdb" }}
,{ "pid":12345, "tid":1, "ts":1758795359230596, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795359230854, "dur":664, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.pdb" }}
,{ "pid":12345, "tid":1, "ts":1758795359231576, "dur":674, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.pdb" }}
,{ "pid":12345, "tid":1, "ts":1758795359232251, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Timeline.pdb" }}
,{ "pid":12345, "tid":1, "ts":1758795359232671, "dur":746, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.pdb" }}
,{ "pid":12345, "tid":1, "ts":1758795359233417, "dur":828829, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359045935, "dur":14378, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359060316, "dur":187, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.rsp2" }}
,{ "pid":12345, "tid":2, "ts":1758795359060503, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359060645, "dur":168, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_F1E1BBCC6ABAE0DB.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359060815, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359060979, "dur":230, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_FF1638973A990706.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359061210, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359061319, "dur":214, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7EB4E75F718193E6.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359061533, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359061666, "dur":208, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_A49A776348DF5E4E.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359061875, "dur":157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359062038, "dur":268, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_E1CEC1A55253DF7A.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359062307, "dur":164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359062477, "dur":192, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_20CB703B61251BE5.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359062670, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359062762, "dur":155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D8293B7A6760759A.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359062917, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359063021, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_712FA009D6907B2B.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359063164, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359063264, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_FFC4A5DF3CDD2051.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359063402, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359063488, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_FD04B64CA8B1F690.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359063615, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359063764, "dur":166, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EB7DEB5E390DBA80.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359063930, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359064015, "dur":252, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":2, "ts":1758795359064267, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359064371, "dur":3787, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359068159, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359068275, "dur":15131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1758795359083407, "dur":202, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359083617, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_80B890C2D7911DBF.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359083723, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359083791, "dur":3170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/UnityEditor.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359086961, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359087018, "dur":11841, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/UnityEditor.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1758795359098863, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359099082, "dur":77, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_B49A4614ABCDBFD4.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359099159, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359099220, "dur":4459, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/UnityEditor.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359103702, "dur":968, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/UnityEditor.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1758795359104670, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359104798, "dur":239, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/Unity.Burst.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359105061, "dur":632, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Burst.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1758795359105694, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359105772, "dur":329, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/Unity.Burst.CodeGen.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758795359106141, "dur":1260, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Burst.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1758795359107428, "dur":106994, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359214451, "dur":4039, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1758795359218490, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359218629, "dur":5503, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1758795359224134, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359224276, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359224396, "dur":501, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1758795359224954, "dur":418, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" }}
,{ "pid":12345, "tid":2, "ts":1758795359225421, "dur":435, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":2, "ts":1758795359225903, "dur":360, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":2, "ts":1758795359226293, "dur":337, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1758795359226631, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1758795359226690, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359226819, "dur":443, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1758795359227398, "dur":563, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll" }}
,{ "pid":12345, "tid":2, "ts":1758795359228013, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359228263, "dur":706, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.AI.Navigation.dll" }}
,{ "pid":12345, "tid":2, "ts":1758795359229001, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359229159, "dur":634, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb" }}
,{ "pid":12345, "tid":2, "ts":1758795359229794, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb" }}
,{ "pid":12345, "tid":2, "ts":1758795359229848, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359230123, "dur":787, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb" }}
,{ "pid":12345, "tid":2, "ts":1758795359230910, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":2, "ts":1758795359230979, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359231193, "dur":740, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":2, "ts":1758795359231975, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359232226, "dur":799, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.pdb" }}
,{ "pid":12345, "tid":2, "ts":1758795359233026, "dur":333777, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795359566899, "dur":231, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":2, "ts":1758795359567134, "dur":495146, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359045943, "dur":14378, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359060325, "dur":245, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":3, "ts":1758795359060570, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359060729, "dur":217, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_A1002E9E9F083797.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359060947, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359061119, "dur":167, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_72E6AB956861F353.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359061287, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359061419, "dur":272, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D7A9AF8C5E67A0AE.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359061691, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359061825, "dur":210, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F56BB6A2C38EBCEC.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359062036, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359062182, "dur":295, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_D4462A08249AAAE8.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359062477, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359062587, "dur":154, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_6BADCFBF033360D0.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359062741, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359062841, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_2AA1A0FABCD57599.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359062998, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359063083, "dur":168, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_0B92978C8F2A41F2.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359063251, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359063335, "dur":124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_641E8BB24D9C991B.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359063460, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359063549, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_AD829E7041D38787.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359063691, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359063828, "dur":154, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_6DFD6E15EC2CCADA.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359063982, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359064079, "dur":192, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.rsp" }}
,{ "pid":12345, "tid":3, "ts":1758795359064271, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359064386, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3F703C20146B7E70.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359064529, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359064628, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_A4E7BFD9C85E5D10.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359064741, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359064809, "dur":134, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_45C3A9F199AE3937.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359064943, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359065021, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_E2BB2975B5B78B38.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359065156, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359065247, "dur":110, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_ECFD0B88A31ACF2A.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359065358, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359065454, "dur":132, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_BB69CBE628A12E20.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359065586, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359065698, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7EECDB7F9D30A336.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359065826, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359065906, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_3CB6A4B5DF319A4A.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359066028, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359066114, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359066235, "dur":1782, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359068017, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359068112, "dur":17497, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1758795359085609, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359085816, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_AFC2E85BC2EDBC0B.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359085924, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359086015, "dur":815, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359086830, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359086932, "dur":6650, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1758795359093582, "dur":243, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359093835, "dur":90, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_92C0142275484313.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359093926, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359094014, "dur":1346, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359095360, "dur":1212, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359096572, "dur":1436, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359098008, "dur":1338, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359099347, "dur":1328, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359100675, "dur":1228, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359101903, "dur":1257, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359103160, "dur":441, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359103602, "dur":257, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359103860, "dur":168, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359104115, "dur":304, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359104420, "dur":148, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758795359104580, "dur":594, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1758795359105254, "dur":860, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359106114, "dur":22288, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359128403, "dur":86054, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359214465, "dur":3636, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Collections.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1758795359218102, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359218226, "dur":4970, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1758795359223197, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359223293, "dur":10493, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/UnityEngine.UI.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1758795359233787, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359233908, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795359234088, "dur":849, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":3, "ts":1758795359234940, "dur":827328, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359045950, "dur":14382, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359060334, "dur":241, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.rsp2" }}
,{ "pid":12345, "tid":4, "ts":1758795359060575, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359060667, "dur":203, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_AD0F59CECD9086B5.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359060870, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359061034, "dur":191, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_AE483740772154F6.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359061225, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359061358, "dur":185, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_8FC1791ACEE3E790.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359061543, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359061687, "dur":236, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4B36DE508E234CBA.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359061923, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359062083, "dur":286, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0CB46753FA7C312F.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359062369, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359062543, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_611725854B2C7A29.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359062715, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359062809, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_11639AB4BFA07D5F.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359062951, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359063037, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_26633F92B9A939A2.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359063180, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359063278, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_56E9249B00D2323D.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359063408, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359063504, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_837685507179BFD9.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359063642, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359063772, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_D28966A0C9F1BC15.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359063936, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359064032, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359064101, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_F3B0C5707ACFC0A0.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359064218, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359064272, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_3EA0D0A7C53907A2.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359064421, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359064540, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_43A2C3499BF00BED.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359064671, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359064743, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_3F74E93A17E40DA9.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359064878, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359064968, "dur":134, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_04AD07D19869F8AC.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359065103, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359065198, "dur":124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_253FA34D0F96CF98.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359065322, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359065402, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_2019ACBA132EC117.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359065531, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359065607, "dur":160, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_1D711A2907908DC5.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359065767, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359065848, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359065947, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_6450FF118201D284.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359066070, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359066191, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359066270, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_37F04A890A90A5DB.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359066398, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359066469, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359066547, "dur":192, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":4, "ts":1758795359066740, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359066834, "dur":277, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Collections.rsp" }}
,{ "pid":12345, "tid":4, "ts":1758795359067111, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359067174, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359067262, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359067353, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359067450, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359067548, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359067657, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359067748, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359067831, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359067922, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359067996, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359068063, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359068159, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359068246, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359068338, "dur":1284, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359069622, "dur":1220, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359070842, "dur":1490, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359072332, "dur":1325, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359073658, "dur":1300, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359074958, "dur":1320, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359076278, "dur":1486, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359077764, "dur":1412, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359079177, "dur":1308, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359080485, "dur":1385, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359081871, "dur":1396, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359083267, "dur":1548, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359084816, "dur":1650, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359086467, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359086605, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359086719, "dur":1567, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1758795359088286, "dur":239, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359088575, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359088656, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_75E0D491C6D663C5.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359088723, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359088793, "dur":1315, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359090109, "dur":1441, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359091550, "dur":1354, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359092904, "dur":1431, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359094336, "dur":1259, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359095595, "dur":1288, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359096884, "dur":1453, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359098338, "dur":1328, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359099667, "dur":1263, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359100930, "dur":1259, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359102189, "dur":1290, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359103479, "dur":642, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359104122, "dur":309, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359104431, "dur":1567, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359105999, "dur":178, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758795359106177, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359106246, "dur":630, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1758795359106967, "dur":107445, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359214420, "dur":3772, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1758795359218192, "dur":157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359218354, "dur":4184, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1758795359222539, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359222622, "dur":4233, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1758795359226856, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359226976, "dur":578, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1758795359227609, "dur":615, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":4, "ts":1758795359228565, "dur":562, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":4, "ts":1758795359229128, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758795359229186, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359229330, "dur":545, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758795359230048, "dur":964, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758795359231043, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359231137, "dur":670, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.AI.Navigation.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758795359231809, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758795359231860, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359232166, "dur":785, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758795359232952, "dur":3381, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359236342, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795359236403, "dur":363, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758795359236767, "dur":825530, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359045958, "dur":14379, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359060340, "dur":88, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_320B820FE8FC5AE8.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359060428, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359060503, "dur":147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_374A8F32DB19802B.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359060650, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359060747, "dur":229, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_1349457C77F70AD4.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359060976, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359061136, "dur":153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_85BC3F815A006E37.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359061307, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359061457, "dur":193, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CF5F61BCAC5D1F90.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359061651, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359061798, "dur":289, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_C0AB20643B6FF208.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359062088, "dur":191, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359062285, "dur":258, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A18767039B605D8A.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359062543, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359062662, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_14EB62D6C267D167.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359062806, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359062900, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5BA053E42555A2B5.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359063040, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359063127, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3193DD45B90182D7.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359063278, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359063362, "dur":134, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_9A9A08C6BDB0373A.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359063496, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359063579, "dur":201, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_63177FAB2D86FAFC.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359063780, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359063886, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E921E8B78225857D.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359064016, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359064095, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_88057B9DC68CCBAB.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359064247, "dur":168, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_7E3D0D223DF58476.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359064415, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359064502, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_92EF508A30199DA1.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359064633, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359064712, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_FBD22BAAF9C7FF17.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359064833, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359064922, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_6E45C98E7E4DEAAC.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359065044, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359065140, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_A023D350F37B3A85.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359065277, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359065354, "dur":143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E18F0E0F745C010F.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359065497, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359065572, "dur":166, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_5762B19BA4A2D3B6.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359065738, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359065830, "dur":139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_8008604A4480A7B6.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359065970, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359066040, "dur":165, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359066205, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359066289, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_927C166CA6DDB07E.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359066411, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359066473, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Burst.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":5, "ts":1758795359066679, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Mathematics.rsp" }}
,{ "pid":12345, "tid":5, "ts":1758795359066749, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359066819, "dur":169, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp" }}
,{ "pid":12345, "tid":5, "ts":1758795359067039, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_F862E2FD6BA6C0C0.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359067159, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359067268, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359067343, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359067410, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359067479, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359067592, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359067683, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359067781, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359067869, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359067948, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359068017, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359068103, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359068196, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359068296, "dur":1292, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359069588, "dur":1202, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359070790, "dur":1500, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359072290, "dur":1415, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359073706, "dur":1304, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359075010, "dur":1332, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359076342, "dur":1471, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359077813, "dur":1226, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359079039, "dur":1330, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359080370, "dur":1361, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359081731, "dur":1417, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359083149, "dur":1500, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359084649, "dur":1690, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359086340, "dur":488, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359086828, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359086903, "dur":1115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1758795359088019, "dur":180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359088203, "dur":6639, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1758795359094842, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359095051, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_A2E97B59907D8CED.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359095127, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359095181, "dur":1282, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359096464, "dur":1438, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359097902, "dur":1333, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359099235, "dur":1318, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359100554, "dur":1273, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359101827, "dur":1265, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359103092, "dur":862, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359103981, "dur":89, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359104070, "dur":55, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359104125, "dur":308, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359104433, "dur":1566, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359106000, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758795359106200, "dur":812, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1758795359107013, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359107109, "dur":107324, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359214443, "dur":7216, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1758795359221659, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359221777, "dur":5394, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1758795359227171, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359227307, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359227438, "dur":593, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":5, "ts":1758795359228216, "dur":680, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1758795359228937, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359229030, "dur":602, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.pdb" }}
,{ "pid":12345, "tid":5, "ts":1758795359229659, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359229925, "dur":602, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb" }}
,{ "pid":12345, "tid":5, "ts":1758795359230556, "dur":164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359230901, "dur":547, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Burst.pdb" }}
,{ "pid":12345, "tid":5, "ts":1758795359231492, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359231807, "dur":754, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb" }}
,{ "pid":12345, "tid":5, "ts":1758795359232562, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Collections.pdb" }}
,{ "pid":12345, "tid":5, "ts":1758795359232655, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795359232949, "dur":453, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Collections.pdb" }}
,{ "pid":12345, "tid":5, "ts":1758795359233403, "dur":828855, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359045966, "dur":14379, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359060348, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_3F0AF0E8FD5B91AB.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359060416, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359060536, "dur":156, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_3BE3DE4E4FF3C20F.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359060692, "dur":157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359060852, "dur":220, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_6C13B72F801586DB.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359061072, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359061221, "dur":195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_5AFB90E5B3938F6A.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359061416, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359061554, "dur":207, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_67D1F298EAAF8408.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359061761, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359061890, "dur":240, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B861AB185A7695D4.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359062130, "dur":192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359062325, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11EA7240A47B953E.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359062563, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359062681, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_AE0AA47335F7F2B3.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359062817, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359062937, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_9A3FDE96F0266F50.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359063074, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359063193, "dur":143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_532B9929C68EE5D8.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359063336, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359063426, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_07C436A676AA1A59.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359063549, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359063629, "dur":178, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_A9F3FB8E6C0780FC.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359063808, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359063915, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_FD774B970C96B0D0.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359064052, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359064135, "dur":83, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_CFEDB20F561084C9.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359064264, "dur":139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_A191F8D8F2A28668.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359064403, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359064523, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_FCE5A639896B35E4.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359064659, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359064732, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_25888E9AFFFF520E.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359064873, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359064938, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_3C8BA2396F714A7A.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359065073, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359065150, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_089E00570BFA24CD.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359065313, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359065384, "dur":148, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_FCEE077DB856D053.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359065533, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359065632, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8BC3E661A9C77141.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359065768, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359065862, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359065971, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_57F26D0ACBE8DD19.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359066084, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359066207, "dur":119, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_40479579ECF7C4CE.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359066326, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359066414, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359066511, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359066581, "dur":154, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Burst.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":6, "ts":1758795359066783, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359066883, "dur":162, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":6, "ts":1758795359067046, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359067141, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F5791B14F366A51E.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359067279, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359067367, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359067422, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359067497, "dur":68, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp" }}
,{ "pid":12345, "tid":6, "ts":1758795359067566, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359067679, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359067768, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359067859, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359067940, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359068003, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359068078, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359068174, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359068249, "dur":1350, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359069600, "dur":1215, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359070815, "dur":1492, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359072307, "dur":1423, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359073730, "dur":1295, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359075025, "dur":1339, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359076364, "dur":1476, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359077840, "dur":648, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359078489, "dur":646, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359079136, "dur":1316, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359080453, "dur":1368, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359081821, "dur":1394, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359083216, "dur":1511, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359084728, "dur":1687, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359086416, "dur":332, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.AI.Navigation.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359086748, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359086816, "dur":6929, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.AI.Navigation.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1758795359093745, "dur":273, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359094020, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_2CF636FCAD6E4B77.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359094097, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359094184, "dur":1241, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359095425, "dur":1243, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359096668, "dur":1439, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359098107, "dur":1382, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359099490, "dur":1286, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359100777, "dur":1241, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359102018, "dur":1258, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359103277, "dur":664, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359103941, "dur":176, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359104117, "dur":309, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359104426, "dur":382, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359104809, "dur":270, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359105108, "dur":728, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1758795359105836, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359105973, "dur":226, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/Unity.Collections.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359106236, "dur":1348, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Collections.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1758795359107640, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/Unity.Collections.CodeGen.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758795359107715, "dur":269, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Collections.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1758795359108026, "dur":492, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput" }}
,{ "pid":12345, "tid":6, "ts":1758795359108804, "dur":74, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359212626, "dur":376, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359109135, "dur":103894, "ph":"X", "name": "ILPP-Configuration",  "args": { "detail":"Library/ilpp-configuration.nevergeneratedoutput" }}
,{ "pid":12345, "tid":6, "ts":1758795359214410, "dur":4297, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.AI.Navigation.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1758795359218707, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359218822, "dur":3578, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Burst.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1758795359222400, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359222482, "dur":5334, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1758795359227817, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359228019, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359228274, "dur":598, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1758795359228914, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359229159, "dur":618, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758795359229778, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758795359229832, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359230049, "dur":800, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758795359230850, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758795359230943, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359231089, "dur":603, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758795359231736, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359232019, "dur":851, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758795359232910, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795359233091, "dur":443, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.pdb" }}
,{ "pid":12345, "tid":6, "ts":1758795359233536, "dur":828739, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359045974, "dur":14377, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359060355, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_ACDD54761A8A8AD1.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359060408, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359060505, "dur":153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_E409852C73E3C815.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359060658, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359060785, "dur":231, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_A3723152F274B049.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359061017, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359061157, "dur":159, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_51F928846D19D7E2.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359061316, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359061469, "dur":188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_23AD0A7361E5AF91.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359061658, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359061803, "dur":211, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_05C8CFED34CB8739.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359062015, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359062168, "dur":284, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_ED8B032DF01A4A2C.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359062452, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359062580, "dur":156, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B4E6D214D3E2A1D4.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359062736, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359062826, "dur":141, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_4EB5407DF9A5D812.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359062968, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359063058, "dur":153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_730EC33EED2E94C6.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359063211, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359063307, "dur":133, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_2E74B195F350D70F.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359063440, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359063522, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_D53BC0BA2A1258D1.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359063649, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359063809, "dur":153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_463AABC76FBC4B88.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359063962, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359064064, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359064186, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359064242, "dur":221, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_68AFAE2ACBA2FE65.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359064464, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359064555, "dur":132, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_E82236F3A6561981.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359064687, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359064758, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_B684BE6BA7888589.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359064894, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359064964, "dur":134, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_E60F1C45244EBAC4.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359065098, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359065163, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_BE55FC8DDE197785.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359065292, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359065356, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_F7D7EA5E200864EA.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359065513, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359065576, "dur":161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_1EB82EF2C9AE7379.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359065738, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359065810, "dur":156, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_BDCC4BA5B011F2E9.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359065967, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359066030, "dur":165, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_F6EE2176487706CE.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359066196, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359066257, "dur":145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_4190A0BFBFAECF93.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359066402, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359066478, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359066542, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359066607, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Burst.rsp" }}
,{ "pid":12345, "tid":7, "ts":1758795359066742, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359066852, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359066932, "dur":143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Burst.CodeGen.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":7, "ts":1758795359067075, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359067164, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359067275, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359067355, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359067443, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359067518, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359067628, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359067732, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359067819, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359067896, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359067980, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359068051, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359068147, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359068247, "dur":1333, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359069580, "dur":1201, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359070782, "dur":1492, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359072275, "dur":1444, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359073719, "dur":1283, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359075002, "dur":1352, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359076355, "dur":1091, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359077447, "dur":1384, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359078831, "dur":1356, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359080188, "dur":1318, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359081506, "dur":1448, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359082955, "dur":1460, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359084416, "dur":1712, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359086129, "dur":961, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359087140, "dur":17196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1758795359104404, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359104484, "dur":1350, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1758795359105834, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359105909, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_C67A4099BF12A775.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359105992, "dur":264, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758795359106285, "dur":867, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1758795359107155, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359107271, "dur":107418, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359214693, "dur":3282, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1758795359217976, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359218071, "dur":4199, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1758795359222270, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359222357, "dur":9168, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1758795359231525, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359231678, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":7, "ts":1758795359231732, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359231986, "dur":939, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":7, "ts":1758795359232926, "dur":955, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795359234017, "dur":934, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":7, "ts":1758795359234953, "dur":827287, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359045981, "dur":14402, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359060432, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359060516, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_C6011564DA201A52.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359060631, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359060766, "dur":250, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_2CF19CB4A1AF158C.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359061017, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359061165, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_C49C0FC47CAFEAC5.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359061316, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359061465, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1DB14EE0893E74EF.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359061637, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359061774, "dur":185, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_99C535C3D6E374C3.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359061959, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359062130, "dur":317, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_F2316A31D98809E8.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359062447, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359062566, "dur":176, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_BF21A61A4CC82206.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359062742, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359062848, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_910BD9D1CE0E2BD1.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359062970, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359063068, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_9663AEF5A1DB96B8.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359063215, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359063319, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3CEA7E364D77D73C.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359063442, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359063529, "dur":116, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_CEEE8E02F8AB65CD.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359063645, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359063791, "dur":155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_734ECF7F5E8D9BFB.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359063946, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359064038, "dur":255, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.UI.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":8, "ts":1758795359064293, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359064398, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_2296864AE3FD3588.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359064556, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359064648, "dur":114, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_CDA841E1F3BEFCEA.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359064762, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359064859, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_FDC92B42D20F9DB0.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359064963, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359065035, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_A5AE20A98D66BE2A.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359065174, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359065265, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_3B66C730F6ED4874.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359065385, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359065456, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_D49580F88C8A9124.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359065593, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359065704, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_010CE801CBBC8038.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359065826, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359065934, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_6F2DFF6A42ECB2AA.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359066051, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359066136, "dur":239, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.UI.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":8, "ts":1758795359066375, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359066450, "dur":2927, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359069377, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359069444, "dur":16183, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1758795359085628, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359085845, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_DB7439BD0E712D01.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359085952, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359086033, "dur":1137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Burst.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359087216, "dur":7174, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Burst.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1758795359094391, "dur":176, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359094572, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_59592FE1C19AAD4D.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359094622, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359094693, "dur":2575, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Collections.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758795359097269, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359097350, "dur":6808, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Collections.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1758795359104266, "dur":557, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1758795359104824, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359104955, "dur":1175, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359106130, "dur":108365, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359214498, "dur":3908, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Mathematics.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1758795359218406, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359218548, "dur":4923, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1758795359223472, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795359223582, "dur":12708, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1758795359236397, "dur":330, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":8, "ts":1758795359236730, "dur":825537, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359045989, "dur":14401, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359060394, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_3A024D3CFAEFB3CE.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359060470, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359060555, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_2338B6331E4A589D.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359060732, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359060881, "dur":244, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_646E6BF09BC34AF8.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359061126, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359061250, "dur":161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_05CCC7FD7CF0316D.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359061412, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359061543, "dur":202, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_761201DEB2FFD5AE.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359061745, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359061847, "dur":207, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_95D79878B91D771D.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359062054, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359062230, "dur":279, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_5F70C3E0135C03E6.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359062509, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359062638, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_7D46FF134A659955.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359062778, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359062867, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_102C078AEE4D5C34.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359063017, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359063094, "dur":159, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_BBB5D930EB61518F.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359063253, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359063347, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_71D986BE924F9760.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359063476, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359063563, "dur":161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_CABAF2279C00D800.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359063725, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359063855, "dur":147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_46B2EC7768D1E935.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359064002, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359064077, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2A5CE0F0450FED37.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359064199, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359064307, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_4EE083DBDE9DCEDF.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359064471, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359064570, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_39A2FB794417C6BF.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359064690, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359064754, "dur":145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_CD3029A75D6D7A40.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359064900, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359064984, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_65B4114BBD2E3965.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359065101, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359065184, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_66BA61F6E735559C.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359065313, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359065371, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_12D58D670DB4180F.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359065513, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359065589, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_9AAFF76B45D04135.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359065738, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359065813, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_7B7BFE77A0C7AD62.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359065970, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359066034, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2F018D732F244A87.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359066191, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359066267, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_49F2D3B3C857FB89.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359066396, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359066478, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359066538, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359066739, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359066832, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359066913, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359067020, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359067111, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_0CCB9384721BA6A1.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359067247, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359067323, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359067401, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359067489, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359067616, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359067717, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359067800, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359067878, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359067963, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359068039, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359068138, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359068215, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359068316, "dur":1290, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359069606, "dur":1215, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359070821, "dur":1496, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359072317, "dur":1247, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359073564, "dur":1288, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359074853, "dur":1328, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359076181, "dur":1468, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359077650, "dur":1388, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359079038, "dur":1325, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359080364, "dur":1361, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359081725, "dur":1398, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359083123, "dur":1521, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359084644, "dur":1683, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359086328, "dur":716, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359087093, "dur":1436, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1758795359088529, "dur":231, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359088765, "dur":83, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_F007F1EECED691E3.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359088848, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359088952, "dur":1275, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359090228, "dur":1484, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359091712, "dur":1298, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359093010, "dur":1390, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359094401, "dur":1287, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359095688, "dur":1340, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359097029, "dur":1424, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359098453, "dur":1322, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359099775, "dur":1266, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359101042, "dur":1279, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359102321, "dur":1243, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359103594, "dur":86, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359103680, "dur":316, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359104119, "dur":290, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359104410, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359104518, "dur":1919, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1758795359106437, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359106561, "dur":98, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758795359106688, "dur":1405, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1758795359108093, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359108244, "dur":218, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1758795359108535, "dur":176, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1758795359109079, "dur":124013, "ph":"X", "name": "Csc",  "args": { "detail":"Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1758795359236920, "dur":1222, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Assembly-CSharp.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1758795359238672, "dur":211, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359563567, "dur":295, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795359239502, "dur":324370, "ph":"X", "name": "ILPostProcess",  "args": { "detail":"Library/Bee/artifacts/1300b0aP.dag/post-processed/Assembly-CSharp.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1758795359566833, "dur":224, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359567069, "dur":87, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359567200, "dur":90, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359567296, "dur":125, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359567427, "dur":1502, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359568972, "dur":1317, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359570328, "dur":88, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359570441, "dur":1109, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359571623, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359571728, "dur":74, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359571830, "dur":1198, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359573063, "dur":175, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359573243, "dur":142, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359573397, "dur":1161, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359574564, "dur":111, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359574711, "dur":80, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":9, "ts":1758795359567060, "dur":7741, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":9, "ts":1758795359575317, "dur":486849, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":10, "ts":1758795359045996, "dur":14407, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359060420, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_C57112EE168C0CEE.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359060575, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_139664EF1148BEA3.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359060726, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359060870, "dur":239, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_788EC0AD0DD1252A.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359061109, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359061248, "dur":189, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_BB076BE8E91D3844.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359061437, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359061563, "dur":191, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_29F333B2BA915C4E.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359061754, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359061921, "dur":206, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_5341A2509D5ED266.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359062127, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359062319, "dur":229, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BB987306F50BED91.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359062548, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359062678, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_7FB03B9F43028903.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359062808, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359062904, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_56092ADF71655F79.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359063041, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359063143, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_21D14335F6F8B5A4.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359063289, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359063373, "dur":132, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_7E945FCC8473B7C7.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359063505, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359063586, "dur":204, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_2F7C47F2C923EECA.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359063790, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359063900, "dur":147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_8B47C372A80AB1D5.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359064047, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359064123, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B98DEA3DACE5B6F2.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359064226, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359064340, "dur":154, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_DF724CA3757AAEF4.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359064494, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359064590, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_F3361C9D618CC613.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359064710, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359064792, "dur":116, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F1A015DAD66E97D.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359064908, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359064995, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_AAFF8212FFC78F4B.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359065121, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359065213, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_397B254DA19E067F.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359065334, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359065421, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_C9170BEF06746105.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359065559, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359065660, "dur":132, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_CE4E24FD428232DA.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359065792, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359065889, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_6BB7F73D7038074E.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359066009, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359066095, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359066241, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359066337, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359066435, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359066529, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359066595, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359066817, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359066906, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.rsp2" }}
,{ "pid":12345, "tid":10, "ts":1758795359067047, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359067146, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_59627543C51E81A1.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359067273, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359067338, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359067410, "dur":194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.rsp" }}
,{ "pid":12345, "tid":10, "ts":1758795359067604, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359067699, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359067786, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359067865, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359067945, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359068009, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359068097, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359068224, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359068328, "dur":1291, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359069619, "dur":1213, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359070833, "dur":1495, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359072328, "dur":1424, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359073752, "dur":1307, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359075059, "dur":1342, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359076402, "dur":1461, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359077863, "dur":631, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359078494, "dur":671, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359079165, "dur":1317, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359080482, "dur":1423, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359081905, "dur":1389, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359083295, "dur":1509, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359084805, "dur":1643, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359086449, "dur":375, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359086859, "dur":8740, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1758795359095599, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359095757, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_08E11C037DD39E3A.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758795359095826, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359095886, "dur":1376, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359097263, "dur":1350, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359098614, "dur":1318, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359099933, "dur":1253, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359101186, "dur":1344, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359102531, "dur":1239, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359103771, "dur":212, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359104144, "dur":293, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359104437, "dur":1674, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359106111, "dur":20074, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359126391, "dur":203, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":10, "ts":1758795359126594, "dur":1183, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner" }}
,{ "pid":12345, "tid":10, "ts":1758795359127777, "dur":613, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger" }}
,{ "pid":12345, "tid":10, "ts":1758795359126186, "dur":2207, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359128393, "dur":86074, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359214471, "dur":4826, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.InputSystem.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1758795359219298, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359219425, "dur":3656, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1758795359223082, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359223187, "dur":8754, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Timeline.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1758795359231941, "dur":187, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359232130, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":10, "ts":1758795359232185, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795359232565, "dur":630, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":10, "ts":1758795359233196, "dur":829066, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795360067266, "dur":499, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 37977, "tid": 668, "ts": 1758795360069954, "dur": 99063, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 37977, "tid": 668, "ts": 1758795360169081, "dur": 472, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 37977, "tid": 668, "ts": 1758795360068849, "dur": 100720, "ph": "X", "name": "Write chrome-trace events", "args": {} },
