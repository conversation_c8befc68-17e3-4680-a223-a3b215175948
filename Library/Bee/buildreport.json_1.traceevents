{ "pid": 37977, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 37977, "tid": 1, "ts": 1758794731085174, "dur": 2805, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 37977, "tid": 1, "ts": 1758794731087982, "dur": 90926, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 37977, "tid": 1, "ts": 1758794731178913, "dur": 28451, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 37977, "tid": 594, "ts": 1758794731332748, "dur": 10, "ph": "X", "name": "", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731084886, "dur": 34573, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731119463, "dur": 212248, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731119484, "dur": 63, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731119557, "dur": 504, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731120065, "dur": 6, "ph": "X", "name": "ProcessMessages 53", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731120072, "dur": 8066, "ph": "X", "name": "ReadAsync 53", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128142, "dur": 4, "ph": "X", "name": "ProcessMessages 3302", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128147, "dur": 31, "ph": "X", "name": "ReadAsync 3302", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128192, "dur": 1, "ph": "X", "name": "ProcessMessages 1275", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128194, "dur": 36, "ph": "X", "name": "ReadAsync 1275", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128233, "dur": 39, "ph": "X", "name": "ReadAsync 636", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128274, "dur": 1, "ph": "X", "name": "ProcessMessages 980", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128276, "dur": 39, "ph": "X", "name": "ReadAsync 980", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128316, "dur": 1, "ph": "X", "name": "ProcessMessages 1162", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128318, "dur": 41, "ph": "X", "name": "ReadAsync 1162", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128364, "dur": 1, "ph": "X", "name": "ProcessMessages 1449", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128366, "dur": 35, "ph": "X", "name": "ReadAsync 1449", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128403, "dur": 1, "ph": "X", "name": "ProcessMessages 791", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128404, "dur": 30, "ph": "X", "name": "ReadAsync 791", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128436, "dur": 1, "ph": "X", "name": "ProcessMessages 1062", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128438, "dur": 20, "ph": "X", "name": "ReadAsync 1062", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128459, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128461, "dur": 26, "ph": "X", "name": "ReadAsync 818", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128491, "dur": 18, "ph": "X", "name": "ReadAsync 756", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128510, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128512, "dur": 27, "ph": "X", "name": "ReadAsync 777", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128544, "dur": 46, "ph": "X", "name": "ReadAsync 711", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128592, "dur": 1, "ph": "X", "name": "ProcessMessages 976", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128595, "dur": 22, "ph": "X", "name": "ReadAsync 976", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128617, "dur": 1, "ph": "X", "name": "ProcessMessages 1037", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128619, "dur": 30, "ph": "X", "name": "ReadAsync 1037", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128652, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128654, "dur": 25, "ph": "X", "name": "ReadAsync 877", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128683, "dur": 1, "ph": "X", "name": "ProcessMessages 926", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128684, "dur": 43, "ph": "X", "name": "ReadAsync 926", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128728, "dur": 1, "ph": "X", "name": "ProcessMessages 1554", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128730, "dur": 38, "ph": "X", "name": "ReadAsync 1554", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128785, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128786, "dur": 28, "ph": "X", "name": "ReadAsync 758", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128817, "dur": 1, "ph": "X", "name": "ProcessMessages 1366", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128820, "dur": 33, "ph": "X", "name": "ReadAsync 1366", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128854, "dur": 1, "ph": "X", "name": "ProcessMessages 1064", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128856, "dur": 21, "ph": "X", "name": "ReadAsync 1064", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128880, "dur": 47, "ph": "X", "name": "ReadAsync 823", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128928, "dur": 1, "ph": "X", "name": "ProcessMessages 1072", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128933, "dur": 36, "ph": "X", "name": "ReadAsync 1072", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731128980, "dur": 1281, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130266, "dur": 6, "ph": "X", "name": "ProcessMessages 8160", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130273, "dur": 34, "ph": "X", "name": "ReadAsync 8160", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130310, "dur": 7, "ph": "X", "name": "ProcessMessages 1389", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130318, "dur": 40, "ph": "X", "name": "ReadAsync 1389", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130359, "dur": 1, "ph": "X", "name": "ProcessMessages 897", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130361, "dur": 28, "ph": "X", "name": "ReadAsync 897", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130391, "dur": 1, "ph": "X", "name": "ProcessMessages 1014", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130393, "dur": 19, "ph": "X", "name": "ReadAsync 1014", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130413, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130416, "dur": 55, "ph": "X", "name": "ReadAsync 503", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130475, "dur": 2, "ph": "X", "name": "ProcessMessages 613", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130479, "dur": 52, "ph": "X", "name": "ReadAsync 613", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130534, "dur": 3, "ph": "X", "name": "ProcessMessages 1675", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130539, "dur": 44, "ph": "X", "name": "ReadAsync 1675", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130586, "dur": 1, "ph": "X", "name": "ProcessMessages 1180", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130592, "dur": 30, "ph": "X", "name": "ReadAsync 1180", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130623, "dur": 1, "ph": "X", "name": "ProcessMessages 921", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130625, "dur": 56, "ph": "X", "name": "ReadAsync 921", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130683, "dur": 1, "ph": "X", "name": "ProcessMessages 1088", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130685, "dur": 26, "ph": "X", "name": "ReadAsync 1088", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130712, "dur": 1, "ph": "X", "name": "ProcessMessages 1293", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130714, "dur": 49, "ph": "X", "name": "ReadAsync 1293", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130764, "dur": 1, "ph": "X", "name": "ProcessMessages 925", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130766, "dur": 30, "ph": "X", "name": "ReadAsync 925", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130797, "dur": 1, "ph": "X", "name": "ProcessMessages 1147", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130799, "dur": 52, "ph": "X", "name": "ReadAsync 1147", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731130853, "dur": 838, "ph": "X", "name": "ReadAsync 645", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731131693, "dur": 6, "ph": "X", "name": "ProcessMessages 8122", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731131699, "dur": 24, "ph": "X", "name": "ReadAsync 8122", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731131726, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731131728, "dur": 44, "ph": "X", "name": "ReadAsync 710", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731131775, "dur": 2, "ph": "X", "name": "ProcessMessages 1282", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731131780, "dur": 514, "ph": "X", "name": "ReadAsync 1282", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132296, "dur": 5, "ph": "X", "name": "ProcessMessages 6359", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132310, "dur": 31, "ph": "X", "name": "ReadAsync 6359", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132347, "dur": 5, "ph": "X", "name": "ProcessMessages 8131", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132353, "dur": 34, "ph": "X", "name": "ReadAsync 8131", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132399, "dur": 22, "ph": "X", "name": "ReadAsync 678", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132424, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132425, "dur": 33, "ph": "X", "name": "ReadAsync 818", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132465, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132467, "dur": 22, "ph": "X", "name": "ReadAsync 554", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132491, "dur": 1, "ph": "X", "name": "ProcessMessages 1009", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132494, "dur": 46, "ph": "X", "name": "ReadAsync 1009", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132543, "dur": 2, "ph": "X", "name": "ProcessMessages 1326", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132546, "dur": 176, "ph": "X", "name": "ReadAsync 1326", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132726, "dur": 2, "ph": "X", "name": "ProcessMessages 880", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132730, "dur": 48, "ph": "X", "name": "ReadAsync 880", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132781, "dur": 2, "ph": "X", "name": "ProcessMessages 1378", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132784, "dur": 39, "ph": "X", "name": "ReadAsync 1378", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132825, "dur": 1, "ph": "X", "name": "ProcessMessages 1325", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132827, "dur": 31, "ph": "X", "name": "ReadAsync 1325", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132862, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132864, "dur": 38, "ph": "X", "name": "ReadAsync 875", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132904, "dur": 1, "ph": "X", "name": "ProcessMessages 921", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132908, "dur": 41, "ph": "X", "name": "ReadAsync 921", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132951, "dur": 2, "ph": "X", "name": "ProcessMessages 1190", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132962, "dur": 29, "ph": "X", "name": "ReadAsync 1190", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132993, "dur": 2, "ph": "X", "name": "ProcessMessages 1191", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731132996, "dur": 32, "ph": "X", "name": "ReadAsync 1191", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133030, "dur": 2, "ph": "X", "name": "ProcessMessages 1079", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133034, "dur": 33, "ph": "X", "name": "ReadAsync 1079", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133070, "dur": 1, "ph": "X", "name": "ProcessMessages 1073", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133073, "dur": 38, "ph": "X", "name": "ReadAsync 1073", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133118, "dur": 1, "ph": "X", "name": "ProcessMessages 1161", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133121, "dur": 34, "ph": "X", "name": "ReadAsync 1161", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133157, "dur": 2, "ph": "X", "name": "ProcessMessages 1087", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133161, "dur": 37, "ph": "X", "name": "ReadAsync 1087", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133200, "dur": 1, "ph": "X", "name": "ProcessMessages 1324", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133203, "dur": 30, "ph": "X", "name": "ReadAsync 1324", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133235, "dur": 1, "ph": "X", "name": "ProcessMessages 930", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133237, "dur": 34, "ph": "X", "name": "ReadAsync 930", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133273, "dur": 1, "ph": "X", "name": "ProcessMessages 840", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133277, "dur": 38, "ph": "X", "name": "ReadAsync 840", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133317, "dur": 1, "ph": "X", "name": "ProcessMessages 1214", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731133320, "dur": 1255, "ph": "X", "name": "ReadAsync 1214", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134579, "dur": 7, "ph": "X", "name": "ProcessMessages 8116", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134588, "dur": 52, "ph": "X", "name": "ReadAsync 8116", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134642, "dur": 2, "ph": "X", "name": "ProcessMessages 2293", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134649, "dur": 49, "ph": "X", "name": "ReadAsync 2293", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134701, "dur": 2, "ph": "X", "name": "ProcessMessages 1621", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134704, "dur": 37, "ph": "X", "name": "ReadAsync 1621", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134742, "dur": 1, "ph": "X", "name": "ProcessMessages 1288", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134745, "dur": 29, "ph": "X", "name": "ReadAsync 1288", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134775, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134776, "dur": 27, "ph": "X", "name": "ReadAsync 832", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134805, "dur": 2, "ph": "X", "name": "ProcessMessages 682", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134808, "dur": 36, "ph": "X", "name": "ReadAsync 682", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134848, "dur": 2, "ph": "X", "name": "ProcessMessages 1178", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134852, "dur": 38, "ph": "X", "name": "ReadAsync 1178", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134894, "dur": 4, "ph": "X", "name": "ProcessMessages 1391", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134898, "dur": 29, "ph": "X", "name": "ReadAsync 1391", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134930, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134932, "dur": 28, "ph": "X", "name": "ReadAsync 957", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134961, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134962, "dur": 27, "ph": "X", "name": "ReadAsync 880", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134992, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731134995, "dur": 37, "ph": "X", "name": "ReadAsync 714", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135033, "dur": 1, "ph": "X", "name": "ProcessMessages 1047", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135037, "dur": 41, "ph": "X", "name": "ReadAsync 1047", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135080, "dur": 1, "ph": "X", "name": "ProcessMessages 1053", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135082, "dur": 27, "ph": "X", "name": "ReadAsync 1053", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135115, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135118, "dur": 35, "ph": "X", "name": "ReadAsync 745", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135157, "dur": 2, "ph": "X", "name": "ProcessMessages 1087", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135175, "dur": 31, "ph": "X", "name": "ReadAsync 1087", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135207, "dur": 1, "ph": "X", "name": "ProcessMessages 1415", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135209, "dur": 37, "ph": "X", "name": "ReadAsync 1415", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135248, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135251, "dur": 105, "ph": "X", "name": "ReadAsync 341", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135359, "dur": 2, "ph": "X", "name": "ProcessMessages 1548", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135362, "dur": 51, "ph": "X", "name": "ReadAsync 1548", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135417, "dur": 1, "ph": "X", "name": "ProcessMessages 1560", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135419, "dur": 42, "ph": "X", "name": "ReadAsync 1560", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135462, "dur": 1, "ph": "X", "name": "ProcessMessages 833", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135464, "dur": 29, "ph": "X", "name": "ReadAsync 833", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135496, "dur": 2, "ph": "X", "name": "ProcessMessages 835", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135499, "dur": 39, "ph": "X", "name": "ReadAsync 835", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135539, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135542, "dur": 41, "ph": "X", "name": "ReadAsync 712", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135585, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135588, "dur": 352, "ph": "X", "name": "ReadAsync 1017", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731135942, "dur": 6, "ph": "X", "name": "ProcessMessages 7529", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731136735, "dur": 22, "ph": "X", "name": "ReadAsync 7529", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731136758, "dur": 4, "ph": "X", "name": "ProcessMessages 5534", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731136773, "dur": 86, "ph": "X", "name": "ReadAsync 5534", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731136863, "dur": 2, "ph": "X", "name": "ProcessMessages 744", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731136866, "dur": 227, "ph": "X", "name": "ReadAsync 744", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731137095, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731137097, "dur": 199, "ph": "X", "name": "ReadAsync 756", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731137298, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731137300, "dur": 168, "ph": "X", "name": "ReadAsync 482", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731137471, "dur": 60, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731137534, "dur": 1, "ph": "X", "name": "ProcessMessages 858", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731137536, "dur": 171, "ph": "X", "name": "ReadAsync 858", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731137710, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731137713, "dur": 24, "ph": "X", "name": "ReadAsync 737", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731137751, "dur": 2563, "ph": "X", "name": "ReadAsync 217", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731140315, "dur": 6, "ph": "X", "name": "ProcessMessages 8168", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731140323, "dur": 22, "ph": "X", "name": "ReadAsync 8168", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731140346, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731140347, "dur": 1381, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731141730, "dur": 30, "ph": "X", "name": "ReadAsync 319", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731141761, "dur": 4, "ph": "X", "name": "ProcessMessages 6434", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731141766, "dur": 159, "ph": "X", "name": "ReadAsync 6434", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731141927, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731141929, "dur": 26, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731141958, "dur": 191, "ph": "X", "name": "ReadAsync 551", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731142152, "dur": 2, "ph": "X", "name": "ProcessMessages 1056", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731142162, "dur": 50, "ph": "X", "name": "ReadAsync 1056", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731142216, "dur": 1, "ph": "X", "name": "ProcessMessages 821", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731142219, "dur": 32, "ph": "X", "name": "ReadAsync 821", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731142253, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731142255, "dur": 235, "ph": "X", "name": "ReadAsync 520", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731142493, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731142496, "dur": 151, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731142648, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731142650, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731142687, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731142720, "dur": 150, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731142874, "dur": 63, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731142940, "dur": 91, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143032, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143036, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143129, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143131, "dur": 27, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143160, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143163, "dur": 142, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143307, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143447, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143450, "dur": 44, "ph": "X", "name": "ReadAsync 76", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143496, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143497, "dur": 96, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143597, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143639, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143642, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143692, "dur": 184, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143879, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143882, "dur": 104, "ph": "X", "name": "ReadAsync 160", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143989, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731143991, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731144026, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731144029, "dur": 54, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731144086, "dur": 155, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731144243, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731144245, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731144310, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731144312, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731144347, "dur": 165, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731144513, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731144515, "dur": 342, "ph": "X", "name": "ReadAsync 160", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731144858, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731144860, "dur": 213, "ph": "X", "name": "ReadAsync 124", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731145075, "dur": 2, "ph": "X", "name": "ProcessMessages 276", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731145079, "dur": 52, "ph": "X", "name": "ReadAsync 276", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731145135, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731145175, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731145230, "dur": 152, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731145385, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731145387, "dur": 70, "ph": "X", "name": "ReadAsync 108", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731145460, "dur": 83, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731145546, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731145659, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731145661, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731145695, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731145732, "dur": 430, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146163, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146166, "dur": 76, "ph": "X", "name": "ReadAsync 304", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146244, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146245, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146288, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146291, "dur": 58, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146353, "dur": 136, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146495, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146498, "dur": 47, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146546, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146548, "dur": 178, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146728, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146729, "dur": 39, "ph": "X", "name": "ReadAsync 124", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146771, "dur": 158, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146945, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146947, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146987, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731146990, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147051, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147158, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147161, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147195, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147298, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147300, "dur": 57, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147359, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147478, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147520, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147522, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147566, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147668, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147670, "dur": 40, "ph": "X", "name": "ReadAsync 124", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147713, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147789, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147837, "dur": 41, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147881, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147885, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147926, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147928, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731147985, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148044, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148046, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148104, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148110, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148157, "dur": 125, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148284, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148287, "dur": 49, "ph": "X", "name": "ReadAsync 76", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148342, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148344, "dur": 80, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148426, "dur": 233, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148661, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148664, "dur": 47, "ph": "X", "name": "ReadAsync 304", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148713, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148715, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148794, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148795, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148835, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148838, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148888, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148890, "dur": 44, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148936, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148939, "dur": 35, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731148977, "dur": 95, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149074, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149076, "dur": 91, "ph": "X", "name": "ReadAsync 108", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149169, "dur": 47, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149219, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149221, "dur": 92, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149314, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149316, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149360, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149438, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149486, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149488, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149534, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149568, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149608, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149664, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731149739, "dur": 1894, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731151652, "dur": 11778, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731163437, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731163439, "dur": 1311, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731164754, "dur": 5182, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731169943, "dur": 4, "ph": "X", "name": "ProcessMessages 304", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731169951, "dur": 840, "ph": "X", "name": "ReadAsync 304", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731170796, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731170798, "dur": 2581, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731173384, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731173386, "dur": 204, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731173594, "dur": 213, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731173817, "dur": 912, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731174734, "dur": 145, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731174881, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731174883, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731174926, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731175086, "dur": 783, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731175872, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731175874, "dur": 1857, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731177735, "dur": 316, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731178055, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731178212, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731178213, "dur": 829, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731179046, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731179203, "dur": 3980, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731183187, "dur": 3189, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731186380, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731186382, "dur": 425, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731187038, "dur": 6, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731187048, "dur": 78, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731187128, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731187130, "dur": 93, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731187227, "dur": 341, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731187572, "dur": 191, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731187767, "dur": 99, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731187868, "dur": 636, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731188507, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731188849, "dur": 234, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731189085, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731189086, "dur": 112, "ph": "X", "name": "ReadAsync 160", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731189201, "dur": 76, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731189294, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731189323, "dur": 511, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731189837, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731189936, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731189938, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731189991, "dur": 45, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731190039, "dur": 281, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731190323, "dur": 152, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731190477, "dur": 134, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731190614, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731190719, "dur": 173, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731190895, "dur": 353, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731191250, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731191255, "dur": 96296, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731287557, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731287560, "dur": 36, "ph": "X", "name": "ReadAsync 8192", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731287599, "dur": 29, "ph": "X", "name": "ReadAsync 8192", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731287630, "dur": 48, "ph": "X", "name": "ReadAsync 8192", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731287690, "dur": 28, "ph": "X", "name": "ReadAsync 8192", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731287720, "dur": 20, "ph": "X", "name": "ReadAsync 8192", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731287743, "dur": 20, "ph": "X", "name": "ReadAsync 8192", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731287764, "dur": 39, "ph": "X", "name": "ProcessMessages 3232", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731287805, "dur": 5850, "ph": "X", "name": "ReadAsync 3232", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731293657, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731293658, "dur": 245, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731293908, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731293911, "dur": 151, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731294065, "dur": 259, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731294327, "dur": 1026, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731295357, "dur": 301, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731295668, "dur": 3871, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731299542, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731299641, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731299643, "dur": 35, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731299681, "dur": 258, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731299942, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731300051, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731300172, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731300174, "dur": 170, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731300346, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731300523, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731300525, "dur": 96, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731300625, "dur": 265, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731300892, "dur": 23, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731300917, "dur": 246, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731301165, "dur": 32, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731301198, "dur": 91, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731301292, "dur": 103, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731301398, "dur": 99, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731301498, "dur": 8, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731301507, "dur": 124, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731301633, "dur": 233, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731301868, "dur": 25, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731301894, "dur": 72, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731301967, "dur": 292, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731302261, "dur": 55, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731302318, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731302320, "dur": 129, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731302451, "dur": 27, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731302480, "dur": 98, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731302580, "dur": 9, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731302590, "dur": 198, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731302791, "dur": 161, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731302956, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731303053, "dur": 20, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731303074, "dur": 41, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731303118, "dur": 11, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731303130, "dur": 121, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731303252, "dur": 10, "ph": "X", "name": "ProcessMessages 106", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731303264, "dur": 253, "ph": "X", "name": "ReadAsync 106", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731303519, "dur": 14, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731303534, "dur": 257, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731303792, "dur": 22, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731303815, "dur": 56, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731303873, "dur": 11, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731303885, "dur": 44, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731303931, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731303933, "dur": 91, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304026, "dur": 8, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304034, "dur": 59, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304095, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304097, "dur": 134, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304234, "dur": 260, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304495, "dur": 18, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304515, "dur": 90, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304606, "dur": 23, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304630, "dur": 43, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304675, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304677, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304723, "dur": 7, "ph": "X", "name": "ProcessMessages 86", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304731, "dur": 78, "ph": "X", "name": "ReadAsync 86", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304810, "dur": 11, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304822, "dur": 44, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304868, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304870, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731304963, "dur": 333, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731305298, "dur": 19, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731305319, "dur": 89, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731305410, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731305412, "dur": 37, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731305450, "dur": 8, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731305459, "dur": 40, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731305500, "dur": 7, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731305509, "dur": 105, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731305616, "dur": 13, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731305630, "dur": 156, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731305787, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731305790, "dur": 110, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731305901, "dur": 7, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731305910, "dur": 76, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731305987, "dur": 17, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306006, "dur": 44, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306052, "dur": 8, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306062, "dur": 126, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306189, "dur": 10, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306201, "dur": 96, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306299, "dur": 10, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306310, "dur": 113, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306425, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306427, "dur": 148, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306576, "dur": 24, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306602, "dur": 95, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306698, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306700, "dur": 108, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306811, "dur": 9, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306821, "dur": 71, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731306894, "dur": 134, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307045, "dur": 33, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307079, "dur": 8, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307089, "dur": 123, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307214, "dur": 7, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307222, "dur": 463, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307687, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307689, "dur": 78, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307770, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307771, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307836, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307840, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307880, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307882, "dur": 45, "ph": "X", "name": "ReadAsync 56", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307930, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307932, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307969, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731307971, "dur": 152, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731308125, "dur": 18, "ph": "X", "name": "ProcessMessages 148", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731308144, "dur": 126, "ph": "X", "name": "ReadAsync 148", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731308271, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731308273, "dur": 165, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731308440, "dur": 8, "ph": "X", "name": "ProcessMessages 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731308449, "dur": 41, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731308492, "dur": 19, "ph": "X", "name": "ProcessMessages 104", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731308513, "dur": 37, "ph": "X", "name": "ReadAsync 104", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731308552, "dur": 34, "ph": "X", "name": "ProcessMessages 126", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731308587, "dur": 43, "ph": "X", "name": "ReadAsync 126", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731308633, "dur": 21, "ph": "X", "name": "ProcessMessages 108", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731308660, "dur": 13797, "ph": "X", "name": "ReadAsync 108", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731322462, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731322464, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 167503724544, "ts": 1758794731322582, "dur": 9122, "ph": "X", "name": "ReadAsync 25", "args": {} },
{ "pid": 37977, "tid": 594, "ts": 1758794731332772, "dur": 926, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 37977, "tid": 163208757248, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 37977, "tid": 163208757248, "ts": 1758794731084760, "dur": 122626, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 37977, "tid": 163208757248, "ts": 1758794731207388, "dur": 76, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 37977, "tid": 594, "ts": 1758794731333714, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 37977, "tid": 158913789952, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 37977, "tid": 158913789952, "ts": 1758794731066036, "dur": 265880, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 37977, "tid": 158913789952, "ts": 1758794731066263, "dur": 18399, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 37977, "tid": 158913789952, "ts": 1758794731331928, "dur": 359, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 37977, "tid": 158913789952, "ts": 1758794731331962, "dur": 290, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 37977, "tid": 594, "ts": 1758794731333720, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1758794731120357, "dur":1294, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794731121655, "dur":7148, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794731128884, "dur":106, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794731130730, "dur":536, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1758794731132334, "dur":361, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1758794731133278, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1758794731134602, "dur":961, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3CEA7E364D77D73C.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1758794731140660, "dur":652, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb" }}
,{ "pid":12345, "tid":0, "ts":1758794731128996, "dur":14250, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794731143251, "dur":180141, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794731323531, "dur":3536, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1758794731128924, "dur":14336, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731143266, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":1, "ts":1758794731143465, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_621CCEC35B13C3FF.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731143594, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731143696, "dur":187, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_CEAF7E5E7A4F62D9.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731143884, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731143995, "dur":133, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_BD28D25607AEE2E1.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731144128, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731144238, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_C1844920F06A43FF.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731144415, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731144515, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_A83C1C8603B0CB24.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731144665, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731144733, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_E5C0679566569A75.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731144841, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731144924, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B964A68D830AC6BF.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731145071, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731145155, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_9A3FDE96F0266F50.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731145294, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731145395, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_532B9929C68EE5D8.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731145507, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731145583, "dur":132, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_07C436A676AA1A59.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731145715, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731145779, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_A9F3FB8E6C0780FC.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731145951, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731146065, "dur":182, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_8189EC8801BEEB18.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731146248, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731146356, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_3EA0D0A7C53907A2.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731146526, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731146630, "dur":141, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_43A2C3499BF00BED.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731146771, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731146837, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_CDA841E1F3BEFCEA.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731146930, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731146999, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_6E45C98E7E4DEAAC.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731147107, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731147190, "dur":155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_A5AE20A98D66BE2A.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731147345, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731147429, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_3B66C730F6ED4874.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731147580, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731147645, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_5762B19BA4A2D3B6.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731147795, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731147905, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731147997, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_6450FF118201D284.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731148164, "dur":212, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.UI.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":1, "ts":1758794731148376, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731148468, "dur":3675, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731152207, "dur":13649, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1758794731165856, "dur":218, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731166080, "dur":83, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_DB7439BD0E712D01.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731166163, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731166244, "dur":1645, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Burst.dll.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731167934, "dur":6277, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Burst.dll (+2 others)" }}
,{ "pid":12345, "tid":1, "ts":1758794731174211, "dur":292, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731174510, "dur":89, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_59592FE1C19AAD4D.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1758794731174599, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731174710, "dur":1643, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731176353, "dur":1636, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731177989, "dur":1578, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731179567, "dur":1444, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731181011, "dur":1375, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731182386, "dur":1435, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731183821, "dur":1508, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731185330, "dur":1568, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731186899, "dur":356, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731187255, "dur":376, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731187631, "dur":1348, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731188979, "dur":19496, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731208664, "dur":170, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1758794731208834, "dur":2363, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner" }}
,{ "pid":12345, "tid":1, "ts":1758794731211198, "dur":681, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger" }}
,{ "pid":12345, "tid":1, "ts":1758794731208476, "dur":3407, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731211883, "dur":78095, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731289987, "dur":4583, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Mathematics.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1758794731294570, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731294731, "dur":5898, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1758794731300629, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794731300775, "dur":8233, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/UnityEngine.UI.dll (+pdb)" }}
,{ "pid":12345, "tid":1, "ts":1758794731309033, "dur":482, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1758794731309515, "dur":13869, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731128927, "dur":14344, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731143275, "dur":271, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.rsp2" }}
,{ "pid":12345, "tid":2, "ts":1758794731143547, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731143628, "dur":167, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_6C13B72F801586DB.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731143795, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731143914, "dur":132, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_5AFB90E5B3938F6A.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731144046, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731144141, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_761201DEB2FFD5AE.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731144288, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731144404, "dur":159, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_95D79878B91D771D.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731144564, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731144651, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_A18767039B605D8A.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731144747, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731144835, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_7FB03B9F43028903.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731144938, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731145045, "dur":152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_56092ADF71655F79.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731145197, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731145290, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3260E1D96E1E22C0.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731145396, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731145465, "dur":97, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_1879345459B1F65E.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731145610, "dur":145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_CEEE8E02F8AB65CD.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731145755, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731145848, "dur":169, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E921E8B78225857D.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731146017, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731146139, "dur":162, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_5DE93EB871AE54DB.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731146302, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731146432, "dur":164, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_DF724CA3757AAEF4.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731146596, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731146702, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_F3361C9D618CC613.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731146826, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731146895, "dur":86, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_9F1A015DAD66E97D.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731146981, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731147060, "dur":118, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_AAFF8212FFC78F4B.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731147178, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731147279, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_397B254DA19E067F.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731147416, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731147512, "dur":111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_C9170BEF06746105.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731147623, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731147722, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_CE4E24FD428232DA.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731147822, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731147887, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731147984, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_6F2DFF6A42ECB2AA.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731148135, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731148276, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_4190A0BFBFAECF93.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731148384, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731148460, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731148566, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731148650, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731148736, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731148862, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731148956, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731149048, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Burst.CodeGen.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":2, "ts":1758794731149242, "dur":118, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_F5791B14F366A51E.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731149360, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731149423, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731149514, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731149575, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731149634, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp" }}
,{ "pid":12345, "tid":2, "ts":1758794731149708, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731149791, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.rsp" }}
,{ "pid":12345, "tid":2, "ts":1758794731149892, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731149968, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731150083, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731150186, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731150290, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731150447, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731150599, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731150695, "dur":1233, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731151928, "dur":1240, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731153168, "dur":1119, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731154287, "dur":1033, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731155321, "dur":943, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731156264, "dur":1091, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731157355, "dur":1401, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731158756, "dur":577, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731159334, "dur":255, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731159589, "dur":1371, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731160960, "dur":1223, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731162183, "dur":1335, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731163518, "dur":1460, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731164978, "dur":1746, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731166725, "dur":549, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731167320, "dur":20198, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1758794731187598, "dur":175, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731187813, "dur":1072, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1758794731188960, "dur":145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1758794731189133, "dur":831, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1758794731190042, "dur":99854, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731289900, "dur":4673, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1758794731294574, "dur":181, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731294761, "dur":6589, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)" }}
,{ "pid":12345, "tid":2, "ts":1758794731301351, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731301553, "dur":575, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794731302170, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731302367, "dur":454, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.AI.Navigation.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794731302960, "dur":402, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794731303501, "dur":570, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794731304228, "dur":494, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794731304767, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731304927, "dur":512, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":2, "ts":1758794731305440, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.pdb" }}
,{ "pid":12345, "tid":2, "ts":1758794731305502, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731305697, "dur":559, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.pdb" }}
,{ "pid":12345, "tid":2, "ts":1758794731306259, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731306420, "dur":507, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.pdb" }}
,{ "pid":12345, "tid":2, "ts":1758794731307095, "dur":430, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.pdb" }}
,{ "pid":12345, "tid":2, "ts":1758794731307527, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731307657, "dur":396, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.AI.Navigation.pdb" }}
,{ "pid":12345, "tid":2, "ts":1758794731308053, "dur":981, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794731309034, "dur":14341, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731128937, "dur":14343, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731143282, "dur":176, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":3, "ts":1758794731143458, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731143551, "dur":147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_F1E1BBCC6ABAE0DB.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731143698, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731143808, "dur":176, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_FF1638973A990706.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731143985, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731144064, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_8FC1791ACEE3E790.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731144222, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731144316, "dur":141, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_A49A776348DF5E4E.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731144457, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731144580, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_E1CEC1A55253DF7A.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731144762, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_20CB703B61251BE5.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731144897, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731144970, "dur":155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D8293B7A6760759A.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731145125, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731145220, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_730EC33EED2E94C6.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731145350, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731145412, "dur":125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_FFC4A5DF3CDD2051.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731145537, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731145597, "dur":124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_837685507179BFD9.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731145721, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731145790, "dur":167, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_EB7DEB5E390DBA80.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731145957, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731146032, "dur":179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":3, "ts":1758794731146211, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731146316, "dur":3178, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731149494, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731149566, "dur":14544, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1758794731164110, "dur":190, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731164312, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_80B890C2D7911DBF.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731164417, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731164500, "dur":2881, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/UnityEditor.TestRunner.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731167381, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731167460, "dur":11314, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/UnityEditor.TestRunner.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1758794731178774, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731178975, "dur":86, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_B49A4614ABCDBFD4.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731179061, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731179144, "dur":4865, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/UnityEditor.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731184009, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731184098, "dur":3496, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/UnityEditor.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1758794731187594, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731187702, "dur":220, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/Unity.Burst.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731187975, "dur":687, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Burst.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1758794731188738, "dur":194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/Unity.Burst.CodeGen.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1758794731188974, "dur":1014, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Burst.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1758794731189989, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731190063, "dur":99938, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731290006, "dur":6560, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1758794731296566, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731296676, "dur":7175, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":3, "ts":1758794731303872, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731304013, "dur":407, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":3, "ts":1758794731304495, "dur":475, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1758794731304991, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731305166, "dur":586, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb" }}
,{ "pid":12345, "tid":3, "ts":1758794731305788, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731305905, "dur":640, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb" }}
,{ "pid":12345, "tid":3, "ts":1758794731306577, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731306688, "dur":542, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.pdb" }}
,{ "pid":12345, "tid":3, "ts":1758794731307360, "dur":504, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.pdb" }}
,{ "pid":12345, "tid":3, "ts":1758794731307864, "dur":978, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731308844, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794731308935, "dur":551, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.pdb" }}
,{ "pid":12345, "tid":3, "ts":1758794731309486, "dur":13886, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731128944, "dur":14340, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731143286, "dur":194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.rsp2" }}
,{ "pid":12345, "tid":4, "ts":1758794731143480, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731143592, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_1349457C77F70AD4.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731143729, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731143845, "dur":159, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_85BC3F815A006E37.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731144004, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731144067, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D7A9AF8C5E67A0AE.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731144206, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731144337, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_99C535C3D6E374C3.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731144475, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731144599, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_F2316A31D98809E8.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731144717, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731144779, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_BF21A61A4CC82206.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731144918, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731144991, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_2AA1A0FABCD57599.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731145141, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731145229, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_9663AEF5A1DB96B8.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731145357, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731145437, "dur":125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_71D986BE924F9760.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731145562, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731145622, "dur":110, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_CABAF2279C00D800.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731145732, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731145817, "dur":165, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_6DFD6E15EC2CCADA.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731145982, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731146102, "dur":169, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_88057B9DC68CCBAB.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731146271, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731146375, "dur":195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_4EE083DBDE9DCEDF.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731146570, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731146648, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_39A2FB794417C6BF.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731146865, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_B684BE6BA7888589.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731146960, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731147033, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_1844A957E02F8FD0.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731147145, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731147233, "dur":161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_CDA2BEF1C1523480.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731147395, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731147463, "dur":118, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_2B487051AB4C3A03.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731147581, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731147667, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_1D711A2907908DC5.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731147802, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731147922, "dur":114, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_6BB7F73D7038074E.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731148036, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731148109, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_28DAF9479429A673.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731148265, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_40479579ECF7C4CE.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731148409, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731148507, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731148587, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731148682, "dur":109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Burst.rsp" }}
,{ "pid":12345, "tid":4, "ts":1758794731148791, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731148883, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731148981, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731149125, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731149208, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_0CCB9384721BA6A1.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731149335, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731149396, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731149522, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731149588, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731149648, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp" }}
,{ "pid":12345, "tid":4, "ts":1758794731149750, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731149842, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731149945, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731150033, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731150135, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731150234, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731150350, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731150476, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731150576, "dur":1263, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731151839, "dur":1245, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731153084, "dur":1124, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731154209, "dur":1083, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731155293, "dur":927, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731156220, "dur":1098, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731157319, "dur":1347, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731158666, "dur":717, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731159383, "dur":259, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731159643, "dur":1438, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731161082, "dur":1220, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731162303, "dur":1311, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731163614, "dur":1476, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731165090, "dur":1723, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731166814, "dur":2307, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731169122, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731169195, "dur":7006, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1758794731176201, "dur":229, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731176435, "dur":116, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_F007F1EECED691E3.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1758794731176552, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731176636, "dur":1558, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731178194, "dur":1569, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731179763, "dur":1421, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731181184, "dur":1353, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731182538, "dur":1476, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731184014, "dur":1509, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731185523, "dur":1526, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731187241, "dur":382, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731187623, "dur":1370, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731188993, "dur":100950, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731289949, "dur":4906, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Assembly-CSharp.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1758794731294856, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731294965, "dur":6161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":4, "ts":1758794731301127, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731301307, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731301615, "dur":485, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":4, "ts":1758794731302205, "dur":571, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":4, "ts":1758794731302894, "dur":451, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":4, "ts":1758794731303386, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731303542, "dur":498, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll" }}
,{ "pid":12345, "tid":4, "ts":1758794731304074, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731304176, "dur":567, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":4, "ts":1758794731304752, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731304988, "dur":539, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758794731305559, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731305698, "dur":541, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758794731306248, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731306366, "dur":457, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Burst.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758794731306862, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731307018, "dur":482, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Collections.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758794731307535, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731307724, "dur":417, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758794731308141, "dur":1054, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731309197, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794731309293, "dur":314, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.pdb" }}
,{ "pid":12345, "tid":4, "ts":1758794731309614, "dur":13770, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731128954, "dur":14333, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731143289, "dur":83, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_320B820FE8FC5AE8.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731143413, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_3BE3DE4E4FF3C20F.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731143529, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731143605, "dur":176, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_2CF19CB4A1AF158C.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731143781, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731143864, "dur":161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_51F928846D19D7E2.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731144025, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731144097, "dur":182, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1DB14EE0893E74EF.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731144279, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731144382, "dur":132, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_05C8CFED34CB8739.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731144515, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731144619, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_D4462A08249AAAE8.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731144727, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731144784, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B4E6D214D3E2A1D4.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731144913, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731144983, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_11639AB4BFA07D5F.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731145134, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731145209, "dur":141, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_712FA009D6907B2B.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731145350, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731145416, "dur":124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_56E9249B00D2323D.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731145540, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731145651, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_2F7C47F2C923EECA.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731145757, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731145888, "dur":119, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_8B47C372A80AB1D5.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731146008, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731146088, "dur":173, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.TestRunner.rsp" }}
,{ "pid":12345, "tid":5, "ts":1758794731146262, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731146351, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_A191F8D8F2A28668.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731146529, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731146635, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_E82236F3A6561981.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731146779, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731146853, "dur":94, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_CD3029A75D6D7A40.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731146947, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731147025, "dur":119, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_04AD07D19869F8AC.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731147144, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731147227, "dur":147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_BE55FC8DDE197785.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731147374, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731147456, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_12D58D670DB4180F.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731147585, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731147651, "dur":143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_1EB82EF2C9AE7379.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731147795, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731147864, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_BDCC4BA5B011F2E9.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731147999, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731148072, "dur":132, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_57F26D0ACBE8DD19.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731148204, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731148282, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_49F2D3B3C857FB89.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731148387, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731148489, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731148578, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731148664, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731148808, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731148920, "dur":180, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp" }}
,{ "pid":12345, "tid":5, "ts":1758794731149126, "dur":79, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_F862E2FD6BA6C0C0.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731149206, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731149273, "dur":181, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Collections.CodeGen.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":5, "ts":1758794731149455, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731149560, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731149614, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731149686, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731149799, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731149902, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731149988, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731150068, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731150169, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731150248, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731150357, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731150499, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731150584, "dur":1258, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731151842, "dur":1210, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731153053, "dur":1166, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731154219, "dur":1094, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731155313, "dur":913, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731156226, "dur":1085, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731157311, "dur":1406, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731158718, "dur":670, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731159389, "dur":693, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731160083, "dur":1291, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731161374, "dur":1332, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731162706, "dur":1339, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731164046, "dur":1421, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731165467, "dur":1737, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731167205, "dur":264, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731167513, "dur":2333, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1758794731169846, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731170019, "dur":9735, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1758794731179754, "dur":219, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731179977, "dur":87, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_2E746244A5BDAEA0.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731180064, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731180136, "dur":1376, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731181513, "dur":1360, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731182873, "dur":1535, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731184408, "dur":1472, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731185880, "dur":1072, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731186952, "dur":136, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731187088, "dur":53, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731187141, "dur":64, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731187244, "dur":395, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731187639, "dur":1324, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731188975, "dur":243, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1758794731189253, "dur":608, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)" }}
,{ "pid":12345, "tid":5, "ts":1758794731189861, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731189964, "dur":100007, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731289975, "dur":4602, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.InputSystem.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1758794731294579, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731294799, "dur":6042, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1758794731300841, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794731300951, "dur":8195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)" }}
,{ "pid":12345, "tid":5, "ts":1758794731309175, "dur":402, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731309587, "dur":202, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731309797, "dur":247, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.AI.Navigation.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731310053, "dur":832, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Burst.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731310893, "dur":729, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731311630, "dur":1372, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731313009, "dur":283, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731313298, "dur":949, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731314256, "dur":148, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731314417, "dur":862, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731315286, "dur":140, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731315434, "dur":1172, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731316612, "dur":130, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731316751, "dur":95, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731316850, "dur":749, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731317661, "dur":520, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731318237, "dur":1094, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731319387, "dur":602, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731319995, "dur":593, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731320593, "dur":1087, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731321686, "dur":843, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731322533, "dur":236, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731322773, "dur":555, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":5, "ts":1758794731309578, "dur":13758, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":6, "ts":1758794731128962, "dur":14332, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731143297, "dur":71, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_3F0AF0E8FD5B91AB.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731143403, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_374A8F32DB19802B.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731143494, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731143570, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_AD0F59CECD9086B5.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731143708, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731143825, "dur":173, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_72E6AB956861F353.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731143998, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731144071, "dur":201, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CF5F61BCAC5D1F90.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731144272, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731144354, "dur":143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_C0AB20643B6FF208.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731144497, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731144614, "dur":118, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_ED8B032DF01A4A2C.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731144732, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731144788, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_6BADCFBF033360D0.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731144918, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731145002, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_910BD9D1CE0E2BD1.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731145144, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731145256, "dur":116, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_21D14335F6F8B5A4.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731145372, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731145450, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_7E945FCC8473B7C7.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731145573, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731145678, "dur":97, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_C69304A59A7A1B74.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731145775, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731145902, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_FD774B970C96B0D0.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731146055, "dur":159, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/UnityEngine.UI.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":6, "ts":1758794731146214, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731146336, "dur":171, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_7E3D0D223DF58476.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731146507, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731146618, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_FCE5A639896B35E4.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731146769, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731146843, "dur":89, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_FBD22BAAF9C7FF17.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731146932, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731146993, "dur":114, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_FDC92B42D20F9DB0.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731147107, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731147208, "dur":160, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_A023D350F37B3A85.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731147368, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731147430, "dur":134, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_E18F0E0F745C010F.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731147564, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731147636, "dur":145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_D49580F88C8A9124.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731147781, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731147849, "dur":147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_010CE801CBBC8038.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731147996, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731148087, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_F6EE2176487706CE.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731148204, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731148298, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731148448, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731148541, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731148628, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Burst.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":6, "ts":1758794731148785, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731148898, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731148988, "dur":189, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":6, "ts":1758794731149177, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731149251, "dur":111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_59627543C51E81A1.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731149363, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731149446, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731149507, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731149576, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.rsp" }}
,{ "pid":12345, "tid":6, "ts":1758794731149680, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731149814, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731149911, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731150011, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731150104, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731150210, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731150329, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731150507, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731150595, "dur":1254, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731151849, "dur":1223, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731153073, "dur":1139, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731154212, "dur":1075, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731155287, "dur":929, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731156216, "dur":1092, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731157308, "dur":918, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731158226, "dur":1464, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731159690, "dur":1442, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731161132, "dur":1224, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731162356, "dur":1325, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731163682, "dur":1465, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731165147, "dur":1716, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731166864, "dur":800, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731167709, "dur":3686, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1758794731171395, "dur":307, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731171707, "dur":77, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_75E0D491C6D663C5.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731171785, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731171868, "dur":1454, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731173322, "dur":1651, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731174973, "dur":1669, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731176643, "dur":1554, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731178197, "dur":1577, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731179774, "dur":1413, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731181188, "dur":1375, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731182563, "dur":1469, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731184032, "dur":1532, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731185564, "dur":1447, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731187012, "dur":114, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731187127, "dur":59, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731187238, "dur":377, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731187615, "dur":91, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731187706, "dur":214, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731187951, "dur":830, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1758794731188841, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/Unity.Collections.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731189058, "dur":1058, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Collections.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1758794731190161, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/Android_CodeGen/Unity.Collections.CodeGen.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1758794731190292, "dur":432, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/Android_CodeGen/Unity.Collections.CodeGen.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1758794731190760, "dur":767, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput" }}
,{ "pid":12345, "tid":6, "ts":1758794731191549, "dur":154, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731288446, "dur":296, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731192179, "dur":96578, "ph":"X", "name": "ILPP-Configuration",  "args": { "detail":"Library/ilpp-configuration.nevergeneratedoutput" }}
,{ "pid":12345, "tid":6, "ts":1758794731289889, "dur":4540, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.AI.Navigation.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1758794731294430, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731294537, "dur":5912, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1758794731300450, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794731300549, "dur":8185, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":6, "ts":1758794731308864, "dur":563, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1758794731309427, "dur":13944, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731128971, "dur":14328, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731143302, "dur":82, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_ACDD54761A8A8AD1.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731143415, "dur":125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_2338B6331E4A589D.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731143540, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731143635, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_788EC0AD0DD1252A.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731143798, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731143921, "dur":125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_BB076BE8E91D3844.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731144046, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731144144, "dur":161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_67D1F298EAAF8408.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731144305, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731144412, "dur":152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B861AB185A7695D4.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731144565, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731144662, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BB987306F50BED91.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731144747, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731144818, "dur":119, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_14EB62D6C267D167.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731144938, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731145034, "dur":153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5BA053E42555A2B5.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731145187, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731145245, "dur":111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_BBB5D930EB61518F.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731145357, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731145432, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_641E8BB24D9C991B.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731145601, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_D53BC0BA2A1258D1.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731145731, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731145815, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_463AABC76FBC4B88.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731145966, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731146029, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_968EEE9C3279D1EC.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731146207, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731146312, "dur":179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_CFEDB20F561084C9.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731146491, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731146600, "dur":167, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_2296864AE3FD3588.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731146767, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731146845, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_25888E9AFFFF520E.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731146936, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731147022, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_E60F1C45244EBAC4.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731147129, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731147219, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_089E00570BFA24CD.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731147368, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731147446, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_F7D7EA5E200864EA.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731147581, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731147655, "dur":139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_9AAFF76B45D04135.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731147794, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731147867, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_7B7BFE77A0C7AD62.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731148011, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731148135, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731148240, "dur":1362, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731149603, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731149675, "dur":16103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1758794731165778, "dur":218, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731166006, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_AFC2E85BC2EDBC0B.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731166086, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731166187, "dur":1136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731167323, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731167382, "dur":8075, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1758794731175457, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731175669, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_92C0142275484313.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731175769, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731175874, "dur":2702, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Collections.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731178576, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731178647, "dur":8618, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Collections.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1758794731187343, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731187414, "dur":631, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1758794731188045, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731188177, "dur":800, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731188977, "dur":171, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731189187, "dur":947, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1758794731190173, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731190248, "dur":566, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1758794731190815, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731190878, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1758794731190942, "dur":441, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":7, "ts":1758794731191453, "dur":98510, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731289967, "dur":5294, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Collections.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1758794731295261, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731295376, "dur":5118, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Burst.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1758794731300494, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794731300631, "dur":7962, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":7, "ts":1758794731308659, "dur":374, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":7, "ts":1758794731309043, "dur":462, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":7, "ts":1758794731309505, "dur":13873, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731128978, "dur":14327, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731143309, "dur":77, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_2C20B8F0E17E12AF.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731143436, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_139664EF1148BEA3.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731143562, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731143671, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_646E6BF09BC34AF8.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731143844, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731143968, "dur":111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_05CCC7FD7CF0316D.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731144079, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731144184, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_29F333B2BA915C4E.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731144341, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731144444, "dur":153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_5341A2509D5ED266.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731144598, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731144672, "dur":97, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_11EA7240A47B953E.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731144769, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731144855, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_AE0AA47335F7F2B3.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731144968, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731145064, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_0E20F46DE1E1A65B.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731145234, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_0B92978C8F2A41F2.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731145362, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731145429, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3CEA7E364D77D73C.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731145584, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_FD04B64CA8B1F690.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731145730, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731145804, "dur":176, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_734ECF7F5E8D9BFB.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731145980, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731146118, "dur":162, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_F3B0C5707ACFC0A0.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731146280, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731146414, "dur":156, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_C9D4E8C710DC3487.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731146570, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731146669, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_5C992D1A061FC68C.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731146813, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731146884, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_68666B651D4ED621.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731146980, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731147046, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_65B4114BBD2E3965.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731147150, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731147264, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_253FA34D0F96CF98.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731147395, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731147468, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConsentModule.dll_FCEE077DB856D053.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731147595, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731147683, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_6900CF627CC382EE.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731147821, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731147876, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_8008604A4480A7B6.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731148013, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731148116, "dur":86, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_673AEEFA91C338F1.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731148202, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731148284, "dur":125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_37F04A890A90A5DB.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731148410, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731148508, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731148603, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":8, "ts":1758794731148766, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731148842, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731148946, "dur":432, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Collections.rsp" }}
,{ "pid":12345, "tid":8, "ts":1758794731149379, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731149457, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731149540, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731149603, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731149680, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731149773, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731149871, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731149956, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731150044, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731150151, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731150246, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731150376, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731150519, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731150630, "dur":1215, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731151845, "dur":1224, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731153069, "dur":1131, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731154200, "dur":1074, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731155275, "dur":933, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731156208, "dur":1076, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731157285, "dur":1353, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731158639, "dur":1423, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731160062, "dur":1291, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731161353, "dur":1334, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731162687, "dur":1321, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731164008, "dur":1414, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731165422, "dur":1728, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731167151, "dur":424, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731167629, "dur":2372, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1758794731170002, "dur":252, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731170260, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_08E11C037DD39E3A.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731170333, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731170488, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731170561, "dur":1663, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731172224, "dur":1499, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731173723, "dur":1632, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731175355, "dur":1725, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731177080, "dur":1494, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731178574, "dur":1624, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731180198, "dur":1390, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731181588, "dur":1337, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731182926, "dur":1553, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731184479, "dur":1581, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731186060, "dur":339, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731186400, "dur":198, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731186598, "dur":388, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731186987, "dur":101, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731187088, "dur":52, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731187140, "dur":65, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731187245, "dur":346, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731187593, "dur":180, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731187774, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731187832, "dur":1625, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1758794731189458, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731189586, "dur":87, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731189700, "dur":1236, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1758794731191005, "dur":71, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731191084, "dur":453, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1758794731191561, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1758794731191653, "dur":195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1758794731191859, "dur":98067, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731289942, "dur":5290, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1758794731295233, "dur":193, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731295433, "dur":5196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1758794731300629, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794731300745, "dur":8036, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Timeline.dll (+pdb)" }}
,{ "pid":12345, "tid":8, "ts":1758794731308880, "dur":567, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":8, "ts":1758794731309448, "dur":13950, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731128985, "dur":14328, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731143315, "dur":70, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_3A024D3CFAEFB3CE.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731143407, "dur":87, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_C6011564DA201A52.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731143494, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731143578, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_A1002E9E9F083797.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731143714, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731143822, "dur":165, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_AE483740772154F6.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731143988, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731144060, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7EB4E75F718193E6.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731144209, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731144324, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4B36DE508E234CBA.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731144460, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731144586, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_0CB46753FA7C312F.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731144714, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731144770, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_611725854B2C7A29.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731144907, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731144988, "dur":152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_4EB5407DF9A5D812.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731145140, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731145217, "dur":139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_26633F92B9A939A2.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731145357, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731145420, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_2E74B195F350D70F.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731145542, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731145631, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_63177FAB2D86FAFC.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731145731, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731145799, "dur":160, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_D28966A0C9F1BC15.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731145960, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731146049, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731146172, "dur":168, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B98DEA3DACE5B6F2.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731146340, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731146471, "dur":164, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3F703C20146B7E70.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731146635, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731146755, "dur":96, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_A4E7BFD9C85E5D10.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731146851, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731146909, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_45C3A9F199AE3937.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731147008, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731147083, "dur":124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_E2BB2975B5B78B38.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731147207, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731147313, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_ECFD0B88A31ACF2A.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731147441, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731147521, "dur":111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_BB69CBE628A12E20.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731147633, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731147723, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7EECDB7F9D30A336.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731147830, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731147942, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_3CB6A4B5DF319A4A.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731148035, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731148095, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_2F018D732F244A87.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731148219, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731148329, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731148427, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731148528, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731148614, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_283D5FC3BD6A67A2.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731148699, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731148795, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731148914, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731149001, "dur":167, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.rsp2" }}
,{ "pid":12345, "tid":9, "ts":1758794731149168, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731149261, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731149361, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Burst.CodeGen.rsp" }}
,{ "pid":12345, "tid":9, "ts":1758794731149499, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731149570, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731149631, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731149782, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731149873, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731149954, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731150056, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731150166, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731150263, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731150400, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731150543, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731150672, "dur":1213, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731151885, "dur":1236, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731153121, "dur":1131, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731154252, "dur":1009, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731155261, "dur":936, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731156197, "dur":1098, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731157296, "dur":1350, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731158647, "dur":1169, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731159817, "dur":1433, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731161250, "dur":1261, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731162511, "dur":1332, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731163844, "dur":1428, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731165273, "dur":1804, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731167078, "dur":299, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731167418, "dur":325, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731167783, "dur":4052, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1758794731171836, "dur":203, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731172043, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_3FA56196D520B7A3.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731172117, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731172199, "dur":1527, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731173727, "dur":1614, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731175341, "dur":1683, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731177024, "dur":1502, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731178527, "dur":1599, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731180126, "dur":1393, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731181519, "dur":1348, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731182867, "dur":1565, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731184432, "dur":1483, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731185916, "dur":484, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731186401, "dur":198, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731186600, "dur":305, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731186905, "dur":173, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731187192, "dur":69, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731187261, "dur":369, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731187631, "dur":1326, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731188972, "dur":218, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1758794731189216, "dur":570, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)" }}
,{ "pid":12345, "tid":9, "ts":1758794731189786, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731189864, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731189923, "dur":100028, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731289957, "dur":6351, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.TextMeshPro.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1758794731296308, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731296408, "dur":8472, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)" }}
,{ "pid":12345, "tid":9, "ts":1758794731304881, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731305078, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731305153, "dur":525, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":9, "ts":1758794731305705, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731305872, "dur":588, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb" }}
,{ "pid":12345, "tid":9, "ts":1758794731306566, "dur":536, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.pdb" }}
,{ "pid":12345, "tid":9, "ts":1758794731307103, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":9, "ts":1758794731307250, "dur":600, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":9, "ts":1758794731307851, "dur":937, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794731308848, "dur":624, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb" }}
,{ "pid":12345, "tid":9, "ts":1758794731309473, "dur":13923, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731128991, "dur":14328, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731143319, "dur":66, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_C57112EE168C0CEE.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731143404, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_E409852C73E3C815.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731143531, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731143614, "dur":168, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_A3723152F274B049.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731143783, "dur":95, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731143884, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_C49C0FC47CAFEAC5.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731144026, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731144118, "dur":167, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_23AD0A7361E5AF91.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731144285, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731144386, "dur":160, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F56BB6A2C38EBCEC.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731144546, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731144650, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_5F70C3E0135C03E6.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731144746, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731144806, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_7D46FF134A659955.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731144936, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731145012, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_102C078AEE4D5C34.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731145147, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731145248, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3193DD45B90182D7.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731145353, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731145439, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_9A9A08C6BDB0373A.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731145563, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731145618, "dur":114, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_AD829E7041D38787.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731145732, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731145828, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_46B2EC7768D1E935.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731146005, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731146076, "dur":145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2A5CE0F0450FED37.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731146221, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731146325, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_68AFAE2ACBA2FE65.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731146495, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731146608, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_92EF508A30199DA1.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731146779, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731146847, "dur":86, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InsightsModule.dll_3F74E93A17E40DA9.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731146933, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731147011, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_3C8BA2396F714A7A.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731147146, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731147251, "dur":145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_66BA61F6E735559C.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731147396, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731147491, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_2019ACBA132EC117.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731147619, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731147701, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_8BC3E661A9C77141.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731147822, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731147879, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_C571055F00C3F184.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731148018, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731148110, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_D01CC5A6D0F5DAF9.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731148241, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731148292, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_927C166CA6DDB07E.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731148392, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731148503, "dur":166, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Burst.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":10, "ts":1758794731148669, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731148736, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731148832, "dur":100, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731148940, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731149086, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731149140, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_89A9C630F938B4E1.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731149214, "dur":107, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731149326, "dur":302, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/Android_CodeGen/Unity.Collections.CodeGen.rsp" }}
,{ "pid":12345, "tid":10, "ts":1758794731149628, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731149716, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731149785, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731149889, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731149961, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731150055, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731150177, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731150255, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731150378, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731150511, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731150610, "dur":1245, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731151855, "dur":1223, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731153078, "dur":1136, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731154214, "dur":932, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731155146, "dur":900, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731156046, "dur":1094, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731157141, "dur":1277, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731158419, "dur":1430, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731159849, "dur":1384, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731161233, "dur":1248, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731162482, "dur":1300, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731163783, "dur":1429, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731165212, "dur":1714, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731166926, "dur":1772, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.AI.Navigation.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731168698, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731168789, "dur":6771, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.AI.Navigation.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1758794731175561, "dur":240, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731175807, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_2CF636FCAD6E4B77.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731175910, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731176019, "dur":1634, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731177654, "dur":1599, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731179253, "dur":1455, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731180709, "dur":1407, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731182116, "dur":1339, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731183456, "dur":1513, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731184969, "dur":1521, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731186490, "dur":314, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731186805, "dur":264, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731187070, "dur":72, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731187142, "dur":65, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731187245, "dur":349, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731187627, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1758794731187793, "dur":645, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll (+2 others)" }}
,{ "pid":12345, "tid":10, "ts":1758794731188523, "dur":467, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731188990, "dur":22896, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731211886, "dur":78024, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731289956, "dur":4951, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1758794731294907, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731295070, "dur":5913, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ILPostProcess Library/Bee/artifacts/1300b0aP.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)" }}
,{ "pid":12345, "tid":10, "ts":1758794731300983, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731301238, "dur":549, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1758794731301837, "dur":599, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":10, "ts":1758794731302532, "dur":400, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":10, "ts":1758794731302971, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731303063, "dur":459, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1758794731303637, "dur":536, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":10, "ts":1758794731304175, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731304231, "dur":577, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Collections.dll" }}
,{ "pid":12345, "tid":10, "ts":1758794731304809, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.pdb" }}
,{ "pid":12345, "tid":10, "ts":1758794731304991, "dur":565, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.pdb" }}
,{ "pid":12345, "tid":10, "ts":1758794731305589, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731305747, "dur":662, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.pdb" }}
,{ "pid":12345, "tid":10, "ts":1758794731306538, "dur":506, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.pdb" }}
,{ "pid":12345, "tid":10, "ts":1758794731307069, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731307227, "dur":523, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":10, "ts":1758794731307750, "dur":873, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794731308702, "dur":409, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb" }}
,{ "pid":12345, "tid":10, "ts":1758794731309111, "dur":14269, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794731331515, "dur":569, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 37977, "tid": 594, "ts": 1758794731334000, "dur": 22, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 37977, "tid": 594, "ts": 1758794731334056, "dur": 436, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 37977, "tid": 594, "ts": 1758794731332741, "dur": 1764, "ph": "X", "name": "Write chrome-trace events", "args": {} },
