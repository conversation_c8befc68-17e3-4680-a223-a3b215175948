﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void LocationGetter_Start_mE25335F30E1C1BFB3DF49D813A2C3A2785866EF9 (void);
extern void LocationGetter_Update_m9F8A26B23F9AB17D760602A516F986A69F2894C7 (void);
extern void LocationGetter__ctor_m3AB58F3B9C529EF9F404594C5D16D5E8571FA75E (void);
extern void LocationGetter_U3CStartU3Eb__1_0_mAB612C514B5E3505E4AE168197E25D1E124059D9 (void);
extern void RegionResolver_Awake_m8C5703C0A1B5CB9247399D2179D67CF1E33EBB23 (void);
extern void RegionResolver_ResolveRegion_mD6244FACCA05ABFEA87B23DB909A418B57500852 (void);
extern void RegionResolver_GetSimRegion_m152519E0590ACD2C3CD14C9403689A032EA7CCAE (void);
extern void RegionResolver_GetLatLon_m2200FA3FFE73A976EFDCC0E5EC6352044E83BBD3 (void);
extern void RegionResolver_HandleLocationNotEnabled_m33660BE30FF7C5E832D04E947F5944306243F19B (void);
extern void RegionResolver_AttemptLocationAccess_mE17B8F202074D766BD8C7B2C31076E2B5B292F8A (void);
extern void RegionResolver_ShowLocationPermissionDialog_mC0FD37C19DB3E466CC46196BF057498CFEAA952B (void);
extern void RegionResolver_ShowAndroidLocationDialog_m939B8AE6FAD6F99C66B96AD35B2D8EDE8707FB40 (void);
extern void RegionResolver__ctor_m3A01159301A9D59C0A01F9B5D73A0F27E272972A (void);
extern void RegionResolver__cctor_m0195B40852CBC29D35700F87EACAA74F88D94D5F (void);
extern void U3CU3Ec__DisplayClass8_0__ctor_mEAFF62E394743257BF184A7B587C5F6FDDC631FD (void);
extern void U3CU3Ec__DisplayClass8_0_U3CHandleLocationNotEnabledU3Eb__0_m6A74B976240FA4826FBB5B96147CA75A1D744D6E (void);
extern void U3CAttemptLocationAccessU3Ed__9__ctor_m3A95EDD6818CAD093EF669F0855F0706F4F66FA7 (void);
extern void U3CAttemptLocationAccessU3Ed__9_System_IDisposable_Dispose_m8E42AE1837DE46A3186BC3491F3F43A190C88C1D (void);
extern void U3CAttemptLocationAccessU3Ed__9_MoveNext_mAAD338F614EA9CBF3C87162ABC34FB42116A76D7 (void);
extern void U3CAttemptLocationAccessU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDE64E7BE2CC6A679E32A7F5641013AAAB90CE04B (void);
extern void U3CAttemptLocationAccessU3Ed__9_System_Collections_IEnumerator_Reset_m0D279B15D469ABA8C43BD05A17998D9CB1B1D72F (void);
extern void U3CAttemptLocationAccessU3Ed__9_System_Collections_IEnumerator_get_Current_m43F215C47843614BD564F9533BBF93CDCAC4D62B (void);
extern void U3CGetLatLonU3Ed__7__ctor_mF4753B8AD51B6EFD44D0DA488CB79E200E899E59 (void);
extern void U3CGetLatLonU3Ed__7_System_IDisposable_Dispose_m1556FE10D2B2838DCF3E97263B77F89174F9E8D8 (void);
extern void U3CGetLatLonU3Ed__7_MoveNext_mFE31183D4F8F2E894EAAA608FF17205F0A7E36CE (void);
extern void U3CGetLatLonU3Ed__7_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDE0E5FFE5F7A309063840BCD538C85DB9D24E323 (void);
extern void U3CGetLatLonU3Ed__7_System_Collections_IEnumerator_Reset_m9587465BE955BE5AD23347EE3B5CAF7851750D99 (void);
extern void U3CGetLatLonU3Ed__7_System_Collections_IEnumerator_get_Current_m1DA23B6BFA1BA214E8CE482ECAE1ABEA63DA579A (void);
extern void U3CHandleLocationNotEnabledU3Ed__8__ctor_m959CD0D80794FAD9A2960A77E5B2C1D5B26054EC (void);
extern void U3CHandleLocationNotEnabledU3Ed__8_System_IDisposable_Dispose_m1BFF5C443D37C3AA67EF73EF341248896C456051 (void);
extern void U3CHandleLocationNotEnabledU3Ed__8_MoveNext_m5BE663F5EB01DCC876D3730967EC4FEDB6D7965A (void);
extern void U3CHandleLocationNotEnabledU3Ed__8_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m041B12C91D92B382EB6A8E28F593E3F8DD0BBC6F (void);
extern void U3CHandleLocationNotEnabledU3Ed__8_System_Collections_IEnumerator_Reset_m9D14AEEA3EC81E3521319E461C0DF860EE4A20FA (void);
extern void U3CHandleLocationNotEnabledU3Ed__8_System_Collections_IEnumerator_get_Current_mFF9EF16EE7B936460A7214D78DA909948B9205E7 (void);
extern void U3CShowAndroidLocationDialogU3Ed__11__ctor_m2F5767B1B33981BD44345EFEEC2B4B23C35DC6D9 (void);
extern void U3CShowAndroidLocationDialogU3Ed__11_System_IDisposable_Dispose_m2FB92D1B23F615D0969C6A2E80B12E217F0D3FA0 (void);
extern void U3CShowAndroidLocationDialogU3Ed__11_MoveNext_m6E6D6119BAC97D6D7036D734BEF6E372BE32C70F (void);
extern void U3CShowAndroidLocationDialogU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m87D854F7A4E0E1165C015B86727B42B53BEF3A54 (void);
extern void U3CShowAndroidLocationDialogU3Ed__11_System_Collections_IEnumerator_Reset_m2D1B9B1615A4E2332682736CF58D3BBBD105B7C2 (void);
extern void U3CShowAndroidLocationDialogU3Ed__11_System_Collections_IEnumerator_get_Current_m30552812ADDB71D7A00007EBBDBCFB6A4B7477EE (void);
extern void U3CShowLocationPermissionDialogU3Ed__10__ctor_mEBC2BFBEDCF0A3739EBFB9492E14B7487D0798A7 (void);
extern void U3CShowLocationPermissionDialogU3Ed__10_System_IDisposable_Dispose_mDEDA179EB72D08AA0D69540D1A80E2D9818F3CD5 (void);
extern void U3CShowLocationPermissionDialogU3Ed__10_MoveNext_mE5112F885E94ABEB6D8971C997F6AED83BE502D7 (void);
extern void U3CShowLocationPermissionDialogU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m933554E19FF712CF53769EF2239C0538D612633E (void);
extern void U3CShowLocationPermissionDialogU3Ed__10_System_Collections_IEnumerator_Reset_mD901F2DD4A345EFB29C4592BD97A7BFF11E25F34 (void);
extern void U3CShowLocationPermissionDialogU3Ed__10_System_Collections_IEnumerator_get_Current_mB1A7AFAD350D8E32B200A48BBA0B4CA0E5C8A381 (void);
extern void Readme__ctor_m69C325C4C171DCB0312B646A9034AA91EA8C39C6 (void);
extern void Section__ctor_m5F732533E4DFC0167D965E5F5DB332E46055399B (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mBEB95BEB954BB63E9710BBC7AD5E78C4CB0A0033 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE70FB23ACC1EA12ABC948AA22C2E78B2D0AA39B1 (void);
static Il2CppMethodPointer s_methodPointers[50] = 
{
	LocationGetter_Start_mE25335F30E1C1BFB3DF49D813A2C3A2785866EF9,
	LocationGetter_Update_m9F8A26B23F9AB17D760602A516F986A69F2894C7,
	LocationGetter__ctor_m3AB58F3B9C529EF9F404594C5D16D5E8571FA75E,
	LocationGetter_U3CStartU3Eb__1_0_mAB612C514B5E3505E4AE168197E25D1E124059D9,
	RegionResolver_Awake_m8C5703C0A1B5CB9247399D2179D67CF1E33EBB23,
	RegionResolver_ResolveRegion_mD6244FACCA05ABFEA87B23DB909A418B57500852,
	RegionResolver_GetSimRegion_m152519E0590ACD2C3CD14C9403689A032EA7CCAE,
	RegionResolver_GetLatLon_m2200FA3FFE73A976EFDCC0E5EC6352044E83BBD3,
	RegionResolver_HandleLocationNotEnabled_m33660BE30FF7C5E832D04E947F5944306243F19B,
	RegionResolver_AttemptLocationAccess_mE17B8F202074D766BD8C7B2C31076E2B5B292F8A,
	RegionResolver_ShowLocationPermissionDialog_mC0FD37C19DB3E466CC46196BF057498CFEAA952B,
	RegionResolver_ShowAndroidLocationDialog_m939B8AE6FAD6F99C66B96AD35B2D8EDE8707FB40,
	RegionResolver__ctor_m3A01159301A9D59C0A01F9B5D73A0F27E272972A,
	RegionResolver__cctor_m0195B40852CBC29D35700F87EACAA74F88D94D5F,
	U3CU3Ec__DisplayClass8_0__ctor_mEAFF62E394743257BF184A7B587C5F6FDDC631FD,
	U3CU3Ec__DisplayClass8_0_U3CHandleLocationNotEnabledU3Eb__0_m6A74B976240FA4826FBB5B96147CA75A1D744D6E,
	U3CAttemptLocationAccessU3Ed__9__ctor_m3A95EDD6818CAD093EF669F0855F0706F4F66FA7,
	U3CAttemptLocationAccessU3Ed__9_System_IDisposable_Dispose_m8E42AE1837DE46A3186BC3491F3F43A190C88C1D,
	U3CAttemptLocationAccessU3Ed__9_MoveNext_mAAD338F614EA9CBF3C87162ABC34FB42116A76D7,
	U3CAttemptLocationAccessU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDE64E7BE2CC6A679E32A7F5641013AAAB90CE04B,
	U3CAttemptLocationAccessU3Ed__9_System_Collections_IEnumerator_Reset_m0D279B15D469ABA8C43BD05A17998D9CB1B1D72F,
	U3CAttemptLocationAccessU3Ed__9_System_Collections_IEnumerator_get_Current_m43F215C47843614BD564F9533BBF93CDCAC4D62B,
	U3CGetLatLonU3Ed__7__ctor_mF4753B8AD51B6EFD44D0DA488CB79E200E899E59,
	U3CGetLatLonU3Ed__7_System_IDisposable_Dispose_m1556FE10D2B2838DCF3E97263B77F89174F9E8D8,
	U3CGetLatLonU3Ed__7_MoveNext_mFE31183D4F8F2E894EAAA608FF17205F0A7E36CE,
	U3CGetLatLonU3Ed__7_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDE0E5FFE5F7A309063840BCD538C85DB9D24E323,
	U3CGetLatLonU3Ed__7_System_Collections_IEnumerator_Reset_m9587465BE955BE5AD23347EE3B5CAF7851750D99,
	U3CGetLatLonU3Ed__7_System_Collections_IEnumerator_get_Current_m1DA23B6BFA1BA214E8CE482ECAE1ABEA63DA579A,
	U3CHandleLocationNotEnabledU3Ed__8__ctor_m959CD0D80794FAD9A2960A77E5B2C1D5B26054EC,
	U3CHandleLocationNotEnabledU3Ed__8_System_IDisposable_Dispose_m1BFF5C443D37C3AA67EF73EF341248896C456051,
	U3CHandleLocationNotEnabledU3Ed__8_MoveNext_m5BE663F5EB01DCC876D3730967EC4FEDB6D7965A,
	U3CHandleLocationNotEnabledU3Ed__8_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m041B12C91D92B382EB6A8E28F593E3F8DD0BBC6F,
	U3CHandleLocationNotEnabledU3Ed__8_System_Collections_IEnumerator_Reset_m9D14AEEA3EC81E3521319E461C0DF860EE4A20FA,
	U3CHandleLocationNotEnabledU3Ed__8_System_Collections_IEnumerator_get_Current_mFF9EF16EE7B936460A7214D78DA909948B9205E7,
	U3CShowAndroidLocationDialogU3Ed__11__ctor_m2F5767B1B33981BD44345EFEEC2B4B23C35DC6D9,
	U3CShowAndroidLocationDialogU3Ed__11_System_IDisposable_Dispose_m2FB92D1B23F615D0969C6A2E80B12E217F0D3FA0,
	U3CShowAndroidLocationDialogU3Ed__11_MoveNext_m6E6D6119BAC97D6D7036D734BEF6E372BE32C70F,
	U3CShowAndroidLocationDialogU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m87D854F7A4E0E1165C015B86727B42B53BEF3A54,
	U3CShowAndroidLocationDialogU3Ed__11_System_Collections_IEnumerator_Reset_m2D1B9B1615A4E2332682736CF58D3BBBD105B7C2,
	U3CShowAndroidLocationDialogU3Ed__11_System_Collections_IEnumerator_get_Current_m30552812ADDB71D7A00007EBBDBCFB6A4B7477EE,
	U3CShowLocationPermissionDialogU3Ed__10__ctor_mEBC2BFBEDCF0A3739EBFB9492E14B7487D0798A7,
	U3CShowLocationPermissionDialogU3Ed__10_System_IDisposable_Dispose_mDEDA179EB72D08AA0D69540D1A80E2D9818F3CD5,
	U3CShowLocationPermissionDialogU3Ed__10_MoveNext_mE5112F885E94ABEB6D8971C997F6AED83BE502D7,
	U3CShowLocationPermissionDialogU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m933554E19FF712CF53769EF2239C0538D612633E,
	U3CShowLocationPermissionDialogU3Ed__10_System_Collections_IEnumerator_Reset_mD901F2DD4A345EFB29C4592BD97A7BFF11E25F34,
	U3CShowLocationPermissionDialogU3Ed__10_System_Collections_IEnumerator_get_Current_mB1A7AFAD350D8E32B200A48BBA0B4CA0E5C8A381,
	Readme__ctor_m69C325C4C171DCB0312B646A9034AA91EA8C39C6,
	Section__ctor_m5F732533E4DFC0167D965E5F5DB332E46055399B,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mBEB95BEB954BB63E9710BBC7AD5E78C4CB0A0033,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE70FB23ACC1EA12ABC948AA22C2E78B2D0AA39B1,
};
static const int32_t s_InvokerIndices[50] = 
{
	13037,
	13037,
	13037,
	9884,
	13037,
	9884,
	22448,
	8445,
	8445,
	8445,
	8445,
	8445,
	13037,
	22532,
	13037,
	9703,
	9827,
	13037,
	12666,
	12849,
	13037,
	12849,
	9827,
	13037,
	12666,
	12849,
	13037,
	12849,
	9827,
	13037,
	12666,
	12849,
	13037,
	12849,
	9827,
	13037,
	12666,
	12849,
	13037,
	12849,
	9827,
	13037,
	12666,
	12849,
	13037,
	12849,
	13037,
	13037,
	22555,
	13037,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule;
const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule = 
{
	"Assembly-CSharp.dll",
	50,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
