﻿#include "pch-cpp.hpp"





template <typename R>
struct VirtualFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};
struct InterfaceActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename T1>
struct InterfaceActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename T1, typename T2>
struct InterfaceActionInvoker2
{
	typedef void (*Action)(void*, T1, T2, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1, T2 p2)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, p1, p2, invokeData.method);
	}
};
template <typename T1>
struct GenericInterfaceActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (const RuntimeMethod* method, RuntimeObject* obj, T1 p1)
	{
		VirtualInvokeData invokeData;
		il2cpp_codegen_get_generic_interface_invoke_data(method, obj, &invokeData);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};

struct BaseRenderFunc_2_tA367BC6873E6166CAEC57DF6061E145117B9C512;
struct BaseRenderFunc_2_t914B3BA6065F3AEE258976DD7F37AF37D8C5BCC8;
struct BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D;
struct Dictionary_2_tCB9019887EB0254D4745B0724BC12327C5B63792;
struct Dictionary_2_tB41FAC88F07BAB98D6D373F7C94FB0496D1BDA32;
struct Dictionary_2_t21E090827BAF9D0D011CB55C02CA666756BF1AE7;
struct Dictionary_2_t54101FB5AEA0292C95D30CFAB7909873BF26E0B9;
struct Dictionary_2_t8D403C45B564DB77AE8A32FD6D35E333ABE68793;
struct Func_3_t04BCD613633CE8BB98EC046F90C4C9B0AA84F0BF;
struct IEnumerator_1_t5926539DBBB2302C569D0A07AF3A95A874CEBE33;
struct List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73;
struct List_1_t6988D2B11937CB8462E839C2DE1714ACD7797AF4;
struct List_1_tB5216E2043E168F4E0A122E4594A52A4BA2394F2;
struct List_1_t1EFB69EBBF25AD12F3A9E56C62B12F01D63469CD;
struct List_1_t67B4F73A05A1220FF45824DBE29F391490B25A0C;
struct List_1_t2E485E650BF1E41358CE56A69323E183C5A89CB6;
struct List_1_t2121653FB628940E808D105AD2C17E0F20AFB3A6;
struct List_1_t9B68833848E4C4D7F623C05F6B77F0449396354A;
struct List_1_tF42FEB6C3B18B7E7C8F2DE1FEBA00D2491736317;
struct List_1_t201EF66D89B48BDF1B448F483575106DF0B49F6A;
struct Stack_1_t3197E0F5EA36E611B259A88751D31FC2396FE4B6;
struct List_1U5BU5D_t37294D7C303231F2FD83B3C398AED0937F4F3206;
struct GraphicsFormatU5BU5DU5BU5D_t1424BD937A890524D2A66FF39E61DEB0F10FE0A2;
struct Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E;
struct RTHandleU5BU5DU5BU5D_t6066EC3F94599A5A42DA9A893F102E9618CB612F;
struct RenderTargetIdentifierU5BU5DU5BU5D_tDB35F8D017FE3AD8BB35E08E323074D47C5A10BB;
struct AttachmentDescriptorU5BU5D_tC70107EBD955FE94BA31C7FDC146069EF9C547C1;
struct BooleanU5BU5D_tD317D27C31DB892BE79FAE3AEBC0B3FFB73DE9B4;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct GraphicsDeviceTypeU5BU5D_t4322565F239068C66BF47053B83BD6A9A9D16408;
struct GraphicsFormatU5BU5D_tF6A3D90C430FA3F548B77E5D58D25D71F154E6C5;
struct Hash128U5BU5D_tB104E7247B842648E447B7FCF4748077DC1F8C98;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D;
struct PlaneU5BU5D_t4EEF66BAA8B0140EFFF34F6183CE7F80546592BE;
struct RTHandleU5BU5D_tE4B403B060D159B839BF74E8B59F8DCD52CF97DF;
struct RenderBufferStoreActionU5BU5D_tFEA8F5DD460573EA9F35FBEC5727D1804C5DCBF5;
struct RenderTargetIdentifierU5BU5D_t179798C153B7CE381B41C57863F98CB24023C4CE;
struct RendererListU5BU5D_t80C28472AABC27AC3809B9300B1F81D3AB423DDC;
struct RendererListHandleU5BU5D_t9F182E8E1E5140DEC19C6EE1F096F0329CCB7E3B;
struct ScriptableRendererU5BU5D_t9B15C048BCE03A67E830F1C79989B6A3E43788E6;
struct ScriptableRendererDataU5BU5D_tC674C147618C92B68DB64ECFDC847C8A941C6169;
struct ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04;
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;
struct TextureHandleU5BU5D_t544FFA6F5A0E1E5F24EB3C5E5F8547CCE9498BD7;
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;
struct Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD;
struct ShadowResolutionRequestU5BU5D_tC33A2E5E2712B8688E21C80BB2350155D029B334;
struct ItemU5BU5D_t4AC8CDDFF9ED634B9ECE154F0C638C00B12B5CCB;
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07;
struct AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C;
struct BaseCommandBuffer_tD67BB9B3F740537BD3F3A96FA17D06E6C3BFDC06;
struct Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235;
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184;
struct CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7;
struct ComputeCommandBuffer_tA6FA5F68FE745317C94802C6A828FC21AC0ADF61;
struct ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C;
struct ContextItem_tFC5ECB349F0410923AE6BC2A9CAACA3589B51086;
struct CullingAllocationInfo_tB260F5CD0B290F74E145EB16E54B901CC68D9D5A;
struct CustomSampler_tDA472186F08B4016626F032F944036BADFDB5487;
struct DebugHandler_t3A09E2CFD1CA6F5C192968A6FF19EE4863F44DA4;
struct DeferredLights_t8A3C2AC9C4AF92CF3411F722D8A094EA07DA14AC;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct IAsyncResult_t7B9B5A0ECB35DCEC31B8A8122C37D687369253B5;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IRasterRenderGraphBuilder_t607F94718848D836CFEEF0DE553E4A79CABD9372;
struct InternalRenderGraphContext_t7197268EBA8C241EB895B070A5E0C71B111D7503;
struct InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB;
struct Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3;
struct MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3;
struct MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553;
struct MethodInfo_t;
struct NativePassCompiler_t4E5CE894AF5B8832B5828B22DAB0EA4FAC819E07;
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C;
struct ProbeVolumeSceneData_t29FD126D36ED1E093C2EBBFB3F248DD5E2A86D90;
struct ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE;
struct RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B;
struct RTHandleSystem_tAE496B31B56A77B4896E34576C961C3CA073998F;
struct RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8;
struct Recorder_t0A14385FB0F5829CAAC1E16F88B095769D648C90;
struct RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E;
struct RenderGraphBuilders_t899F9362CCFA453C5AF608B820107C9C37860D2A;
struct RenderGraphCompilationCache_t25B996EBE41BF50CAE9A81E97B9BBE376AAB55D2;
struct RenderGraphDebugParams_t36422B33508548E3E56CBAD04521104C2B3E669D;
struct RenderGraphDefaultResources_tCE331152C84ED1A36CF186CA0092AE10E599E25D;
struct RenderGraphLogger_t63D96976880E0CD356860F2470D7DAF60B1AD40C;
struct RenderGraphObjectPool_t2F5488D55D836B54B6E85D2952105BA61AEE6437;
struct RenderGraphPass_tEFB5BD685D417024760D82991EEEA4F4D0454A93;
struct RenderGraphResourceRegistry_t87A07027B2175239DB5239913091B9D9FE7244D1;
struct RenderGraphSettings_tC3A05CA5C042545DB58E32841FF2FEA9E260A130;
struct RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13;
struct RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct ScaleFunc_t423F661DAD5C7A18F509C8F1F62C9D6AEA9A9791;
struct ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0;
struct ScriptableRenderer_tF15B95BB85F26BE4B4719901D909831B89DC8892;
struct ScriptableRendererData_t9005CE645D4881FA4431E52EDC7678203632CAA7;
struct Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692;
struct StpHistory_t9A3E110F0E97FE93E44838B51A330C2111F96081;
struct String_t;
struct TaaHistory_tA203D496A5F23B4717184375DEAA12944359B85D;
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700;
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1;
struct Type_t;
struct UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7;
struct UniversalCameraHistory_t15D275DAE9AD5B608CE533D0FCE0884F07BB1E80;
struct UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2;
struct UniversalRenderPipelineAsset_tE8A9AA6F030CC3B558CEA2EB54FFF4FC58CA6232;
struct UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6;
struct UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C;
struct UnsafeCommandBuffer_tDE6BB2FE234DC7453CA682AB275888E9E35F22F2;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct VolumeProfile_t9B5F2005F575A710F38A124EF81A6228CCACACE1;
struct XRPass_tFC4577E97B88E0EAAAB2EB387AB3A92E9EB9C6DF;
struct U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192;
struct PassData_tD366FA555BBB5E9EA116F4553DA083826230EB0B;
struct U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264;
struct PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A;
struct CompiledGraph_tC8C6B8E2DD962A7BBC9B48ED868E03BE278D0644;
struct OnExecutionRegisteredDelegate_tCCD234646691F298602408925867AE91256D2226;
struct OnGraphRegisteredDelegate_t7DEB75DB071EA0F49C47E2617D72D40EF3A8188C;
struct RenderingFeatures_t31044CBDCDC2F05194BFA2A2122FBD937D78A371;
struct TextureResources_t8FB6A098EBF3C08BF1BD5EA926743C0FA595202B;

IL2CPP_EXTERN_C RuntimeClass* BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* CommandBufferHelpers_tB8C09D01D6F0369DF52DEA955F017D6001BC72E5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GBufferPass_t540C12BCC3AFAC32B775694C8A29B69A49C284E7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IBaseRenderGraphBuilder_tFFF84F72F862F1BE246A789AB6A59F959B490F3D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IRasterRenderGraphBuilder_t607F94718848D836CFEEF0DE553E4A79CABD9372_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RendererListHandleU5BU5D_t9F182E8E1E5140DEC19C6EE1F096F0329CCB7E3B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RendererListU5BU5D_t80C28472AABC27AC3809B9300B1F81D3AB423DDC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RenderingUtils_t4E40200449A82FA3A172A563C490DF11FADA2BE1_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ShadowSplitData_tC276A96F461DD73CFF6D94DB557D42A1643640DF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* UniversalRenderPipeline_t54B4737DC500C08628C5BE283D8C583C14DED98F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* UniversalRenderer_t31019D4AD52F646128E0D1649E7B87E33BA36D8A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4____rtMSAA_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral01A849374C26F36DBF4641EFCBB31ABDCEFF10D2;
IL2CPP_EXTERN_C String_t* _stringLiteral078C5819101F5FFE2A982E701EF7729257290FF7;
IL2CPP_EXTERN_C String_t* _stringLiteral1428B80A06183DF581677E12334AA50BA983AEA7;
IL2CPP_EXTERN_C String_t* _stringLiteral22CD4666EAE2AB1BED30C112E46A3027A62CFC9B;
IL2CPP_EXTERN_C String_t* _stringLiteral2CC3067D916B46FDC1022B552374730D579C2A17;
IL2CPP_EXTERN_C String_t* _stringLiteral3BEE5BF39527F408B86D7745DAE7590BE1EEB556;
IL2CPP_EXTERN_C String_t* _stringLiteral49AE794C00022ECA141068DEA9531BF6E0D342B7;
IL2CPP_EXTERN_C String_t* _stringLiteral4B2BF663FAFE4EA038675F6C2D1083425BC2DD57;
IL2CPP_EXTERN_C String_t* _stringLiteral59DE37D5237EDCD7A817E9624C3FD01BE5F920C3;
IL2CPP_EXTERN_C String_t* _stringLiteral5F20103D3E71C714D1518DC7B1C00D058D9A1D37;
IL2CPP_EXTERN_C String_t* _stringLiteral5FCFE8BB42D2AA29FAADC098234D0BEF8F39BE29;
IL2CPP_EXTERN_C String_t* _stringLiteral73FF05278C18960F05EB20845B1ECE59D9D3F9FF;
IL2CPP_EXTERN_C String_t* _stringLiteral7796075F085B1A73F0304B0217A48603C4A6B819;
IL2CPP_EXTERN_C String_t* _stringLiteral788668D2A163F3ADB8F2CBE770488E43D1CA6E2D;
IL2CPP_EXTERN_C String_t* _stringLiteral7A4F8AA35B73603CF0C795996C54334A2D00248C;
IL2CPP_EXTERN_C String_t* _stringLiteral7E7DB1B4C2DED075605289B76FF28624395D3688;
IL2CPP_EXTERN_C String_t* _stringLiteralAB69FA1AB6BB831506EFCAD83900FEE751E85F6F;
IL2CPP_EXTERN_C String_t* _stringLiteralABC8C9038985FA39FE6CEE7EA87E485F8723F5AE;
IL2CPP_EXTERN_C String_t* _stringLiteralC56629E7BE3CBCE4ECF0CAF12E998C56278552E6;
IL2CPP_EXTERN_C String_t* _stringLiteralEF420ABFDDBDA7B9EE665D85EF62E4A437554003;
IL2CPP_EXTERN_C const RuntimeMethod* ContextContainer_Get_TisUniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7_mE62F7903614384541B770CC0B9A99BD2E608A0F8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContextContainer_Get_TisUniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2_m7D529C251F384985900DEA334BC399BAD36BFC8F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContextContainer_Get_TisUniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6_m71849CEFB9C023EDE026A0F38CE6044274505C06_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ContextContainer_Get_TisUniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C_m071A668846CA61588A4D53CCA3CFCD3730530E4E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* IRasterRenderGraphBuilder_SetRenderFunc_TisPassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A_mF80B40083B6B7C1FEDE12E6F5156C27582A83E8B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* NativeArrayExtensions_UnsafeElementAt_TisShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20_m172973527DE9C444ABA117A4CE8B1D951C896487_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* NativeArrayExtensions_UnsafeElementAt_TisURPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9_m56159FAB7E5AD01E526C4937CB1FAFF4F8F52A7D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ProfilingSampler_Get_TisURPProfileId_tA83520239B6C0F10A73CCC6CEC7D3DA1F1932481_m9F3104BDEBD70A287E8F4BAC86579564A19A5661_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RenderGraph_AddRasterRenderPass_TisPassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A_mC53A7D9CBCFE77D6877B8B7B2819DACBFD86AE9B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec_U3CRenderU3Eb__39_0_m33741C44C25353E83945D40A6BCA43D0EB888829_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_0_0_0_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D;
struct RendererListU5BU5D_t80C28472AABC27AC3809B9300B1F81D3AB423DDC;
struct RendererListHandleU5BU5D_t9F182E8E1E5140DEC19C6EE1F096F0329CCB7E3B;
struct ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04;
struct Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD;
struct ItemU5BU5D_t4AC8CDDFF9ED634B9ECE154F0C638C00B12B5CCB;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct TypeId_1_tF7C39317892E31289E8C529424E70ED463C8334C  : public RuntimeObject
{
};
struct U3CPrivateImplementationDetailsU3E_t16CE31F4DEE6BA0AEFEB3FA0105D58630695B339  : public RuntimeObject
{
};
struct BaseCommandBuffer_tD67BB9B3F740537BD3F3A96FA17D06E6C3BFDC06  : public RuntimeObject
{
	CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* ___m_WrappedCommandBuffer;
	RenderGraphPass_tEFB5BD685D417024760D82991EEEA4F4D0454A93* ___m_ExecutingPass;
};
struct ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C  : public RuntimeObject
{
	ItemU5BU5D_t4AC8CDDFF9ED634B9ECE154F0C638C00B12B5CCB* ___m_Items;
	List_1_t9B68833848E4C4D7F623C05F6B77F0449396354A* ___m_ActiveItemIndices;
};
struct ContextItem_tFC5ECB349F0410923AE6BC2A9CAACA3589B51086  : public RuntimeObject
{
};
struct MemberInfo_t  : public RuntimeObject
{
};
struct ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE  : public RuntimeObject
{
	CustomSampler_tDA472186F08B4016626F032F944036BADFDB5487* ___U3CsamplerU3Ek__BackingField;
	CustomSampler_tDA472186F08B4016626F032F944036BADFDB5487* ___U3CinlineSamplerU3Ek__BackingField;
	String_t* ___U3CnameU3Ek__BackingField;
	Recorder_t0A14385FB0F5829CAAC1E16F88B095769D648C90* ___m_Recorder;
	Recorder_t0A14385FB0F5829CAAC1E16F88B095769D648C90* ___m_InlineRecorder;
};
struct ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91  : public RuntimeObject
{
};
struct ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192  : public RuntimeObject
{
};
struct U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264  : public RuntimeObject
{
};
struct MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3  : public RuntimeObject
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct CameraData_tC27AE109CD20677486A4AC19C0CF014AE0F50C3E 
{
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___frameData;
};
struct CameraData_tC27AE109CD20677486A4AC19C0CF014AE0F50C3E_marshaled_pinvoke
{
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___frameData;
};
struct CameraData_tC27AE109CD20677486A4AC19C0CF014AE0F50C3E_marshaled_com
{
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___frameData;
};
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	float ___r;
	float ___g;
	float ___b;
	float ___a;
};
struct CommandBufferHelpers_tB8C09D01D6F0369DF52DEA955F017D6001BC72E5 
{
	union
	{
		struct
		{
		};
		uint8_t CommandBufferHelpers_tB8C09D01D6F0369DF52DEA955F017D6001BC72E5__padding[1];
	};
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D 
{
	uint32_t ___m_Index;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB 
{
	int32_t ___m_Mask;
};
struct LightData_t6A82F1C9AA97327A5EE9C16A3E949918F3A55470 
{
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___frameData;
};
struct LightData_t6A82F1C9AA97327A5EE9C16A3E949918F3A55470_marshaled_pinvoke
{
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___frameData;
};
struct LightData_t6A82F1C9AA97327A5EE9C16A3E949918F3A55470_marshaled_com
{
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___frameData;
};
struct Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 
{
	float ___m00;
	float ___m10;
	float ___m20;
	float ___m30;
	float ___m01;
	float ___m11;
	float ___m21;
	float ___m31;
	float ___m02;
	float ___m12;
	float ___m22;
	float ___m32;
	float ___m03;
	float ___m13;
	float ___m23;
	float ___m33;
};
struct PostProcessingData_tFA75BF22951C600258B2707AF7A113E4EDA49BD4 
{
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___frameData;
};
struct PostProcessingData_tFA75BF22951C600258B2707AF7A113E4EDA49BD4_marshaled_pinvoke
{
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___frameData;
};
struct PostProcessingData_tFA75BF22951C600258B2707AF7A113E4EDA49BD4_marshaled_com
{
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___frameData;
};
struct ProfilingScope_t57898BA31E8EF8F083EF84E0DA2737AC61CBC5BD 
{
	union
	{
		struct
		{
		};
		uint8_t ProfilingScope_t57898BA31E8EF8F083EF84E0DA2737AC61CBC5BD__padding[1];
	};
};
struct RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8  : public BaseCommandBuffer_tD67BB9B3F740537BD3F3A96FA17D06E6C3BFDC06
{
};
struct RasterGraphContext_tC4D3E53FF7131476487751ACB0237D56C8327147 
{
	InternalRenderGraphContext_t7197268EBA8C241EB895B070A5E0C71B111D7503* ___wrappedContext;
	RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___cmd;
};
struct RasterGraphContext_tC4D3E53FF7131476487751ACB0237D56C8327147_marshaled_pinvoke
{
	InternalRenderGraphContext_t7197268EBA8C241EB895B070A5E0C71B111D7503* ___wrappedContext;
	RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___cmd;
};
struct RasterGraphContext_tC4D3E53FF7131476487751ACB0237D56C8327147_marshaled_com
{
	InternalRenderGraphContext_t7197268EBA8C241EB895B070A5E0C71B111D7503* ___wrappedContext;
	RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___cmd;
};
struct Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D 
{
	float ___m_XMin;
	float ___m_YMin;
	float ___m_Width;
	float ___m_Height;
};
struct ShaderTagId_t453E2085B5EE9448FF75E550CAB111EFF690ECB0 
{
	int32_t ___m_Id;
};
struct ShadowData_tA165FDF7CA4CE6BEA8B649FFAB91C59ED684D832 
{
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___frameData;
};
struct ShadowData_tA165FDF7CA4CE6BEA8B649FFAB91C59ED684D832_marshaled_pinvoke
{
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___frameData;
};
struct ShadowData_tA165FDF7CA4CE6BEA8B649FFAB91C59ED684D832_marshaled_com
{
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___frameData;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B 
{
	uint32_t ___m_value;
};
struct UIntPtr_t 
{
	void* ____pointer;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 
{
	float ___x;
	float ___y;
};
struct Vector2Int_t69B2886EBAB732D9B880565E18E7568F3DE0CE6A 
{
	int32_t ___m_X;
	int32_t ___m_Y;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D12_t5F40C9EEDE242DFE47A8DCE218ED3DF3E88B4EC0 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D12_t5F40C9EEDE242DFE47A8DCE218ED3DF3E88B4EC0__padding[12];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D16_tB86B9BFC4ADBF4E2DF11F39AF43639693C65DF05 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D16_tB86B9BFC4ADBF4E2DF11F39AF43639693C65DF05__padding[16];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D19683_tBE7B83EE59191082CB97010C2CB0D4428C300DEF 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D19683_tBE7B83EE59191082CB97010C2CB0D4428C300DEF__padding[19683];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D24_tB605E983EFADFA4C2759D8C48AB45B0B3A7BCC51 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D24_tB605E983EFADFA4C2759D8C48AB45B0B3A7BCC51__padding[24];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D24908_t065ED3CA4290ED87B3640EFB82C5399994BB4775 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D24908_t065ED3CA4290ED87B3640EFB82C5399994BB4775__padding[24908];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D960_t86900CB1F8550ABFAD884FDD8E17F7B7AA90ED0D 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D960_t86900CB1F8550ABFAD884FDD8E17F7B7AA90ED0D__padding[960];
	};
};
#pragma pack(pop, tp)
struct Item_tFA847F868B1ECD768A489638A61F7BC398DECDAC 
{
	ContextItem_tFC5ECB349F0410923AE6BC2A9CAACA3589B51086* ___storage;
	bool ___isSet;
};
struct Item_tFA847F868B1ECD768A489638A61F7BC398DECDAC_marshaled_pinvoke
{
	ContextItem_tFC5ECB349F0410923AE6BC2A9CAACA3589B51086* ___storage;
	int32_t ___isSet;
};
struct Item_tFA847F868B1ECD768A489638A61F7BC398DECDAC_marshaled_com
{
	ContextItem_tFC5ECB349F0410923AE6BC2A9CAACA3589B51086* ___storage;
	int32_t ___isSet;
};
struct SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4 
{
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___rtMSAA;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___rtResolve;
	String_t* ___name;
	int32_t ___msaa;
};
struct SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_marshaled_pinvoke
{
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___rtMSAA;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___rtResolve;
	char* ___name;
	int32_t ___msaa;
};
struct SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_marshaled_com
{
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___rtMSAA;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___rtResolve;
	Il2CppChar* ___name;
	int32_t ___msaa;
};
struct U3Cm_CullingPlanesU3Ee__FixedBuffer_tC12F25D54F08F8DA4BD1129A6E4B09385A888B62 
{
	union
	{
		struct
		{
			uint8_t ___FixedElementField;
		};
		uint8_t U3Cm_CullingPlanesU3Ee__FixedBuffer_tC12F25D54F08F8DA4BD1129A6E4B09385A888B62__padding[160];
	};
};
struct AccessFlags_tB7D400C853C05A1DB9C6B56DF14E43721F0B1739 
{
	int32_t ___value__;
};
struct Allocator_t996642592271AAD9EE688F142741D512C07B5824 
{
	int32_t ___value__;
};
struct AntialiasingMode_tDF75AC7BDAF51FA550F528F7B798416ACB8D3487 
{
	int32_t ___value__;
};
struct AntialiasingQuality_t45B2A050F79EB8B705FED3F3F30A70942E71D605 
{
	int32_t ___value__;
};
struct BatchCullingProjectionType_tAD14BC373E72D5F74188E0899F8670FAB51FD481 
{
	int32_t ___value__;
};
struct Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Center;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Extents;
};
struct BuiltinRenderTextureType_t3D56813CAC7C6E4AC3B438039BD1CE7E62FE7C4E 
{
	int32_t ___value__;
};
struct CameraRenderType_tC686ABD18F67CA30E6DF217007744F509606A41D 
{
	int32_t ___value__;
};
struct CameraType_tCA1017DBE96964E1D967942FB98F152F14121FCD 
{
	int32_t ___value__;
};
struct ClearFlag_t0B57BE5A60AA0EE7CC0DAE7E7DF82EB993A59ADD 
{
	int32_t ___value__;
};
struct ColorGradingMode_t980B9396D20213763F23C4D474BC079FC68BF83E 
{
	int32_t ___value__;
};
struct CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7  : public RuntimeObject
{
	intptr_t ___m_Ptr;
};
struct CubemapFace_t300D6E2CD7DF60D44AA28338748B607677ED1D1B 
{
	int32_t ___value__;
};
struct CullingResults_tD6B7EF20B68D47DFF3A99EB2EA73F47F1D460267 
{
	intptr_t ___ptr;
	CullingAllocationInfo_tB260F5CD0B290F74E145EB16E54B901CC68D9D5A* ___m_AllocationInfo;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Downsampling_tFE6A5D41D0A9881972AE6C6470FA5E1700410D49 
{
	int32_t ___value__;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct FilterMode_t4AD57F1A3FE272D650E0E688BA044AE872BD2A34 
{
	int32_t ___value__;
};
struct GPUResidentDrawerMode_t25B237C91A9832B9A2C0DA9F7D85CF2BAE44BD2C 
{
	uint8_t ___value__;
};
struct GraphicsFormat_tC3D1898F3F3F1F57256C7F3FFD6BA9A37AE7E713 
{
	int32_t ___value__;
};
struct HDRColorBufferPrecision_tEF57E1CB7C2B230E3AFF5D6628010DD456EA58C7 
{
	int32_t ___value__;
};
struct ImageScalingMode_tB7E238BD8F6E6D9CBC6C2C92E3E4C9DF72A4AF54 
{
	int32_t ___value__;
};
struct ImageUpscalingFilter_t6BAB8A7CB0216E88F69458503780865981937631 
{
	int32_t ___value__;
};
struct Int32Enum_tCBAC8BA2BFF3A845FA599F303093BBBA374B6F0C 
{
	int32_t ___value__;
};
struct LODCrossFadeDitheringType_tB8C1B60F3BCA8E2BEFB7F09B3D93F96CE2CB1667 
{
	int32_t ___value__;
};
struct LightCookieFormat_tDED41022799DAEAA99C550708D80B99A2A8F9EC1 
{
	int32_t ___value__;
};
struct LightCookieResolution_t10D8305CBC46C8C4261C5EFAA031A2B35AF2BF39 
{
	int32_t ___value__;
};
struct LightProbeSystem_tEFBD1F503CF60DFBD07B38E3422CA44B5825921C 
{
	int32_t ___value__;
};
struct LightRenderingMode_t38A0DEB49D920E66D4854F5C54437D11BBA6D024 
{
	int32_t ___value__;
};
struct LightShadows_t5A3719FE33F8D536E5785AC42B4DF6E6F19666EA 
{
	int32_t ___value__;
};
struct LightShape_t538BE3D1AD8C9B537615DAE1C77FA43E26295E91 
{
	int32_t ___value__;
};
struct LightType_t2D4D43054E7473EECEB54493C0055AE074780234 
{
	int32_t ___value__;
};
struct LightmapBakeType_tD6FF28E59BAAD80648796C2835AB8DC0B0F8B232 
{
	int32_t ___value__;
};
struct MixedLightingMode_t6B7F0DC1BB531DDE85B2FF98C8BD122840060061 
{
	int32_t ___value__;
};
struct MsaaQuality_tE945475230F4F9265C9C862D32DE0484CB458FF9 
{
	int32_t ___value__;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct PerObjectData_t04DDCBE9ABF1113E8F9BAFCF4A7F94DD841B9CC9 
{
	int32_t ___value__;
};
struct PipelineDebugLevel_tA1231A8106C64D773860E40E46555EB8682D746B 
{
	int32_t ___value__;
};
struct ProbeVolumeBlendingTextureMemoryBudget_t7EE399384930322B57F0CF83315FE1FA651CB002 
{
	int32_t ___value__;
};
struct ProbeVolumeSHBands_t1515D16254FE4344C5FC4C9506F4F9A7ABA4D194 
{
	int32_t ___value__;
};
struct ProbeVolumeTextureMemoryBudget_t9F9B7221A5D6E98DAED0233C050A91A2D745CB66 
{
	int32_t ___value__;
};
struct RTHandleProperties_tBCB3E1EFE8B366995704C1322B9C443877580CD6 
{
	Vector2Int_t69B2886EBAB732D9B880565E18E7568F3DE0CE6A ___previousViewportSize;
	Vector2Int_t69B2886EBAB732D9B880565E18E7568F3DE0CE6A ___previousRenderTargetSize;
	Vector2Int_t69B2886EBAB732D9B880565E18E7568F3DE0CE6A ___currentViewportSize;
	Vector2Int_t69B2886EBAB732D9B880565E18E7568F3DE0CE6A ___currentRenderTargetSize;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___rtHandleScale;
};
struct RenderBufferLoadAction_t3333B2CABABAC39DA0CDC25602E5E4FD93C2CB0E 
{
	int32_t ___value__;
};
struct RenderBufferStoreAction_t87683F22C09634E24A574F21F42037C953A2C8B7 
{
	int32_t ___value__;
};
struct RenderGraphResourceType_t5F552AF06E38DEC5775B77F13C8783A895FCD086 
{
	int32_t ___value__;
};
struct RenderGraphState_tFB60E034516492478A523A4D4EB7E380C799C1E3 
{
	int32_t ___value__;
};
struct RenderPassEvent_t65FBDDF314AC831A598C794FD81BB61AD3930353 
{
	int32_t ___value__;
};
struct RenderTextureCreationFlags_t1C01993691E5BA956575134696509089FE852F50 
{
	int32_t ___value__;
};
struct RenderTextureFormat_tB6F1ED5040395B46880CE00312D2FDDBF9EEB40F 
{
	int32_t ___value__;
};
struct RenderTextureMemoryless_tE3B7F3AE353C3E9ACF86076376EB862131D19A69 
{
	int32_t ___value__;
};
struct RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 
{
	uintptr_t ___context;
	uint32_t ___index;
	uint32_t ___frame;
	uint32_t ___type;
	uint32_t ___contextID;
};
struct RendererListHandleType_tF4A920C02D6273E876EA0E5E7BF6B7096E278E57 
{
	int32_t ___value__;
};
struct RendererType_t952E4F0C867408594D6DB4894BEFF90C854B6C90 
{
	int32_t ___value__;
};
struct RenderingData_tAAA01190551D6D5954314E3E1E85B58DD45EED71 
{
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___frameData;
	CameraData_tC27AE109CD20677486A4AC19C0CF014AE0F50C3E ___cameraData;
	LightData_t6A82F1C9AA97327A5EE9C16A3E949918F3A55470 ___lightData;
	ShadowData_tA165FDF7CA4CE6BEA8B649FFAB91C59ED684D832 ___shadowData;
	PostProcessingData_tFA75BF22951C600258B2707AF7A113E4EDA49BD4 ___postProcessingData;
};
struct RenderingData_tAAA01190551D6D5954314E3E1E85B58DD45EED71_marshaled_pinvoke
{
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___frameData;
	CameraData_tC27AE109CD20677486A4AC19C0CF014AE0F50C3E_marshaled_pinvoke ___cameraData;
	LightData_t6A82F1C9AA97327A5EE9C16A3E949918F3A55470_marshaled_pinvoke ___lightData;
	ShadowData_tA165FDF7CA4CE6BEA8B649FFAB91C59ED684D832_marshaled_pinvoke ___shadowData;
	PostProcessingData_tFA75BF22951C600258B2707AF7A113E4EDA49BD4_marshaled_pinvoke ___postProcessingData;
};
struct RenderingData_tAAA01190551D6D5954314E3E1E85B58DD45EED71_marshaled_com
{
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___frameData;
	CameraData_tC27AE109CD20677486A4AC19C0CF014AE0F50C3E_marshaled_com ___cameraData;
	LightData_t6A82F1C9AA97327A5EE9C16A3E949918F3A55470_marshaled_com ___lightData;
	ShadowData_tA165FDF7CA4CE6BEA8B649FFAB91C59ED684D832_marshaled_com ___shadowData;
	PostProcessingData_tFA75BF22951C600258B2707AF7A113E4EDA49BD4_marshaled_com ___postProcessingData;
};
struct RenderingMode_t55C56C57973CFEF6AC1C91E2F4D7C8D76FF393A1 
{
	int32_t ___value__;
};
struct RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B 
{
	intptr_t ___value;
};
struct ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 
{
	intptr_t ___m_Ptr;
};
struct ScriptableRenderPassInput_t2E28A5DE1B3B8001EE14298E0133EFF3204DE645 
{
	int32_t ___value__;
};
struct ShEvalMode_t3815527819404E432D030BC50500EF1C5A0C0117 
{
	int32_t ___value__;
};
struct ShadowCascadesOption_t0A87F71CB8129325144A22F446F8BDF0297F0823 
{
	int32_t ___value__;
};
struct ShadowObjectsFilter_t33DCB0BA372F88094F5BDCDAD3ADD835453AE186 
{
	int32_t ___value__;
};
struct ShadowQuality_tA2A95092FE517E629C7015F01CFFD83704BFAD24 
{
	int32_t ___value__;
};
struct ShadowResolution_t6C40A535E6EC0EFEF19D5BD8B3470E3EAEE58C2F 
{
	int32_t ___value__;
};
struct ShadowSamplingMode_t8BE740C4258CFEDDBAC01FDC0438D8EE3F776BA8 
{
	int32_t ___value__;
};
struct ShadowSplitData_tC276A96F461DD73CFF6D94DB557D42A1643640DF 
{
	int32_t ___m_CullingPlaneCount;
	U3Cm_CullingPlanesU3Ee__FixedBuffer_tC12F25D54F08F8DA4BD1129A6E4B09385A888B62 ___m_CullingPlanes;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___m_CullingSphere;
	float ___m_ShadowCascadeBlendCullingFactor;
	float ___m_CullingNearPlane;
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___m_CullingMatrix;
};
struct SoftShadowQuality_t912D2519B85B76887C033210FFE985794D829D14 
{
	int32_t ___value__;
};
struct SortingCriteria_t4907D221CB6E6AA4A32C1ED7B5D17103FD3E7C39 
{
	int32_t ___value__;
};
struct StencilUsage_t1844800BFE5C84056335025A817D26B37D6BF9ED 
{
	int32_t ___value__;
};
struct StoreActionsOptimization_tB5EB82E81175365B1DF3C5DE71F35E77E3B38B4A 
{
	int32_t ___value__;
};
struct TemporalAAQuality_t03A8B3F777D54108A9CE21E79AB4C022968AD5F5 
{
	int32_t ___value__;
};
struct TextureDimension_t8D7148B9168256EE1E9AF91378ABA148888CE642 
{
	int32_t ___value__;
};
struct TextureWrapMode_tF9851343029052ED45668D1C99BAE09B2CCC13AD 
{
	int32_t ___value__;
};
struct URPProfileId_tA83520239B6C0F10A73CCC6CEC7D3DA1F1932481 
{
	int32_t ___value__;
};
struct UpscalingFilterSelection_t2C57376448148F3F22A29AAD71BBB7DB99D8F9A4 
{
	int32_t ___value__;
};
struct VRTextureUsage_t57FAA0077810142A461D74EDC5E33FC3D78BD2E8 
{
	int32_t ___value__;
};
struct VisibleLightFlags_t337DB92EFB0014AD6A250E1E45338B1194657CD8 
{
	int32_t ___value__;
};
struct VolumeFrameworkUpdateMode_tCD9A8BEF3700F3AA490F1BB39EF8A88E94398627 
{
	int32_t ___value__;
};
struct NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct NativeArray_1_t9B7A94FA050F43A3996B812B9164E7885F38ADC3 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct NativeArray_1_t4C11F337CF2A7773644650D071AA5F21F158A5E0 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct NativeArray_1_t71485A1E60B31CCAD3E525C907CF172E8B804468 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct NativeArray_1_t9086C71A5110879F9F76FDDC0009F7C7CF9D1B36 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct LightBakingOutput_t6212AB0B6B34C94F1982FE964FC48201854B5B90 
{
	int32_t ___probeOcclusionLightIndex;
	int32_t ___occlusionMaskChannel;
	int32_t ___lightmapBakeType;
	int32_t ___mixedLightingMode;
	bool ___isBaked;
};
struct LightBakingOutput_t6212AB0B6B34C94F1982FE964FC48201854B5B90_marshaled_pinvoke
{
	int32_t ___probeOcclusionLightIndex;
	int32_t ___occlusionMaskChannel;
	int32_t ___lightmapBakeType;
	int32_t ___mixedLightingMode;
	int32_t ___isBaked;
};
struct LightBakingOutput_t6212AB0B6B34C94F1982FE964FC48201854B5B90_marshaled_com
{
	int32_t ___probeOcclusionLightIndex;
	int32_t ___occlusionMaskChannel;
	int32_t ___lightmapBakeType;
	int32_t ___mixedLightingMode;
	int32_t ___isBaked;
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E  : public RuntimeObject
{
	NativePassCompiler_t4E5CE894AF5B8832B5828B22DAB0EA4FAC819E07* ___nativeCompiler;
	bool ___U3CnativeRenderPassesEnabledU3Ek__BackingField;
	RenderGraphResourceRegistry_t87A07027B2175239DB5239913091B9D9FE7244D1* ___m_Resources;
	RenderGraphObjectPool_t2F5488D55D836B54B6E85D2952105BA61AEE6437* ___m_RenderGraphPool;
	RenderGraphBuilders_t899F9362CCFA453C5AF608B820107C9C37860D2A* ___m_builderInstance;
	List_1_t1EFB69EBBF25AD12F3A9E56C62B12F01D63469CD* ___m_RenderPasses;
	List_1_t67B4F73A05A1220FF45824DBE29F391490B25A0C* ___m_RendererLists;
	RenderGraphDebugParams_t36422B33508548E3E56CBAD04521104C2B3E669D* ___m_DebugParameters;
	RenderGraphLogger_t63D96976880E0CD356860F2470D7DAF60B1AD40C* ___m_FrameInformationLogger;
	RenderGraphDefaultResources_tCE331152C84ED1A36CF186CA0092AE10E599E25D* ___m_DefaultResources;
	Dictionary_2_t21E090827BAF9D0D011CB55C02CA666756BF1AE7* ___m_DefaultProfilingSamplers;
	InternalRenderGraphContext_t7197268EBA8C241EB895B070A5E0C71B111D7503* ___m_RenderGraphContext;
	CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* ___m_PreviousCommandBuffer;
	List_1U5BU5D_t37294D7C303231F2FD83B3C398AED0937F4F3206* ___m_ImmediateModeResourceList;
	RenderGraphCompilationCache_t25B996EBE41BF50CAE9A81E97B9BBE376AAB55D2* ___m_CompilationCache;
	RenderTargetIdentifierU5BU5DU5BU5D_tDB35F8D017FE3AD8BB35E08E323074D47C5A10BB* ___m_TempMRTArrays;
	Stack_1_t3197E0F5EA36E611B259A88751D31FC2396FE4B6* ___m_CullingStack;
	String_t* ___m_CurrentExecutionName;
	int32_t ___m_ExecutionCount;
	int32_t ___m_CurrentFrameIndex;
	int32_t ___m_CurrentImmediatePassIndex;
	bool ___m_ExecutionExceptionWasRaised;
	bool ___m_RendererListCulling;
	bool ___m_EnableCompilationCaching;
	CompiledGraph_tC8C6B8E2DD962A7BBC9B48ED868E03BE278D0644* ___m_DefaultCompiledGraph;
	CompiledGraph_tC8C6B8E2DD962A7BBC9B48ED868E03BE278D0644* ___m_CurrentCompiledGraph;
	String_t* ___m_CaptureDebugDataForExecution;
	int32_t ___m_RenderGraphState;
	Dictionary_2_t8D403C45B564DB77AE8A32FD6D35E333ABE68793* ___m_DebugData;
	String_t* ___U3CnameU3Ek__BackingField;
	Dictionary_2_t54101FB5AEA0292C95D30CFAB7909873BF26E0B9* ___registeredGlobals;
	StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* ___k_PassNameDebugIgnoreList;
};
struct RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13  : public RuntimeObject
{
	SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4 ___m_A;
	SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4 ___m_B;
	int32_t ___m_FilterMode;
	bool ___m_AllowMSAA;
};
struct RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B 
{
	int32_t ___m_Type;
	int32_t ___m_NameID;
	int32_t ___m_InstanceID;
	intptr_t ___m_BufferPointer;
	int32_t ___m_MipLevel;
	int32_t ___m_CubeFace;
	int32_t ___m_DepthSlice;
};
struct RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46 
{
	int32_t ___U3CwidthU3Ek__BackingField;
	int32_t ___U3CheightU3Ek__BackingField;
	int32_t ___U3CmsaaSamplesU3Ek__BackingField;
	int32_t ___U3CvolumeDepthU3Ek__BackingField;
	int32_t ___U3CmipCountU3Ek__BackingField;
	int32_t ____graphicsFormat;
	int32_t ___U3CstencilFormatU3Ek__BackingField;
	int32_t ___U3CdepthStencilFormatU3Ek__BackingField;
	int32_t ___U3CdimensionU3Ek__BackingField;
	int32_t ___U3CshadowSamplingModeU3Ek__BackingField;
	int32_t ___U3CvrUsageU3Ek__BackingField;
	int32_t ____flags;
	int32_t ___U3CmemorylessU3Ek__BackingField;
};
struct RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA 
{
	int32_t ___type;
	bool ___m_IsValid;
	int32_t ___U3ChandleU3Ek__BackingField;
};
struct RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA_marshaled_pinvoke
{
	int32_t ___type;
	int32_t ___m_IsValid;
	int32_t ___U3ChandleU3Ek__BackingField;
};
struct RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA_marshaled_com
{
	int32_t ___type;
	int32_t ___m_IsValid;
	int32_t ___U3ChandleU3Ek__BackingField;
};
struct ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C 
{
	uint32_t ___m_Value;
	int32_t ___m_Version;
	int32_t ___m_Type;
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_pinvoke : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_com : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
};
struct ShadowDrawingSettings_t3C0AD7F3960F1BF5536867AF6E641F23F4C7AFA4 
{
	CullingResults_tD6B7EF20B68D47DFF3A99EB2EA73F47F1D460267 ___m_CullingResults;
	int32_t ___m_LightIndex;
	int32_t ___m_SplitIndex;
	int32_t ___m_UseRenderingLayerMaskTest;
	uint32_t ___m_BatchLayerMask;
	ShadowSplitData_tC276A96F461DD73CFF6D94DB557D42A1643640DF ___m_SplitData;
	int32_t ___m_ObjectsFilter;
	int32_t ___m_ProjectionType;
};
struct ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20 
{
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___viewMatrix;
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___projectionMatrix;
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___shadowTransform;
	int32_t ___offsetX;
	int32_t ___offsetY;
	int32_t ___resolution;
	ShadowSplitData_tC276A96F461DD73CFF6D94DB557D42A1643640DF ___splitData;
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct Type_t  : public MemberInfo_t
{
	RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ____impl;
};
struct UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6  : public ContextItem_tFC5ECB349F0410923AE6BC2A9CAACA3589B51086
{
	CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* ___m_CommandBuffer;
	CullingResults_tD6B7EF20B68D47DFF3A99EB2EA73F47F1D460267 ___cullResults;
	bool ___supportsDynamicBatching;
	int32_t ___perObjectData;
	int32_t ___U3CrenderingModeU3Ek__BackingField;
	LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB ___U3CprepassLayerMaskU3Ek__BackingField;
	LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB ___U3CopaqueLayerMaskU3Ek__BackingField;
	LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB ___U3CtransparentLayerMaskU3Ek__BackingField;
	bool ___U3CstencilLodCrossFadeEnabledU3Ek__BackingField;
};
struct VisibleLight_t0A4DF5B22865A00F618A0352B805277FA0132805 
{
	int32_t ___m_LightType;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_FinalColor;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___m_ScreenRect;
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___m_LocalToWorldMatrix;
	float ___m_Range;
	float ___m_SpotAngle;
	int32_t ___m_InstanceId;
	int32_t ___m_Flags;
};
struct Settings_t3BEFDFF2C1A3D3A215DAF7B76E735B1BFB946C92 
{
	int32_t ___m_Quality;
	float ___m_FrameInfluence;
	float ___m_JitterScale;
	float ___m_MipBias;
	float ___m_VarianceClampScale;
	float ___m_ContrastAdaptiveSharpening;
	int32_t ___resetHistoryFrames;
	int32_t ___jitterFrameCountOffset;
};
struct BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D  : public MulticastDelegate_t
{
};
struct AdditionalLightsShadowAtlasLayout_t2641AB50478ED46482F321134D86853AA802E50E 
{
	NativeArray_1_t9086C71A5110879F9F76FDDC0009F7C7CF9D1B36 ___m_SortedShadowResolutionRequests;
	NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___m_VisibleLightIndexToSortedShadowResolutionRequestsFirstSliceIndex;
	int32_t ___m_TotalShadowSlicesCount;
	int32_t ___m_TotalShadowResolutionRequestCount;
	bool ___m_TooManyShadowMaps;
	int32_t ___m_ShadowSlicesScaleFactor;
	int32_t ___m_AtlasSize;
};
struct AdditionalLightsShadowAtlasLayout_t2641AB50478ED46482F321134D86853AA802E50E_marshaled_pinvoke
{
	NativeArray_1_t9086C71A5110879F9F76FDDC0009F7C7CF9D1B36 ___m_SortedShadowResolutionRequests;
	NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___m_VisibleLightIndexToSortedShadowResolutionRequestsFirstSliceIndex;
	int32_t ___m_TotalShadowSlicesCount;
	int32_t ___m_TotalShadowResolutionRequestCount;
	int32_t ___m_TooManyShadowMaps;
	int32_t ___m_ShadowSlicesScaleFactor;
	int32_t ___m_AtlasSize;
};
struct AdditionalLightsShadowAtlasLayout_t2641AB50478ED46482F321134D86853AA802E50E_marshaled_com
{
	NativeArray_1_t9086C71A5110879F9F76FDDC0009F7C7CF9D1B36 ___m_SortedShadowResolutionRequests;
	NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___m_VisibleLightIndexToSortedShadowResolutionRequestsFirstSliceIndex;
	int32_t ___m_TotalShadowSlicesCount;
	int32_t ___m_TotalShadowResolutionRequestCount;
	int32_t ___m_TooManyShadowMaps;
	int32_t ___m_ShadowSlicesScaleFactor;
	int32_t ___m_AtlasSize;
};
struct AttachmentDescriptor_tBAC9B26B50BB0838C5C0CC22BB296F9DFF41276E 
{
	int32_t ___m_LoadAction;
	int32_t ___m_StoreAction;
	int32_t ___m_Format;
	RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B ___m_LoadStoreTarget;
	RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B ___m_ResolveTarget;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_ClearColor;
	float ___m_ClearDepth;
	uint32_t ___m_ClearStencil;
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B  : public RuntimeObject
{
	RTHandleSystem_tAE496B31B56A77B4896E34576C961C3CA073998F* ___m_Owner;
	RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* ___m_RT;
	Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* ___m_ExternalTexture;
	RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B ___m_NameID;
	bool ___m_EnableMSAA;
	bool ___m_EnableRandomWrite;
	bool ___m_EnableHWDynamicScale;
	bool ___m_RTHasOwnership;
	String_t* ___m_Name;
	bool ___m_UseCustomHandleScales;
	RTHandleProperties_tBCB3E1EFE8B366995704C1322B9C443877580CD6 ___m_CustomHandleProperties;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CscaleFactorU3Ek__BackingField;
	ScaleFunc_t423F661DAD5C7A18F509C8F1F62C9D6AEA9A9791* ___scaleFunc;
	bool ___U3CuseScalingU3Ek__BackingField;
	Vector2Int_t69B2886EBAB732D9B880565E18E7568F3DE0CE6A ___U3CreferenceSizeU3Ek__BackingField;
};
struct RenderPipelineAsset_t5F9BF815BF931E1314B184E7F9070FB649C7054E  : public ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A
{
};
struct ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0  : public RuntimeObject
{
	int32_t ___U3CrenderPassEventU3Ek__BackingField;
	RenderBufferStoreActionU5BU5D_tFEA8F5DD460573EA9F35FBEC5727D1804C5DCBF5* ___m_ColorStoreActions;
	int32_t ___m_DepthStoreAction;
	bool ___U3CrequiresIntermediateTextureU3Ek__BackingField;
	BooleanU5BU5D_tD317D27C31DB892BE79FAE3AEBC0B3FFB73DE9B4* ___m_OverriddenColorStoreActions;
	bool ___m_OverriddenDepthStoreAction;
	ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* ___m_ProfingSampler;
	String_t* ___m_PassName;
	RenderGraphSettings_tC3A05CA5C042545DB58E32841FF2FEA9E260A130* ___m_RenderGraphSettings;
	bool ___U3CoverrideCameraTargetU3Ek__BackingField;
	bool ___U3CisBlitRenderPassU3Ek__BackingField;
	bool ___U3CuseNativeRenderPassU3Ek__BackingField;
	int32_t ___U3CrenderPassQueueIndexU3Ek__BackingField;
	NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___m_ColorAttachmentIndices;
	NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___m_InputAttachmentIndices;
	GraphicsFormatU5BU5D_tF6A3D90C430FA3F548B77E5D58D25D71F154E6C5* ___U3CrenderTargetFormatU3Ek__BackingField;
	RTHandleU5BU5D_tE4B403B060D159B839BF74E8B59F8DCD52CF97DF* ___m_ColorAttachments;
	RTHandleU5BU5D_tE4B403B060D159B839BF74E8B59F8DCD52CF97DF* ___m_InputAttachments;
	BooleanU5BU5D_tD317D27C31DB892BE79FAE3AEBC0B3FFB73DE9B4* ___m_InputAttachmentIsTransient;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___m_DepthAttachment;
	int32_t ___m_Input;
	int32_t ___m_ClearFlag;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_ClearColor;
};
struct TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 
{
	ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C ___handle;
	bool ___builtin;
};
struct TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388_marshaled_pinvoke
{
	ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C ___handle;
	int32_t ___builtin;
};
struct TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388_marshaled_com
{
	ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C ___handle;
	int32_t ___builtin;
};
struct URPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9 
{
	NativeArray_1_t9B7A94FA050F43A3996B812B9164E7885F38ADC3 ___slices;
	uint32_t ___slicesValidMask;
};
struct UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7  : public ContextItem_tFC5ECB349F0410923AE6BC2A9CAACA3589B51086
{
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___m_ViewMatrix;
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___m_ProjectionMatrix;
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___m_JitterMatrix;
	bool ___m_CachedRenderIntoTextureXR;
	bool ___m_InitBuiltinXRConstants;
	Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___camera;
	UniversalCameraHistory_t15D275DAE9AD5B608CE533D0FCE0884F07BB1E80* ___m_HistoryManager;
	int32_t ___renderType;
	RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* ___targetTexture;
	RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46 ___cameraTargetDescriptor;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___pixelRect;
	bool ___useScreenCoordOverride;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___screenSizeOverride;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___screenCoordScaleBias;
	int32_t ___pixelWidth;
	int32_t ___pixelHeight;
	float ___aspectRatio;
	float ___renderScale;
	int32_t ___imageScalingMode;
	int32_t ___upscalingFilter;
	bool ___fsrOverrideSharpness;
	float ___fsrSharpness;
	int32_t ___hdrColorBufferPrecision;
	bool ___clearDepth;
	int32_t ___cameraType;
	bool ___isDefaultViewport;
	bool ___isHdrEnabled;
	bool ___allowHDROutput;
	bool ___isAlphaOutputEnabled;
	bool ___requiresDepthTexture;
	bool ___requiresOpaqueTexture;
	bool ___postProcessingRequiresDepthTexture;
	bool ___xrRendering;
	bool ___useGPUOcclusionCulling;
	bool ___stackLastCameraOutputToHDR;
	int32_t ___defaultOpaqueSortFlags;
	XRPass_tFC4577E97B88E0EAAAB2EB387AB3A92E9EB9C6DF* ___U3CxrU3Ek__BackingField;
	float ___maxShadowDistance;
	bool ___postProcessEnabled;
	bool ___stackAnyPostProcessingEnabled;
	RuntimeObject* ___captureActions;
	LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB ___volumeLayerMask;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___volumeTrigger;
	bool ___isStopNaNEnabled;
	bool ___isDitheringEnabled;
	int32_t ___antialiasing;
	int32_t ___antialiasingQuality;
	ScriptableRenderer_tF15B95BB85F26BE4B4719901D909831B89DC8892* ___renderer;
	bool ___resolveFinalTarget;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___worldSpaceCameraPos;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___backgroundColor;
	TaaHistory_tA203D496A5F23B4717184375DEAA12944359B85D* ___taaHistory;
	StpHistory_t9A3E110F0E97FE93E44838B51A330C2111F96081* ___stpHistory;
	Settings_t3BEFDFF2C1A3D3A215DAF7B76E735B1BFB946C92 ___taaSettings;
	Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___baseCamera;
	bool ___isLastBaseCamera;
};
struct UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2  : public ContextItem_tFC5ECB349F0410923AE6BC2A9CAACA3589B51086
{
	int32_t ___mainLightIndex;
	int32_t ___additionalLightsCount;
	int32_t ___maxPerObjectAdditionalLightsCount;
	NativeArray_1_t71485A1E60B31CCAD3E525C907CF172E8B804468 ___visibleLights;
	bool ___shadeAdditionalLightsPerVertex;
	bool ___supportsMixedLighting;
	bool ___reflectionProbeBoxProjection;
	bool ___reflectionProbeBlending;
	bool ___reflectionProbeAtlas;
	bool ___supportsLightLayers;
	bool ___supportsAdditionalLights;
};
struct RenderPipelineAsset_1_t2873A88178504E8793D044D4422CB05E32AEB121  : public RenderPipelineAsset_t5F9BF815BF931E1314B184E7F9070FB649C7054E
{
};
struct Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	int32_t ___U3CshapeU3Ek__BackingField;
	int32_t ___m_BakedIndex;
};
struct MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3  : public ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0
{
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___m_MainLightShadowmapTexture;
	int32_t ___renderTargetWidth;
	int32_t ___renderTargetHeight;
	int32_t ___m_ShadowCasterCascadesCount;
	bool ___m_CreateEmptyShadowmap;
	bool ___m_SetKeywordForEmptyShadowmap;
	bool ___m_EmptyShadowmapNeedsClear;
	float ___m_CascadeBorder;
	float ___m_MaxShadowDistanceSq;
	PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* ___m_PassData;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___m_EmptyMainLightShadowmapTexture;
	RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46 ___m_MainLightShadowDescriptor;
	Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* ___m_CascadeSplitDistances;
	Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D* ___m_MainLightShadowMatrices;
	ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* ___m_ProfilingSetupSampler;
	ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04* ___m_CascadeSlices;
};
struct RenderGraphDefaultResources_tCE331152C84ED1A36CF186CA0092AE10E599E25D  : public RuntimeObject
{
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___m_BlackTexture2D;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___m_WhiteTexture2D;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___m_ShadowTexture2D;
	TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 ___U3CblackTextureU3Ek__BackingField;
	TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 ___U3CwhiteTextureU3Ek__BackingField;
	TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 ___U3CclearTextureXRU3Ek__BackingField;
	TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 ___U3CmagentaTextureXRU3Ek__BackingField;
	TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 ___U3CblackTextureXRU3Ek__BackingField;
	TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 ___U3CblackTextureArrayXRU3Ek__BackingField;
	TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 ___U3CblackUIntTextureXRU3Ek__BackingField;
	TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 ___U3CblackTexture3DXRU3Ek__BackingField;
	TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 ___U3CwhiteTextureXRU3Ek__BackingField;
	TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 ___U3CdefaultShadowTextureU3Ek__BackingField;
};
struct ScriptableRenderer_tF15B95BB85F26BE4B4719901D909831B89DC8892  : public RuntimeObject
{
	int32_t ___m_LastBeginSubpassPassIndex;
	Dictionary_2_tCB9019887EB0254D4745B0724BC12327C5B63792* ___m_MergeableRenderPassesMap;
	Int32U5BU5DU5BU5D_t179D865D5B30EFCBC50F82C9774329C15943466E* ___m_MergeableRenderPassesMapArrays;
	Hash128U5BU5D_tB104E7247B842648E447B7FCF4748077DC1F8C98* ___m_PassIndexToPassHash;
	Dictionary_2_tB41FAC88F07BAB98D6D373F7C94FB0496D1BDA32* ___m_RenderPassesAttachmentCount;
	int32_t ___m_firstPassIndexOfLastMergeableGroup;
	AttachmentDescriptorU5BU5D_tC70107EBD955FE94BA31C7FDC146069EF9C547C1* ___m_ActiveColorAttachmentDescriptors;
	AttachmentDescriptor_tBAC9B26B50BB0838C5C0CC22BB296F9DFF41276E ___m_ActiveDepthAttachmentDescriptor;
	BooleanU5BU5D_tD317D27C31DB892BE79FAE3AEBC0B3FFB73DE9B4* ___m_IsActiveColorAttachmentTransient;
	RenderBufferStoreActionU5BU5D_tFEA8F5DD460573EA9F35FBEC5727D1804C5DCBF5* ___m_FinalColorStoreAction;
	int32_t ___m_FinalDepthStoreAction;
	ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* ___U3CprofilingExecuteU3Ek__BackingField;
	bool ___hasReleasedRTs;
	DebugHandler_t3A09E2CFD1CA6F5C192968A6FF19EE4863F44DA4* ___U3CDebugHandlerU3Ek__BackingField;
	RenderingFeatures_t31044CBDCDC2F05194BFA2A2122FBD937D78A371* ___U3CsupportedRenderingFeaturesU3Ek__BackingField;
	GraphicsDeviceTypeU5BU5D_t4322565F239068C66BF47053B83BD6A9A9D16408* ___U3CunsupportedGraphicsDeviceTypesU3Ek__BackingField;
	int32_t ___m_StoreActionsOptimizationSetting;
	List_1_t2E485E650BF1E41358CE56A69323E183C5A89CB6* ___m_ActiveRenderPassQueue;
	List_1_t2121653FB628940E808D105AD2C17E0F20AFB3A6* ___m_RendererFeatures;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___m_CameraColorTarget;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___m_CameraDepthTarget;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___m_CameraResolveTarget;
	bool ___m_FirstTimeCameraColorTargetIsBound;
	bool ___m_FirstTimeCameraDepthTargetIsBound;
	bool ___m_IsPipelineExecuting;
	bool ___disableNativeRenderPassInFeatures;
	bool ___useRenderPassEnabled;
	ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___m_frameData;
	bool ___U3CuseDepthPrimingU3Ek__BackingField;
	bool ___U3CstripShadowsOffVariantsU3Ek__BackingField;
	bool ___U3CstripAdditionalLightOffVariantsU3Ek__BackingField;
};
struct UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C  : public ContextItem_tFC5ECB349F0410923AE6BC2A9CAACA3589B51086
{
	bool ___supportsMainLightShadows;
	bool ___mainLightShadowsEnabled;
	int32_t ___mainLightShadowmapWidth;
	int32_t ___mainLightShadowmapHeight;
	int32_t ___mainLightShadowCascadesCount;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___mainLightShadowCascadesSplit;
	float ___mainLightShadowCascadeBorder;
	bool ___supportsAdditionalLightShadows;
	bool ___additionalLightShadowsEnabled;
	int32_t ___additionalLightsShadowmapWidth;
	int32_t ___additionalLightsShadowmapHeight;
	bool ___supportsSoftShadows;
	int32_t ___shadowmapDepthBufferBits;
	List_1_tF42FEB6C3B18B7E7C8F2DE1FEBA00D2491736317* ___bias;
	List_1_t05915E9237850A58106982B7FE4BC5DA4E872E73* ___resolution;
	bool ___isKeywordAdditionalLightShadowsEnabled;
	bool ___isKeywordSoftShadowsEnabled;
	int32_t ___mainLightShadowResolution;
	int32_t ___mainLightRenderTargetWidth;
	int32_t ___mainLightRenderTargetHeight;
	NativeArray_1_t4C11F337CF2A7773644650D071AA5F21F158A5E0 ___visibleLightsShadowCullingInfos;
	AdditionalLightsShadowAtlasLayout_t2641AB50478ED46482F321134D86853AA802E50E ___shadowAtlasLayout;
};
struct PassData_tD366FA555BBB5E9EA116F4553DA083826230EB0B  : public RuntimeObject
{
	TextureHandleU5BU5D_t544FFA6F5A0E1E5F24EB3C5E5F8547CCE9498BD7* ___gbuffer;
	TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 ___depth;
	DeferredLights_t8A3C2AC9C4AF92CF3411F722D8A094EA07DA14AC* ___deferredLights;
	RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA ___rendererListHdl;
	RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA ___objectsWithErrorRendererListHdl;
	RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 ___rendererList;
	RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 ___objectsWithErrorRendererList;
};
struct PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A  : public RuntimeObject
{
	bool ___emptyShadowmap;
	bool ___setKeywordForEmptyShadowmap;
	UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* ___renderingData;
	UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* ___cameraData;
	UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* ___lightData;
	UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* ___shadowData;
	MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* ___pass;
	TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 ___shadowmapTexture;
	RendererListU5BU5D_t80C28472AABC27AC3809B9300B1F81D3AB423DDC* ___shadowRendererLists;
	RendererListHandleU5BU5D_t9F182E8E1E5140DEC19C6EE1F096F0329CCB7E3B* ___shadowRendererListsHandle;
};
struct UniversalRenderPipelineAsset_tE8A9AA6F030CC3B558CEA2EB54FFF4FC58CA6232  : public RenderPipelineAsset_1_t2873A88178504E8793D044D4422CB05E32AEB121
{
	ScriptableRendererU5BU5D_t9B15C048BCE03A67E830F1C79989B6A3E43788E6* ___m_Renderers;
	int32_t ___k_AssetVersion;
	int32_t ___k_AssetPreviousVersion;
	int32_t ___m_RendererType;
	ScriptableRendererData_t9005CE645D4881FA4431E52EDC7678203632CAA7* ___m_RendererData;
	ScriptableRendererDataU5BU5D_tC674C147618C92B68DB64ECFDC847C8A941C6169* ___m_RendererDataList;
	int32_t ___m_DefaultRendererIndex;
	bool ___m_RequireDepthTexture;
	bool ___m_RequireOpaqueTexture;
	int32_t ___m_OpaqueDownsampling;
	bool ___m_SupportsTerrainHoles;
	bool ___m_SupportsHDR;
	int32_t ___m_HDRColorBufferPrecision;
	int32_t ___m_MSAA;
	float ___m_RenderScale;
	int32_t ___m_UpscalingFilter;
	bool ___m_FsrOverrideSharpness;
	float ___m_FsrSharpness;
	bool ___m_EnableLODCrossFade;
	int32_t ___m_LODCrossFadeDitheringType;
	int32_t ___m_ShEvalMode;
	int32_t ___m_LightProbeSystem;
	int32_t ___m_ProbeVolumeMemoryBudget;
	int32_t ___m_ProbeVolumeBlendingMemoryBudget;
	bool ___m_SupportProbeVolumeGPUStreaming;
	bool ___m_SupportProbeVolumeDiskStreaming;
	bool ___m_SupportProbeVolumeScenarios;
	bool ___m_SupportProbeVolumeScenarioBlending;
	int32_t ___m_ProbeVolumeSHBands;
	int32_t ___m_MainLightRenderingMode;
	bool ___m_MainLightShadowsSupported;
	int32_t ___m_MainLightShadowmapResolution;
	int32_t ___m_AdditionalLightsRenderingMode;
	int32_t ___m_AdditionalLightsPerObjectLimit;
	bool ___m_AdditionalLightShadowsSupported;
	int32_t ___m_AdditionalLightsShadowmapResolution;
	int32_t ___m_AdditionalLightsShadowResolutionTierLow;
	int32_t ___m_AdditionalLightsShadowResolutionTierMedium;
	int32_t ___m_AdditionalLightsShadowResolutionTierHigh;
	bool ___m_ReflectionProbeBlending;
	bool ___m_ReflectionProbeBoxProjection;
	bool ___m_ReflectionProbeAtlas;
	float ___m_ShadowDistance;
	int32_t ___m_ShadowCascadeCount;
	float ___m_Cascade2Split;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___m_Cascade3Split;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_Cascade4Split;
	float ___m_CascadeBorder;
	float ___m_ShadowDepthBias;
	float ___m_ShadowNormalBias;
	bool ___m_SoftShadowsSupported;
	bool ___m_ConservativeEnclosingSphere;
	int32_t ___m_NumIterationsEnclosingSphere;
	int32_t ___m_SoftShadowQuality;
	int32_t ___m_AdditionalLightsCookieResolution;
	int32_t ___m_AdditionalLightsCookieFormat;
	bool ___m_UseSRPBatcher;
	bool ___m_SupportsDynamicBatching;
	bool ___m_MixedLightingSupported;
	bool ___m_SupportsLightCookies;
	bool ___m_SupportsLightLayers;
	int32_t ___m_DebugLevel;
	int32_t ___m_StoreActionsOptimization;
	bool ___m_UseAdaptivePerformance;
	int32_t ___m_ColorGradingMode;
	int32_t ___m_ColorGradingLutSize;
	bool ___m_AllowPostProcessAlphaOutput;
	bool ___m_UseFastSRGBLinearConversion;
	bool ___m_SupportDataDrivenLensFlare;
	bool ___m_SupportScreenSpaceLensFlare;
	uint8_t ___m_GPUResidentDrawerMode;
	float ___m_SmallMeshScreenPercentage;
	bool ___m_GPUResidentDrawerEnableOcclusionCullingInCameras;
	int32_t ___m_ShadowType;
	bool ___m_LocalShadowsSupported;
	int32_t ___m_LocalShadowsAtlasResolution;
	int32_t ___m_MaxPixelLights;
	int32_t ___m_ShadowAtlasResolution;
	int32_t ___m_VolumeFrameworkUpdateMode;
	VolumeProfile_t9B5F2005F575A710F38A124EF81A6228CCACACE1* ___m_VolumeProfile;
	ProbeVolumeSceneData_t29FD126D36ED1E093C2EBBFB3F248DD5E2A86D90* ___apvScenesData;
	Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* ___m_DefaultShader;
	int32_t ___m_ShaderVariantLogLevel;
	int32_t ___m_ShadowCascades;
	TextureResources_t8FB6A098EBF3C08BF1BD5EA926743C0FA595202B* ___m_Textures;
};
struct TypeId_1_tF7C39317892E31289E8C529424E70ED463C8334C_StaticFields
{
	uint32_t ___value;
};
struct U3CPrivateImplementationDetailsU3E_t16CE31F4DEE6BA0AEFEB3FA0105D58630695B339_StaticFields
{
	__StaticArrayInitTypeSizeU3D16_tB86B9BFC4ADBF4E2DF11F39AF43639693C65DF05 ___08243D32F28C35701F6EA57F52AE707302C8528E8D358F13C6E6915543D265C6;
	__StaticArrayInitTypeSizeU3D24_tB605E983EFADFA4C2759D8C48AB45B0B3A7BCC51 ___18689A54C1FF754BE58500B2ED77A6C75B025BE96F6D01FEF89C42DA1C953F34;
	__StaticArrayInitTypeSizeU3D12_t5F40C9EEDE242DFE47A8DCE218ED3DF3E88B4EC0 ___4636993D3E1DA4E9D6B8F87B79E8F7C6D018580D52661950EABC3845C5897A4D;
	__StaticArrayInitTypeSizeU3D960_t86900CB1F8550ABFAD884FDD8E17F7B7AA90ED0D ___6322123493378558D4F9DD025993C168685B194246485704DD5B391FDCD77A64;
	__StaticArrayInitTypeSizeU3D12_t5F40C9EEDE242DFE47A8DCE218ED3DF3E88B4EC0 ___67F97CD57FBAB63018C13EBA31B2DF63BC71EFF185FC0E19E116C63B1954D9EA;
	__StaticArrayInitTypeSizeU3D19683_tBE7B83EE59191082CB97010C2CB0D4428C300DEF ___702DFD76CFE9AC288D20328B8741B80990D007B60D0B227DE3BC87261834745B;
	__StaticArrayInitTypeSizeU3D16_tB86B9BFC4ADBF4E2DF11F39AF43639693C65DF05 ___888955380992D62883B27CC51FDC7E5C290C441026048F403C5618F305AD2BF1;
	__StaticArrayInitTypeSizeU3D24908_t065ED3CA4290ED87B3640EFB82C5399994BB4775 ___91A93E725CDC484F32294E4B975CDC70D305E85E07DB245F35D515BBEA67C2A3;
	__StaticArrayInitTypeSizeU3D12_t5F40C9EEDE242DFE47A8DCE218ED3DF3E88B4EC0 ___9D3A6E7E88415D8C1A0F3887B6384A9A8E4F44A036C5A24796C319751ACACCAD;
	__StaticArrayInitTypeSizeU3D12_t5F40C9EEDE242DFE47A8DCE218ED3DF3E88B4EC0 ___B6599D21CE74F24FA42D57991D6B0D0C5770322C90AF734EEB36A37F74090137;
	__StaticArrayInitTypeSizeU3D16_tB86B9BFC4ADBF4E2DF11F39AF43639693C65DF05 ___BAED642339816AFFB3FE8719792D0E4CE82F12DB72B7373D244EAA65445800FE;
	__StaticArrayInitTypeSizeU3D16_tB86B9BFC4ADBF4E2DF11F39AF43639693C65DF05 ___C94719FC63BFC7010A8361E8B4D4746BAB3C8AD593769F86532655EE58EBB101;
	__StaticArrayInitTypeSizeU3D960_t86900CB1F8550ABFAD884FDD8E17F7B7AA90ED0D ___E2EF5640DF412939A64301FFA3F66A62A34FA6E45A26E62F6994E5390B380D01;
};
struct ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C_StaticFields
{
	uint32_t ___s_TypeCount;
};
struct ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91_StaticFields
{
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___MainLightShadows;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___MainLightShadowCascades;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___MainLightShadowScreen;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___CastingPunctualLightShadow;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___AdditionalLightsVertex;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___AdditionalLightsPixel;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___ClusterLightLoop;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___AdditionalLightShadows;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___ReflectionProbeBoxProjection;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___ReflectionProbeBlending;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___ReflectionProbeAtlas;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___SoftShadows;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___SoftShadowsLow;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___SoftShadowsMedium;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___SoftShadowsHigh;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___MixedLightingSubtractive;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___LightmapShadowMixing;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___ShadowsShadowMask;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___LightLayers;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___RenderPassEnabled;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___BillboardFaceCameraPos;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___LightCookies;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DepthNoMsaa;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DepthMsaa2;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DepthMsaa4;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DepthMsaa8;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DBufferMRT1;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DBufferMRT2;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DBufferMRT3;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DecalNormalBlendLow;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DecalNormalBlendMedium;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DecalNormalBlendHigh;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DecalLayers;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___WriteRenderingLayers;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___ScreenSpaceOcclusion;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____SPOT;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____DIRECTIONAL;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____POINT;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____DEFERRED_STENCIL;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____DEFERRED_FIRST_LIGHT;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____DEFERRED_MAIN_LIGHT;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____GBUFFER_NORMALS_OCT;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____DEFERRED_MIXED_LIGHTING;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___LIGHTMAP_ON;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DYNAMICLIGHTMAP_ON;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____ALPHATEST_ON;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DIRLIGHTMAP_COMBINED;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____DETAIL_MULX2;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____DETAIL_SCALED;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____CLEARCOAT;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____CLEARCOATMAP;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DEBUG_DISPLAY;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___LOD_FADE_CROSSFADE;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___USE_UNITY_CROSSFADE;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____EMISSION;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____RECEIVE_SHADOWS_OFF;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____SURFACE_TYPE_TRANSPARENT;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____ALPHAPREMULTIPLY_ON;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____ALPHAMODULATE_ON;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____NORMALMAP;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____ADD_PRECOMPUTED_VELOCITY;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___EDITOR_VISUALIZATION;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___FoveatedRenderingNonUniformRaster;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DisableTexture2DXArray;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___BlitSingleSlice;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___XROcclusionMeshCombined;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___SCREEN_COORD_OVERRIDE;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DOWNSAMPLING_SIZE_2;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DOWNSAMPLING_SIZE_4;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DOWNSAMPLING_SIZE_8;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___DOWNSAMPLING_SIZE_16;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___EVALUATE_SH_MIXED;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___EVALUATE_SH_VERTEX;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___ProbeVolumeL1;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___ProbeVolumeL2;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___LIGHTMAP_BICUBIC_SAMPLING;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____OUTPUT_DEPTH;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___LinearToSRGBConversion;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ____ENABLE_ALPHA_OUTPUT;
	GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D ___ForwardPlus;
};
struct ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_StaticFields
{
	bool ___m_ForceShadowPointSampling;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192_StaticFields
{
	U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192* ___U3CU3E9;
	BaseRenderFunc_2_t914B3BA6065F3AEE258976DD7F37AF37D8C5BCC8* ___U3CU3E9__21_0;
};
struct U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_StaticFields
{
	U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264* ___U3CU3E9;
	BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D* ___U3CU3E9__39_0;
};
struct MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields
{
	int32_t ____WorldToShadow;
	int32_t ____ShadowParams;
	int32_t ____CascadeShadowSplitSpheres0;
	int32_t ____CascadeShadowSplitSpheres1;
	int32_t ____CascadeShadowSplitSpheres2;
	int32_t ____CascadeShadowSplitSpheres3;
	int32_t ____CascadeShadowSplitSphereRadii;
	int32_t ____ShadowOffset0;
	int32_t ____ShadowOffset1;
	int32_t ____ShadowmapSize;
	int32_t ____MainLightShadowmapID;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct CommandBufferHelpers_tB8C09D01D6F0369DF52DEA955F017D6001BC72E5_StaticFields
{
	RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___rasterCmd;
	ComputeCommandBuffer_tA6FA5F68FE745317C94802C6A828FC21AC0ADF61* ___computeCmd;
	UnsafeCommandBuffer_tDE6BB2FE234DC7453CA682AB275888E9E35F22F2* ___unsafeCmd;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6_StaticFields
{
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___zeroMatrix;
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___identityMatrix;
};
struct RasterGraphContext_tC4D3E53FF7131476487751ACB0237D56C8327147_StaticFields
{
	RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___rastercmd;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___zeroVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___oneVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___downVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___forwardVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___backVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___positiveInfinityVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___negativeInfinityVector;
};
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_StaticFields
{
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___zeroVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___oneVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___positiveInfinityVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___negativeInfinityVector;
};
struct CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7_StaticFields
{
	bool ___ThrowOnSetRenderTarget;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject;
};
struct RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85_StaticFields
{
	RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 ___nullRendererList;
};
struct ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36_StaticFields
{
	ShaderTagId_t453E2085B5EE9448FF75E550CAB111EFF690ECB0 ___kRenderTypeTag;
};
struct ShadowSplitData_tC276A96F461DD73CFF6D94DB557D42A1643640DF_StaticFields
{
	int32_t ___maximumCullingPlaneCount;
};
struct RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E_StaticFields
{
	int32_t ___kMaxMRTCount;
	List_1_tB5216E2043E168F4E0A122E4594A52A4BA2394F2* ___s_RegisteredGraphs;
	bool ___U3CisRenderGraphViewerActiveU3Ek__BackingField;
	bool ___U3CenableValidityChecksU3Ek__BackingField;
	OnGraphRegisteredDelegate_t7DEB75DB071EA0F49C47E2617D72D40EF3A8188C* ___onGraphRegistered;
	OnGraphRegisteredDelegate_t7DEB75DB071EA0F49C47E2617D72D40EF3A8188C* ___onGraphUnregistered;
	OnExecutionRegisteredDelegate_tCCD234646691F298602408925867AE91256D2226* ___onExecutionRegistered;
	OnExecutionRegisteredDelegate_tCCD234646691F298602408925867AE91256D2226* ___onExecutionUnregistered;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___onDebugDataCaptured;
};
struct RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_StaticFields
{
	bool ___m_AisBackBuffer;
	RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46 ___m_Desc;
};
struct RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B_StaticFields
{
	RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B ___Invalid;
};
struct ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C_StaticFields
{
	uint32_t ___s_CurrentValidBit;
	uint32_t ___s_SharedResourceValidBit;
};
struct Type_t_StaticFields
{
	Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235* ___s_defaultBinder;
	Il2CppChar ___Delimiter;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___EmptyTypes;
	RuntimeObject* ___Missing;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterAttribute;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterName;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterNameIgnoreCase;
};
struct ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0_StaticFields
{
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___k_CameraTarget;
};
struct TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388_StaticFields
{
	TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 ___s_NullHandle;
};
struct MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_StaticFields
{
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___s_EmptyShadowParams;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___s_EmptyShadowmapSize;
};
struct ScriptableRenderer_tF15B95BB85F26BE4B4719901D909831B89DC8892_StaticFields
{
	ScriptableRenderer_tF15B95BB85F26BE4B4719901D909831B89DC8892* ___current;
	bool ___m_UseOptimizedStoreActions;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___k_CameraTarget;
	RenderTargetIdentifierU5BU5D_t179798C153B7CE381B41C57863F98CB24023C4CE* ___m_ActiveColorAttachmentIDs;
	RTHandleU5BU5D_tE4B403B060D159B839BF74E8B59F8DCD52CF97DF* ___m_ActiveColorAttachments;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___m_ActiveDepthAttachment;
	RenderBufferStoreActionU5BU5D_tFEA8F5DD460573EA9F35FBEC5727D1804C5DCBF5* ___m_ActiveColorStoreActions;
	int32_t ___m_ActiveDepthStoreAction;
	RenderTargetIdentifierU5BU5DU5BU5D_tDB35F8D017FE3AD8BB35E08E323074D47C5A10BB* ___m_TrimmedColorAttachmentCopyIDs;
	RTHandleU5BU5DU5BU5D_t6066EC3F94599A5A42DA9A893F102E9618CB612F* ___m_TrimmedColorAttachmentCopies;
	PlaneU5BU5D_t4EEF66BAA8B0140EFFF34F6183CE7F80546592BE* ___s_Planes;
	Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* ___s_VectorPlanes;
};
struct UniversalRenderPipelineAsset_tE8A9AA6F030CC3B558CEA2EB54FFF4FC58CA6232_StaticFields
{
	int32_t ___AdditionalLightsDefaultShadowResolutionTierLow;
	int32_t ___AdditionalLightsDefaultShadowResolutionTierMedium;
	int32_t ___AdditionalLightsDefaultShadowResolutionTierHigh;
	StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* ___s_Names;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___s_Values;
	GraphicsFormatU5BU5DU5BU5D_t1424BD937A890524D2A66FF39E61DEB0F10FE0A2* ___s_LightCookieFormatList;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D  : public RuntimeArray
{
	ALIGN_FIELD (8) Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 m_Items[1];

	inline Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 value)
	{
		m_Items[index] = value;
	}
};
struct ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04  : public RuntimeArray
{
	ALIGN_FIELD (8) ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20 m_Items[1];

	inline ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20 GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20 value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20 GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20 value)
	{
		m_Items[index] = value;
	}
};
struct Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD  : public RuntimeArray
{
	ALIGN_FIELD (8) Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 m_Items[1];

	inline Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 value)
	{
		m_Items[index] = value;
	}
};
struct RendererListU5BU5D_t80C28472AABC27AC3809B9300B1F81D3AB423DDC  : public RuntimeArray
{
	ALIGN_FIELD (8) RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 m_Items[1];

	inline RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 value)
	{
		m_Items[index] = value;
	}
};
struct RendererListHandleU5BU5D_t9F182E8E1E5140DEC19C6EE1F096F0329CCB7E3B  : public RuntimeArray
{
	ALIGN_FIELD (8) RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA m_Items[1];

	inline RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA value)
	{
		m_Items[index] = value;
	}
};
struct ItemU5BU5D_t4AC8CDDFF9ED634B9ECE154F0C638C00B12B5CCB  : public RuntimeArray
{
	ALIGN_FIELD (8) Item_tFA847F868B1ECD768A489638A61F7BC398DECDAC m_Items[1];

	inline Item_tFA847F868B1ECD768A489638A61F7BC398DECDAC GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Item_tFA847F868B1ECD768A489638A61F7BC398DECDAC* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Item_tFA847F868B1ECD768A489638A61F7BC398DECDAC value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___storage), (void*)NULL);
	}
	inline Item_tFA847F868B1ECD768A489638A61F7BC398DECDAC GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Item_tFA847F868B1ECD768A489638A61F7BC398DECDAC* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Item_tFA847F868B1ECD768A489638A61F7BC398DECDAC value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___storage), (void*)NULL);
	}
};


IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* ContextContainer_Get_TisRuntimeObject_mD332AE37F62256B78E48145FFDEADB66FEEF3A5E_gshared_inline (ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR URPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9* NativeArrayExtensions_UnsafeElementAt_TisURPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9_m56159FAB7E5AD01E526C4937CB1FAFF4F8F52A7D_gshared (NativeArray_1_t4C11F337CF2A7773644650D071AA5F21F158A5E0 ___0_array, int32_t ___1_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20* NativeArrayExtensions_UnsafeElementAt_TisShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20_m172973527DE9C444ABA117A4CE8B1D951C896487_gshared (NativeArray_1_t9B7A94FA050F43A3996B812B9164E7885F38ADC3 ___0_array, int32_t ___1_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* ProfilingSampler_Get_TisInt32Enum_tCBAC8BA2BFF3A845FA599F303093BBBA374B6F0C_m8A88D1A0EBDE48471A988A3C0B292D7E590709B2_gshared (int32_t ___0_marker, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* RenderGraph_AddRasterRenderPass_TisRuntimeObject_mC67DBCDAE9E5C0D6FA1406B1CD4EA8A1F9244044_gshared (RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* __this, String_t* ___0_passName, RuntimeObject** ___1_passData, ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* ___2_sampler, String_t* ___3_file, int32_t ___4_line, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BaseRenderFunc_2__ctor_m72268C37DF7D310181ABEF09D65817B1108D843E_gshared (BaseRenderFunc_2_tA367BC6873E6166CAEC57DF6061E145117B9C512* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_mBF1B8DCCD140DBF4D4E6CB55011C43FAA9A8555F (U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 RendererListHandle_op_Implicit_m23F3E49F9D97B0BABE1044E02A7A70784F05C585 (RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA ___0_rendererList, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GBufferPass_ExecutePass_mB750B5A6C75C1247A55C7C2F2DA20999F5EC2D54 (RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___0_cmd, PassData_tD366FA555BBB5E9EA116F4553DA083826230EB0B* ___1_data, RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 ___2_rendererList, RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 ___3_errorRendererList, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProfilingSampler__ctor_m26500989FCDB07FA33C9A3BB7F215CBD892F5BB7 (ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* __this, String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ScriptableRenderPass__ctor_mE49D4FF8E68A854367A4081E664B8DBA74E6B752 (ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ScriptableRenderPass_set_profilingSampler_mFD238B85B68DED586BA8C678141BEEAF229FBF2D (ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0* __this, ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ScriptableRenderPass_set_renderPassEvent_m63FA581FFDE1C69C2E1358BD0B8DB30275334960_inline (ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PassData__ctor_m8A566CD8229EC1D10D7A229EC89195F68F862CE9 (PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RTHandle_Release_m743C2A22FD95D177D2D425E9DF1F3088161F387B (RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* __this, const RuntimeMethod* method) ;
inline UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* ContextContainer_Get_TisUniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6_m71849CEFB9C023EDE026A0F38CE6044274505C06_inline (ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* __this, const RuntimeMethod* method)
{
	return ((  UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* (*) (ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C*, const RuntimeMethod*))ContextContainer_Get_TisRuntimeObject_mD332AE37F62256B78E48145FFDEADB66FEEF3A5E_gshared_inline)(__this, method);
}
inline UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* ContextContainer_Get_TisUniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7_mE62F7903614384541B770CC0B9A99BD2E608A0F8_inline (ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* __this, const RuntimeMethod* method)
{
	return ((  UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* (*) (ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C*, const RuntimeMethod*))ContextContainer_Get_TisRuntimeObject_mD332AE37F62256B78E48145FFDEADB66FEEF3A5E_gshared_inline)(__this, method);
}
inline UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* ContextContainer_Get_TisUniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2_m7D529C251F384985900DEA334BC399BAD36BFC8F_inline (ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* __this, const RuntimeMethod* method)
{
	return ((  UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* (*) (ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C*, const RuntimeMethod*))ContextContainer_Get_TisRuntimeObject_mD332AE37F62256B78E48145FFDEADB66FEEF3A5E_gshared_inline)(__this, method);
}
inline UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* ContextContainer_Get_TisUniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C_m071A668846CA61588A4D53CCA3CFCD3730530E4E_inline (ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* __this, const RuntimeMethod* method)
{
	return ((  UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* (*) (ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C*, const RuntimeMethod*))ContextContainer_Get_TisRuntimeObject_mD332AE37F62256B78E48145FFDEADB66FEEF3A5E_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MainLightShadowCasterPass_Setup_mBE7DBA64DB246FF091229EC1B945D516D2868CDC (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* ___0_renderingData, UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* ___1_cameraData, UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* ___2_lightData, UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* ___3_shadowData, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProfilingScope__ctor_m4B73587A2295443A73B64DDD3D484D8EAECC0D65 (ProfilingScope_t57898BA31E8EF8F083EF84E0DA2737AC61CBC5BD* __this, ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* ___0_sampler, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProfilingScope_Dispose_m4231A2ACA1F8E345BB0078310A9F7601704C8BE4 (ProfilingScope_t57898BA31E8EF8F083EF84E0DA2737AC61CBC5BD* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool ScriptableRenderer_get_stripShadowsOffVariants_mEC78AA6E4F4353DEF4DA00EB6E2BF7A55CEE322F_inline (ScriptableRenderer_tF15B95BB85F26BE4B4719901D909831B89DC8892* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_Clear_m87F3BD44E2481FBD03E0A7F66E4EAC6363BD9728 (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MainLightShadowCasterPass_SetupForEmptyRendering_m18DFF100A5175846F92FC5BA4E4DEDCEBCE227C4 (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, bool ___0_stripShadowsOffVariants, bool ___1_shadowsEnabled, Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* ___2_light, UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* ___3_cameraData, UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* ___4_shadowData, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* VisibleLight_get_light_mD179E0BF18C77DBE2FD85FE9687F63A8C1859E6B (VisibleLight_t0A4DF5B22865A00F618A0352B805277FA0132805* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Light_get_shadows_m1A11721F202C27838A7A8ED72455E6A727CEE6C5 (Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR LightBakingOutput_t6212AB0B6B34C94F1982FE964FC48201854B5B90 Light_get_bakingOutput_mF383DB97CFD32D65DA468329E18DD2DD61521CED (Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t VisibleLight_get_lightType_mFFCEBE6E368853F13E7CDBA910F6D9B689292454 (VisibleLight_t0A4DF5B22865A00F618A0352B805277FA0132805* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_LogWarning_m33EF1B897E0C7C6FF538989610BFAFFEF4628CA9 (RuntimeObject* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool CullingResults_GetShadowCasterBounds_m5DD3647DB1560ECCF6620DD7DE16D6304012CF0B (CullingResults_tD6B7EF20B68D47DFF3A99EB2EA73F47F1D460267* __this, int32_t ___0_lightIndex, Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3* ___1_outBounds, const RuntimeMethod* method) ;
inline URPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9* NativeArrayExtensions_UnsafeElementAt_TisURPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9_m56159FAB7E5AD01E526C4937CB1FAFF4F8F52A7D (NativeArray_1_t4C11F337CF2A7773644650D071AA5F21F158A5E0 ___0_array, int32_t ___1_index, const RuntimeMethod* method)
{
	return ((  URPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9* (*) (NativeArray_1_t4C11F337CF2A7773644650D071AA5F21F158A5E0, int32_t, const RuntimeMethod*))NativeArrayExtensions_UnsafeElementAt_TisURPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9_m56159FAB7E5AD01E526C4937CB1FAFF4F8F52A7D_gshared)(___0_array, ___1_index, method);
}
inline ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20* NativeArrayExtensions_UnsafeElementAt_TisShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20_m172973527DE9C444ABA117A4CE8B1D951C896487 (NativeArray_1_t9B7A94FA050F43A3996B812B9164E7885F38ADC3 ___0_array, int32_t ___1_index, const RuntimeMethod* method)
{
	return ((  ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20* (*) (NativeArray_1_t9B7A94FA050F43A3996B812B9164E7885F38ADC3, int32_t, const RuntimeMethod*))NativeArrayExtensions_UnsafeElementAt_TisShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20_m172973527DE9C444ABA117A4CE8B1D951C896487_gshared)(___0_array, ___1_index, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ShadowSplitData_get_cullingSphere_mEABEC468FE12ADDB09239EABD3FB59551E4A44E0 (ShadowSplitData_tC276A96F461DD73CFF6D94DB557D42A1643640DF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool URPLightShadowCullingInfos_IsSliceValid_m7A4FD76F47B4EA4E62951751ED4FD63E69535BD8 (URPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9* __this, int32_t ___0_i, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_UpdateTextureDescriptorIfNeeded_mAA75D25F976BC7984D895956B30417CD707CF95B (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ScriptableRenderPass_set_useNativeRenderPass_m1D60C30BB1CF1B4D383FFCABC1F57EA755626895_inline (ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t RenderTextureDescriptor_get_width_mB159E4EB08B23B19CCCFADB465864361FB840BFF_inline (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t RenderTextureDescriptor_get_height_m1006F9AA45029715C552C8A8C2F102F63D3A91EC_inline (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t RenderTextureDescriptor_get_depthBufferBits_mC095E36F9803B2E68E258C03E48ACD0B0E678953 (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t RenderTextureDescriptor_get_colorFormat_mF87FD5E3AC4688BBB921568003ED4A1FFB1614FF (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RenderTextureDescriptor__ctor_mE27A3C225736C1F806C12A7C31C0DC66A0AFE61B (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* __this, int32_t ___0_width, int32_t ___1_height, int32_t ___2_colorFormat, int32_t ___3_depthBufferBits, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline (Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* __this, float ___0_x, float ___1_y, float ___2_z, float ___3_w, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float ShadowUtils_SoftShadowQualityToShaderProperty_m02B6A27D17A4C26FA5E622F580113664A6ED3BE8 (Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* ___0_light, bool ___1_softShadowsEnabled, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ShadowUtils_GetScaleAndBiasForLinearDistanceFade_mE07E0F336969447E89E448D23AF050BF1646B20F (float ___0_fadeDistance, float ___1_border, float* ___2_scale, float* ___3_bias, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Light_get_shadowStrength_m4AB6E78F7A28A97C61EDBD06ECEAF8A565688FC8 (Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ShadowUtils_ShadowRTReAllocateIfNeeded_m1D2B971FC205B86AA3CCB3396FD0DDCC07B2198B (RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B** ___0_handle, int32_t ___1_width, int32_t ___2_height, int32_t ___3_bits, int32_t ___4_anisoLevel, float ___5_mipMapBias, String_t* ___6_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ScriptableRenderPass_ConfigureTarget_m6767C5E94D51F989348F415771B770DE844FD4A6 (ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0* __this, RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___0_colorAttachment, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_black_mB50217951591A045844C61E7FF31EEE3FEF16737_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ScriptableRenderPass_ConfigureClear_m5C82128C3ABDD63621501DC012ED91F392ABF123 (ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0* __this, int32_t ___0_clearFlag, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___1_clearColor, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7** RenderingData_get_commandBuffer_m747CD6ABF19DD5BB05F8231CC84A9922D9DC080A (RenderingData_tAAA01190551D6D5954314E3E1E85B58DD45EED71* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* CommandBufferHelpers_GetRasterCommandBuffer_m6086D650343F166614B3FB5ED89D63DE8F85C42B_inline (CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* ___0_baseBuffer, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RasterCommandBuffer_EnableKeyword_m554A685119A5DE8DAD67ADA432176DCEFEDA3494 (RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* __this, GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D* ___0_keyword, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_SetShadowParamsForEmptyShadowmap_mCBE8758960CEB5A2DF6953350A969A492312810E (RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___0_rasterCommandBuffer, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* UniversalRenderingData_get_commandBuffer_m8397484CEAB1A0D725DEA8A85C9B955E2B4007F8 (UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B RTHandle_get_nameID_m30AF2567853494DB845D83A8B37D0FB523DA76E9_inline (RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CommandBuffer_SetGlobalTexture_m65E012CB3C35EA43533CB4FF4C6F6498FDE229CD (CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* __this, int32_t ___0_nameID, RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_InitPassData_m53ED19C2B72CB997374CEC86E590E380BD9DEF6E (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** ___0_passData, UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* ___1_renderingData, UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* ___2_cameraData, UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* ___3_lightData, UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* ___4_shadowData, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_InitRendererLists_mF29AB3ADDD25931ABEC828A4A71189E87F3E9A93 (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** ___0_passData, ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 ___1_context, RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* ___2_renderGraph, bool ___3_useRenderGraph, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_RenderMainLightCascadeShadowmap_m7F529AA6F3582E375793023C425D366AA04F0547 (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___0_cmd, PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** ___1_data, bool ___2_isRenderGraph, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 Matrix4x4_get_identity_m6568A73831F3E2D587420D20FF423959D7D8AB56_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ShadowSliceData_Clear_mB5BFA7D8B81B48BD2CCF60B127DC0AFBAD9CC6BC (ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RasterCommandBuffer_SetGlobalVector_mB54A698709E920A961D8601DC5B2452B4866C68E (RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* __this, int32_t ___0_nameID, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___1_value, const RuntimeMethod* method) ;
inline ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* ProfilingSampler_Get_TisURPProfileId_tA83520239B6C0F10A73CCC6CEC7D3DA1F1932481_m9F3104BDEBD70A287E8F4BAC86579564A19A5661 (int32_t ___0_marker, const RuntimeMethod* method)
{
	return ((  ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* (*) (int32_t, const RuntimeMethod*))ProfilingSampler_Get_TisInt32Enum_tCBAC8BA2BFF3A845FA599F303093BBBA374B6F0C_m8A88D1A0EBDE48471A988A3C0B292D7E590709B2_gshared)(___0_marker, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ProfilingScope__ctor_mEF7BF01DCAD3709F978E564AEDEDD643FC617904 (ProfilingScope_t57898BA31E8EF8F083EF84E0DA2737AC61CBC5BD* __this, BaseCommandBuffer_tD67BB9B3F740537BD3F3A96FA17D06E6C3BFDC06* ___0_cmd, ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* ___1_sampler, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ShadowUtils_SetCameraPosition_mD5AB6C44100346891E7C1ADC676715C9C4D4B094 (RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___0_cmd, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_worldSpaceCameraPos, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 UniversalCameraData_GetViewMatrix_mE4676E11126A0A1F10B2425B245CF438A671A21A (UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* __this, int32_t ___0_viewIndex, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ShadowUtils_SetWorldToCameraAndCameraToWorldMatrices_mE2BC025C6530F7E5E287A9E43129CF7C6D541793 (RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___0_cmd, Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___1_viewMatrix, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ShadowUtils_GetShadowBias_m865F36086076039F16AF6D1AC4000B8169839BD6 (VisibleLight_t0A4DF5B22865A00F618A0352B805277FA0132805* ___0_shadowLight, int32_t ___1_shadowLightIndex, UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* ___2_shadowData, Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___3_lightProjectionMatrix, float ___4_shadowResolution, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ShadowUtils_SetupShadowCasterConstantBuffer_mA686DC7DDB43AA723119176A4143A494403629EB (RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___0_cmd, VisibleLight_t0A4DF5B22865A00F618A0352B805277FA0132805* ___1_shadowLight, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___2_shadowBias, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RasterCommandBuffer_SetKeyword_m134867C426359E758261AB04E59061F70A69B5C3 (RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* __this, GlobalKeyword_tFA029618DA6ADCA20CFA44C1B8AFFF9EA097AE7D* ___0_keyword, bool ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ShadowUtils_RenderShadowSlice_m8A4EF57464A61CC7AB7C5E01EA905AFE3F23FF7B (RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___0_cmd, ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20* ___1_shadowSliceData, RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85* ___2_shadowRendererList, Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___3_proj, Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___4_view, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ShadowUtils_SetSoftShadowQualityShaderKeywords_m295C5B71C0C784A63CBED40DDE0F3762D8268492 (RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___0_cmd, UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* ___1_shadowData, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_SetupMainLightShadowReceiverConstants_m9FCB09B3813A1B73704CE6B5C3BBB46439979131 (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___0_cmd, VisibleLight_t0A4DF5B22865A00F618A0352B805277FA0132805* ___1_shadowLight, UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* ___2_shadowData, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 Matrix4x4_get_zero_m5D5F0475AD231C2C6BE5A9C80E11E24013B1B827 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool SystemInfo_get_usesReversedZBuffer_m52819B4B538F590FCA0370FC99775B3AA6B32514 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RasterCommandBuffer_SetGlobalMatrixArray_m8AFED067E7189FA1F1D301BFD7E1F31AF191EDA1 (RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* __this, int32_t ___0_nameID, Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D* ___1_values, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ShadowDrawingSettings__ctor_mA9BADD0F63BF177F6BF380999B9B7115263B2BF4 (ShadowDrawingSettings_t3C0AD7F3960F1BF5536867AF6E641F23F4C7AFA4* __this, CullingResults_tD6B7EF20B68D47DFF3A99EB2EA73F47F1D460267 ___0_cullingResults, int32_t ___1_lightIndex, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR UniversalRenderPipelineAsset_tE8A9AA6F030CC3B558CEA2EB54FFF4FC58CA6232* UniversalRenderPipeline_get_asset_mCDEF564C748A6FE271F3749C82ECA64D0F6DE9E9 (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool UniversalRenderPipelineAsset_get_useRenderingLayers_mA473541E634D2A1BEB4CEAFBF27B79251E0FA5E6_inline (UniversalRenderPipelineAsset_tE8A9AA6F030CC3B558CEA2EB54FFF4FC58CA6232* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ShadowDrawingSettings_set_useRenderingLayerMaskTest_m9E81FEE30547B3720C365016689F87A2AB63F025 (ShadowDrawingSettings_t3C0AD7F3960F1BF5536867AF6E641F23F4C7AFA4* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA RenderGraph_CreateShadowRendererList_mB2EE56B34D7B4C2544C0044F040979D561AB12EE (RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* __this, ShadowDrawingSettings_t3C0AD7F3960F1BF5536867AF6E641F23F4C7AFA4* ___0_shadowDrawingSettings, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 ScriptableRenderContext_CreateShadowRendererList_m34053F9A4CF0DCB00BD4A8816C2756A3943DDA3D (ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36* __this, ShadowDrawingSettings_t3C0AD7F3960F1BF5536867AF6E641F23F4C7AFA4* ___0_settings, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* ScriptableRenderPass_get_passName_m838292A44DB6ED7D67E43C1DE58383959B4F1925_inline (ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* ScriptableRenderPass_get_profilingSampler_m627C9BF8A4A08101DCB6F40E0A97145A5A1CDA38 (ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0* __this, const RuntimeMethod* method) ;
inline RuntimeObject* RenderGraph_AddRasterRenderPass_TisPassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A_mC53A7D9CBCFE77D6877B8B7B2819DACBFD86AE9B (RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* __this, String_t* ___0_passName, PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** ___1_passData, ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* ___2_sampler, String_t* ___3_file, int32_t ___4_line, const RuntimeMethod* method)
{
	return ((  RuntimeObject* (*) (RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E*, String_t*, PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**, ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE*, String_t*, int32_t, const RuntimeMethod*))RenderGraph_AddRasterRenderPass_TisRuntimeObject_mC67DBCDAE9E5C0D6FA1406B1CD4EA8A1F9244044_gshared)(__this, ___0_passName, ___1_passData, ___2_sampler, ___3_file, ___4_line, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 UniversalRenderer_CreateRenderGraphTexture_m4BCA4F2339499873D3DE1C8562D3FB7B7DE21613 (RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* ___0_renderGraph, RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46 ___1_desc, String_t* ___2_name, bool ___3_clear, int32_t ___4_filterMode, int32_t ___5_wrapMode, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RenderGraphDefaultResources_tCE331152C84ED1A36CF186CA0092AE10E599E25D* RenderGraph_get_defaultResources_m9392476073E82DC8F45ED8AB11B271EA471FC206_inline (RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 RenderGraphDefaultResources_get_defaultShadowTexture_mF16D95793225F7D0A560E587401F5638A8A8E913_inline (RenderGraphDefaultResources_tCE331152C84ED1A36CF186CA0092AE10E599E25D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool TextureHandle_IsValid_mECFF64B8BAC6402F0D37B67BB79FFB3AB3C7F3C2_inline (TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388* __this, const RuntimeMethod* method) ;
inline void BaseRenderFunc_2__ctor_m37F90588EE8CDF8E636149561D131DF846D2406E (BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D*, RuntimeObject*, intptr_t, const RuntimeMethod*))BaseRenderFunc_2__ctor_m72268C37DF7D310181ABEF09D65817B1108D843E_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA (String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_m058892C1C716FD6680DFAFD5ED2A8BFAD692237C (U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m9E3155FB84015C823606188F53B47CB44C444991 (String_t* ___0_str0, String_t* ___1_str1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* RenderTargetBufferSystem_get_backBuffer_mF7305DBD196865D17A0029AC212E8CC02D8205A2 (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RenderTargetBufferSystem_ReAllocate_m21F112E4C9D22893403D2BDC60ED8D41312AE0D9 (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* ___0_cmd, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* RenderTargetBufferSystem_PeekBackBuffer_m5496A9F37497CE9915D760AD5F44FEA5EE304941 (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* RenderTargetBufferSystem_get_frontBuffer_mDD8E09139E41F59A682216AD6C0A94AE4387E11A (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void RenderTextureDescriptor_set_msaaSamples_m6910E09489372746391B14FBAF59A7237539D6C4_inline (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t RenderTextureDescriptor_get_msaaSamples_mFCC33643AFF2265C8305DCFD79ED8774A1A8FA22_inline (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool RenderingUtils_ReAllocateHandleIfNeeded_mB2BC0F5A65EFBBD73D29B7C5AA081D84FECED9EF (RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B** ___0_handle, RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* ___1_descriptor, int32_t ___2_filterMode, int32_t ___3_wrapMode, int32_t ___4_anisoLevel, float ___5_mipMapBias, String_t* ___6_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B RTHandle_op_Implicit_m2462183372B0496DE475889924EDCAAAD2011B54 (RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* ___0_handle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CommandBuffer_SetGlobalTexture_mD6F1CC7E87FA88B5838D5EDAFBA602EF94FE1F69 (CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* __this, String_t* ___0_name, RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B ___1_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void RenderTextureDescriptor_set_depthStencilFormat_m7EC335006743B59AAD1895AD06B2DD74333F9A03_inline (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RenderTargetBufferSystem_EnableMSAA_mFACEC550EEF2910AC94C1F22C0DA146DBE36F3CA (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, bool ___0_enable, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, float ___0_r, float ___1_g, float ___2_b, float ___3_a, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool ResourceHandle_IsValid_m20B0218FDCA98DCD069AE3BE86FEFCAEDB985B9A_inline (ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool ContextContainer_Contains_mD38FBF0FAC84169E395802CBF880BD0980490AF3_inline (ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* __this, uint32_t ___0_typeId, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57 (RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ___0_handle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B (String_t* ___0_str0, String_t* ___1_str1, String_t* ___2_str2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162 (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* __this, String_t* ___0_message, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Method Definition Index: 35174
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PassData__ctor_mD00F3474656E4B80E46530645D891F4DDEFD0A82 (PassData_tD366FA555BBB5E9EA116F4553DA083826230EB0B* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Method Definition Index: 35175
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__cctor_m611B45C4D881DD8E24435B117B0AF68AE5D119D4 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192* L_0 = (U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192*)il2cpp_codegen_object_new(U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192_il2cpp_TypeInfo_var);
		U3CU3Ec__ctor_mBF1B8DCCD140DBF4D4E6CB55011C43FAA9A8555F(L_0, NULL);
		((U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192_il2cpp_TypeInfo_var))->___U3CU3E9 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192_il2cpp_TypeInfo_var))->___U3CU3E9), (void*)L_0);
		return;
	}
}
// Method Definition Index: 35176
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_mBF1B8DCCD140DBF4D4E6CB55011C43FAA9A8555F (U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// Method Definition Index: 35177
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec_U3CRenderU3Eb__21_0_mFAD5AC6EDAF9A66156C735625BB73F2DD8D197D3 (U3CU3Ec_tECFB6D8664AF684F619F352BF71CA9ABE19AF192* __this, PassData_tD366FA555BBB5E9EA116F4553DA083826230EB0B* ___0_data, RasterGraphContext_tC4D3E53FF7131476487751ACB0237D56C8327147 ___1_context, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GBufferPass_t540C12BCC3AFAC32B775694C8A29B69A49C284E7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/GBufferPass.cs:264>
		RasterGraphContext_tC4D3E53FF7131476487751ACB0237D56C8327147 L_0 = ___1_context;
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_1 = L_0.___cmd;
		PassData_tD366FA555BBB5E9EA116F4553DA083826230EB0B* L_2 = ___0_data;
		PassData_tD366FA555BBB5E9EA116F4553DA083826230EB0B* L_3 = ___0_data;
		NullCheck(L_3);
		RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA L_4 = L_3->___rendererListHdl;
		RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 L_5;
		L_5 = RendererListHandle_op_Implicit_m23F3E49F9D97B0BABE1044E02A7A70784F05C585(L_4, NULL);
		PassData_tD366FA555BBB5E9EA116F4553DA083826230EB0B* L_6 = ___0_data;
		NullCheck(L_6);
		RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA L_7 = L_6->___objectsWithErrorRendererListHdl;
		RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 L_8;
		L_8 = RendererListHandle_op_Implicit_m23F3E49F9D97B0BABE1044E02A7A70784F05C585(L_7, NULL);
		il2cpp_codegen_runtime_class_init_inline(GBufferPass_t540C12BCC3AFAC32B775694C8A29B69A49C284E7_il2cpp_TypeInfo_var);
		GBufferPass_ExecutePass_mB750B5A6C75C1247A55C7C2F2DA20999F5EC2D54(L_1, L_2, L_5, L_8, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/GBufferPass.cs:265>
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Method Definition Index: 35178
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass__ctor_mB767B87419FA7EFB05B3BEBA5507AB59C14A3A51 (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, int32_t ___0_evt, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3BEE5BF39527F408B86D7745DAE7590BE1EEB556);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral788668D2A163F3ADB8F2CBE770488E43D1CA6E2D);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:28>
		ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* L_0 = (ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE*)il2cpp_codegen_object_new(ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE_il2cpp_TypeInfo_var);
		ProfilingSampler__ctor_m26500989FCDB07FA33C9A3BB7F215CBD892F5BB7(L_0, _stringLiteral3BEE5BF39527F408B86D7745DAE7590BE1EEB556, NULL);
		__this->___m_ProfilingSetupSampler = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_ProfilingSetupSampler), (void*)L_0);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:75>
		il2cpp_codegen_runtime_class_init_inline(ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0_il2cpp_TypeInfo_var);
		ScriptableRenderPass__ctor_mE49D4FF8E68A854367A4081E664B8DBA74E6B752(__this, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:77>
		ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* L_1 = (ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE*)il2cpp_codegen_object_new(ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE_il2cpp_TypeInfo_var);
		ProfilingSampler__ctor_m26500989FCDB07FA33C9A3BB7F215CBD892F5BB7(L_1, _stringLiteral788668D2A163F3ADB8F2CBE770488E43D1CA6E2D, NULL);
		ScriptableRenderPass_set_profilingSampler_mFD238B85B68DED586BA8C678141BEEAF229FBF2D(__this, L_1, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:78>
		int32_t L_2 = ___0_evt;
		ScriptableRenderPass_set_renderPassEvent_m63FA581FFDE1C69C2E1358BD0B8DB30275334960_inline(__this, L_2, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:80>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_3 = (PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A*)il2cpp_codegen_object_new(PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A_il2cpp_TypeInfo_var);
		PassData__ctor_m8A566CD8229EC1D10D7A229EC89195F68F862CE9(L_3, NULL);
		__this->___m_PassData = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_PassData), (void*)L_3);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:81>
		Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D* L_4 = (Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D*)(Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D*)SZArrayNew(Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D_il2cpp_TypeInfo_var, (uint32_t)5);
		__this->___m_MainLightShadowMatrices = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_MainLightShadowMatrices), (void*)L_4);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:82>
		ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04* L_5 = (ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04*)(ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04*)SZArrayNew(ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04_il2cpp_TypeInfo_var, (uint32_t)4);
		__this->___m_CascadeSlices = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_CascadeSlices), (void*)L_5);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:83>
		Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_6 = (Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD*)(Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD*)SZArrayNew(Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD_il2cpp_TypeInfo_var, (uint32_t)4);
		__this->___m_CascadeSplitDistances = L_6;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_CascadeSplitDistances), (void*)L_6);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:85>
		__this->___m_EmptyShadowmapNeedsClear = (bool)1;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:86>
		return;
	}
}
// Method Definition Index: 35179
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_Dispose_mF51E4FFE56C00AE46D4FC4CB57A6F229E2C05C04 (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, const RuntimeMethod* method) 
{
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* G_B2_0 = NULL;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* G_B1_0 = NULL;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* G_B5_0 = NULL;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* G_B4_0 = NULL;
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:93>
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_0 = __this->___m_MainLightShadowmapTexture;
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000c;
		}
		G_B1_0 = L_1;
	}
	{
		goto IL_0011;
	}

IL_000c:
	{
		NullCheck(G_B2_0);
		RTHandle_Release_m743C2A22FD95D177D2D425E9DF1F3088161F387B(G_B2_0, NULL);
	}

IL_0011:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:94>
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_2 = __this->___m_EmptyMainLightShadowmapTexture;
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_3 = L_2;
		if (L_3)
		{
			G_B5_0 = L_3;
			goto IL_001c;
		}
		G_B4_0 = L_3;
	}
	{
		return;
	}

IL_001c:
	{
		NullCheck(G_B5_0);
		RTHandle_Release_m743C2A22FD95D177D2D425E9DF1F3088161F387B(G_B5_0, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:95>
		return;
	}
}
// Method Definition Index: 35180
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MainLightShadowCasterPass_Setup_m12C13F721C3A0E61DF8A4DE84681F36968892E4D (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, RenderingData_tAAA01190551D6D5954314E3E1E85B58DD45EED71* ___0_renderingData, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContextContainer_Get_TisUniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7_mE62F7903614384541B770CC0B9A99BD2E608A0F8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContextContainer_Get_TisUniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2_m7D529C251F384985900DEA334BC399BAD36BFC8F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContextContainer_Get_TisUniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6_m71849CEFB9C023EDE026A0F38CE6044274505C06_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContextContainer_Get_TisUniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C_m071A668846CA61588A4D53CCA3CFCD3730530E4E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* V_0 = NULL;
	UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* V_1 = NULL;
	UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* V_2 = NULL;
	UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* V_3 = NULL;
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:105>
		RenderingData_tAAA01190551D6D5954314E3E1E85B58DD45EED71* L_0 = ___0_renderingData;
		ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* L_1 = L_0->___frameData;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:106>
		ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* L_2 = L_1;
		NullCheck(L_2);
		UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* L_3;
		L_3 = ContextContainer_Get_TisUniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6_m71849CEFB9C023EDE026A0F38CE6044274505C06_inline(L_2, ContextContainer_Get_TisUniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6_m71849CEFB9C023EDE026A0F38CE6044274505C06_RuntimeMethod_var);
		V_0 = L_3;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:107>
		ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* L_4 = L_2;
		NullCheck(L_4);
		UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_5;
		L_5 = ContextContainer_Get_TisUniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7_mE62F7903614384541B770CC0B9A99BD2E608A0F8_inline(L_4, ContextContainer_Get_TisUniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7_mE62F7903614384541B770CC0B9A99BD2E608A0F8_RuntimeMethod_var);
		V_1 = L_5;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:108>
		ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* L_6 = L_4;
		NullCheck(L_6);
		UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* L_7;
		L_7 = ContextContainer_Get_TisUniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2_m7D529C251F384985900DEA334BC399BAD36BFC8F_inline(L_6, ContextContainer_Get_TisUniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2_m7D529C251F384985900DEA334BC399BAD36BFC8F_RuntimeMethod_var);
		V_2 = L_7;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:109>
		NullCheck(L_6);
		UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_8;
		L_8 = ContextContainer_Get_TisUniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C_m071A668846CA61588A4D53CCA3CFCD3730530E4E_inline(L_6, ContextContainer_Get_TisUniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C_m071A668846CA61588A4D53CCA3CFCD3730530E4E_RuntimeMethod_var);
		V_3 = L_8;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:110>
		UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* L_9 = V_0;
		UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_10 = V_1;
		UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* L_11 = V_2;
		UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_12 = V_3;
		bool L_13;
		L_13 = MainLightShadowCasterPass_Setup_mBE7DBA64DB246FF091229EC1B945D516D2868CDC(__this, L_9, L_10, L_11, L_12, NULL);
		return L_13;
	}
}
// Method Definition Index: 35181
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MainLightShadowCasterPass_Setup_mBE7DBA64DB246FF091229EC1B945D516D2868CDC (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* ___0_renderingData, UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* ___1_cameraData, UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* ___2_lightData, UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* ___3_shadowData, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArrayExtensions_UnsafeElementAt_TisShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20_m172973527DE9C444ABA117A4CE8B1D951C896487_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArrayExtensions_UnsafeElementAt_TisURPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9_m56159FAB7E5AD01E526C4937CB1FAFF4F8F52A7D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ShadowSplitData_tC276A96F461DD73CFF6D94DB557D42A1643640DF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4B2BF663FAFE4EA038675F6C2D1083425BC2DD57);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	ProfilingScope_t57898BA31E8EF8F083EF84E0DA2737AC61CBC5BD V_2;
	memset((&V_2), 0, sizeof(V_2));
	bool V_3 = false;
	int32_t V_4 = 0;
	VisibleLight_t0A4DF5B22865A00F618A0352B805277FA0132805 V_5;
	memset((&V_5), 0, sizeof(V_5));
	Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* V_6 = NULL;
	URPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9* V_7 = NULL;
	bool V_8 = false;
	Bounds_t367E830C64BBF235ED8C3B2F8CF6254FDCAD39C3 V_9;
	memset((&V_9), 0, sizeof(V_9));
	int32_t V_10 = 0;
	ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20* V_11 = NULL;
	ShadowSplitData_tC276A96F461DD73CFF6D94DB557D42A1643640DF V_12;
	memset((&V_12), 0, sizeof(V_12));
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:124>
		UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_0 = ___3_shadowData;
		NullCheck(L_0);
		bool L_1 = L_0->___mainLightShadowsEnabled;
		V_0 = L_1;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:125>
		UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_2 = ___3_shadowData;
		NullCheck(L_2);
		bool L_3 = L_2->___supportsMainLightShadows;
		V_1 = L_3;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:132>
		ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* L_4 = __this->___m_ProfilingSetupSampler;
		ProfilingScope__ctor_m4B73587A2295443A73B64DDD3D484D8EAECC0D65((&V_2), L_4, NULL);
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_0216:
			{
				ProfilingScope_Dispose_m4231A2ACA1F8E345BB0078310A9F7601704C8BE4((&V_2), NULL);
				return;
			}
		});
		try
		{
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:134>
				UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_5 = ___1_cameraData;
				NullCheck(L_5);
				ScriptableRenderer_tF15B95BB85F26BE4B4719901D909831B89DC8892* L_6 = L_5->___renderer;
				NullCheck(L_6);
				bool L_7;
				L_7 = ScriptableRenderer_get_stripShadowsOffVariants_mEC78AA6E4F4353DEF4DA00EB6E2BF7A55CEE322F_inline(L_6, NULL);
				V_3 = L_7;
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:136>
				MainLightShadowCasterPass_Clear_m87F3BD44E2481FBD03E0A7F66E4EAC6363BD9728(__this, NULL);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:137>
				UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* L_8 = ___2_lightData;
				NullCheck(L_8);
				int32_t L_9 = L_8->___mainLightIndex;
				V_4 = L_9;
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:138>
				int32_t L_10 = V_4;
				if ((!(((uint32_t)L_10) == ((uint32_t)(-1)))))
				{
					goto IL_005a_1;
				}
			}
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:140>
				bool L_11 = V_0;
				if (!L_11)
				{
					goto IL_0052_1;
				}
			}
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:141>
				bool L_12 = V_3;
				bool L_13 = V_0;
				UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_14 = ___1_cameraData;
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_15 = ___3_shadowData;
				bool L_16;
				L_16 = MainLightShadowCasterPass_SetupForEmptyRendering_m18DFF100A5175846F92FC5BA4E4DEDCEBCE227C4(__this, L_12, L_13, (Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3*)NULL, L_14, L_15, NULL);
				V_8 = L_16;
				goto IL_0224;
			}

IL_0052_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:143>
				V_8 = (bool)0;
				goto IL_0224;
			}

IL_005a_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:146>
				UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* L_17 = ___2_lightData;
				NullCheck(L_17);
				NativeArray_1_t71485A1E60B31CCAD3E525C907CF172E8B804468* L_18 = (NativeArray_1_t71485A1E60B31CCAD3E525C907CF172E8B804468*)(&L_17->___visibleLights);
				int32_t L_19 = V_4;
				VisibleLight_t0A4DF5B22865A00F618A0352B805277FA0132805 L_20;
				L_20 = IL2CPP_NATIVEARRAY_GET_ITEM(VisibleLight_t0A4DF5B22865A00F618A0352B805277FA0132805, (L_18)->___m_Buffer, L_19);
				V_5 = L_20;
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:147>
				Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_21;
				L_21 = VisibleLight_get_light_mD179E0BF18C77DBE2FD85FE9687F63A8C1859E6B((&V_5), NULL);
				V_6 = L_21;
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:148>
				bool L_22 = V_1;
				if (!L_22)
				{
					goto IL_0092_1;
				}
			}
			{
				Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_23 = V_6;
				NullCheck(L_23);
				int32_t L_24;
				L_24 = Light_get_shadows_m1A11721F202C27838A7A8ED72455E6A727CEE6C5(L_23, NULL);
				if (L_24)
				{
					goto IL_0092_1;
				}
			}
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:149>
				bool L_25 = V_3;
				bool L_26 = V_0;
				Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_27 = V_6;
				UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_28 = ___1_cameraData;
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_29 = ___3_shadowData;
				bool L_30;
				L_30 = MainLightShadowCasterPass_SetupForEmptyRendering_m18DFF100A5175846F92FC5BA4E4DEDCEBCE227C4(__this, L_25, L_26, L_27, L_28, L_29, NULL);
				V_8 = L_30;
				goto IL_0224;
			}

IL_0092_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:151>
				bool L_31 = V_0;
				if (L_31)
				{
					goto IL_00e5_1;
				}
			}
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:155>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:156>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:157>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:158>
				Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_32 = V_6;
				NullCheck(L_32);
				int32_t L_33;
				L_33 = Light_get_shadows_m1A11721F202C27838A7A8ED72455E6A727CEE6C5(L_32, NULL);
				if (!L_33)
				{
					goto IL_00dd_1;
				}
			}
			{
				Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_34 = V_6;
				NullCheck(L_34);
				LightBakingOutput_t6212AB0B6B34C94F1982FE964FC48201854B5B90 L_35;
				L_35 = Light_get_bakingOutput_mF383DB97CFD32D65DA468329E18DD2DD61521CED(L_34, NULL);
				bool L_36 = L_35.___isBaked;
				if (!L_36)
				{
					goto IL_00dd_1;
				}
			}
			{
				Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_37 = V_6;
				NullCheck(L_37);
				LightBakingOutput_t6212AB0B6B34C94F1982FE964FC48201854B5B90 L_38;
				L_38 = Light_get_bakingOutput_mF383DB97CFD32D65DA468329E18DD2DD61521CED(L_37, NULL);
				int32_t L_39 = L_38.___mixedLightingMode;
				if (!L_39)
				{
					goto IL_00dd_1;
				}
			}
			{
				Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_40 = V_6;
				NullCheck(L_40);
				LightBakingOutput_t6212AB0B6B34C94F1982FE964FC48201854B5B90 L_41;
				L_41 = Light_get_bakingOutput_mF383DB97CFD32D65DA468329E18DD2DD61521CED(L_40, NULL);
				int32_t L_42 = L_41.___lightmapBakeType;
				if ((!(((uint32_t)L_42) == ((uint32_t)1))))
				{
					goto IL_00dd_1;
				}
			}
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:160>
				bool L_43 = V_3;
				bool L_44 = V_0;
				Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_45 = V_6;
				UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_46 = ___1_cameraData;
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_47 = ___3_shadowData;
				bool L_48;
				L_48 = MainLightShadowCasterPass_SetupForEmptyRendering_m18DFF100A5175846F92FC5BA4E4DEDCEBCE227C4(__this, L_43, L_44, L_45, L_46, L_47, NULL);
				V_8 = L_48;
				goto IL_0224;
			}

IL_00dd_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:163>
				V_8 = (bool)0;
				goto IL_0224;
			}

IL_00e5_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:166>
				bool L_49 = V_1;
				if (L_49)
				{
					goto IL_00fb_1;
				}
			}
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:167>
				bool L_50 = V_3;
				bool L_51 = V_0;
				UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_52 = ___1_cameraData;
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_53 = ___3_shadowData;
				bool L_54;
				L_54 = MainLightShadowCasterPass_SetupForEmptyRendering_m18DFF100A5175846F92FC5BA4E4DEDCEBCE227C4(__this, L_50, L_51, (Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3*)NULL, L_52, L_53, NULL);
				V_8 = L_54;
				goto IL_0224;
			}

IL_00fb_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:169>
				int32_t L_55;
				L_55 = VisibleLight_get_lightType_mFFCEBE6E368853F13E7CDBA910F6D9B689292454((&V_5), NULL);
				if ((((int32_t)L_55) == ((int32_t)1)))
				{
					goto IL_010f_1;
				}
			}
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:171>
				il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
				Debug_LogWarning_m33EF1B897E0C7C6FF538989610BFAFFEF4628CA9(_stringLiteral4B2BF663FAFE4EA038675F6C2D1083425BC2DD57, NULL);
			}

IL_010f_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:174>
				UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* L_56 = ___0_renderingData;
				NullCheck(L_56);
				CullingResults_tD6B7EF20B68D47DFF3A99EB2EA73F47F1D460267* L_57 = (CullingResults_tD6B7EF20B68D47DFF3A99EB2EA73F47F1D460267*)(&L_56->___cullResults);
				int32_t L_58 = V_4;
				bool L_59;
				L_59 = CullingResults_GetShadowCasterBounds_m5DD3647DB1560ECCF6620DD7DE16D6304012CF0B(L_57, L_58, (&V_9), NULL);
				if (L_59)
				{
					goto IL_0134_1;
				}
			}
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:175>
				bool L_60 = V_3;
				bool L_61 = V_0;
				Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_62 = V_6;
				UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_63 = ___1_cameraData;
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_64 = ___3_shadowData;
				bool L_65;
				L_65 = MainLightShadowCasterPass_SetupForEmptyRendering_m18DFF100A5175846F92FC5BA4E4DEDCEBCE227C4(__this, L_60, L_61, L_62, L_63, L_64, NULL);
				V_8 = L_65;
				goto IL_0224;
			}

IL_0134_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:177>
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_66 = ___3_shadowData;
				NullCheck(L_66);
				int32_t L_67 = L_66->___mainLightShadowCascadesCount;
				__this->___m_ShadowCasterCascadesCount = L_67;
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:178>
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_68 = ___3_shadowData;
				NullCheck(L_68);
				int32_t L_69 = L_68->___mainLightRenderTargetWidth;
				__this->___renderTargetWidth = L_69;
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:179>
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_70 = ___3_shadowData;
				NullCheck(L_70);
				int32_t L_71 = L_70->___mainLightRenderTargetHeight;
				__this->___renderTargetHeight = L_71;
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:181>
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_72 = ___3_shadowData;
				NullCheck(L_72);
				NativeArray_1_t4C11F337CF2A7773644650D071AA5F21F158A5E0 L_73 = L_72->___visibleLightsShadowCullingInfos;
				int32_t L_74 = V_4;
				URPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9* L_75;
				L_75 = NativeArrayExtensions_UnsafeElementAt_TisURPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9_m56159FAB7E5AD01E526C4937CB1FAFF4F8F52A7D(L_73, L_74, NativeArrayExtensions_UnsafeElementAt_TisURPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9_m56159FAB7E5AD01E526C4937CB1FAFF4F8F52A7D_RuntimeMethod_var);
				V_7 = L_75;
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:183>
				V_10 = 0;
				goto IL_01d3_1;
			}

IL_0170_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:185>
				URPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9* L_76 = V_7;
				NativeArray_1_t9B7A94FA050F43A3996B812B9164E7885F38ADC3 L_77 = L_76->___slices;
				int32_t L_78 = V_10;
				ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20* L_79;
				L_79 = NativeArrayExtensions_UnsafeElementAt_TisShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20_m172973527DE9C444ABA117A4CE8B1D951C896487(L_77, L_78, NativeArrayExtensions_UnsafeElementAt_TisShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20_m172973527DE9C444ABA117A4CE8B1D951C896487_RuntimeMethod_var);
				V_11 = L_79;
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:186>
				Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_80 = __this->___m_CascadeSplitDistances;
				int32_t L_81 = V_10;
				ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20* L_82 = V_11;
				ShadowSplitData_tC276A96F461DD73CFF6D94DB557D42A1643640DF L_83 = L_82->___splitData;
				V_12 = L_83;
				il2cpp_codegen_runtime_class_init_inline(ShadowSplitData_tC276A96F461DD73CFF6D94DB557D42A1643640DF_il2cpp_TypeInfo_var);
				Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_84;
				L_84 = ShadowSplitData_get_cullingSphere_mEABEC468FE12ADDB09239EABD3FB59551E4A44E0((&V_12), NULL);
				NullCheck(L_80);
				(L_80)->SetAt(static_cast<il2cpp_array_size_t>(L_81), (Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3)L_84);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:187>
				ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04* L_85 = __this->___m_CascadeSlices;
				int32_t L_86 = V_10;
				ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20* L_87 = V_11;
				ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20 L_88 = (*(ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20*)L_87);
				NullCheck(L_85);
				(L_85)->SetAt(static_cast<il2cpp_array_size_t>(L_86), (ShadowSliceData_t1BCFEDC63BECA994949FE1F4245CEE930EE69E20)L_88);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:189>
				URPLightShadowCullingInfos_t8EBC5966B6C0C703C739850EA3B585324022F0E9* L_89 = V_7;
				int32_t L_90 = V_10;
				bool L_91;
				L_91 = URPLightShadowCullingInfos_IsSliceValid_m7A4FD76F47B4EA4E62951751ED4FD63E69535BD8(L_89, L_90, NULL);
				if (L_91)
				{
					goto IL_01cd_1;
				}
			}
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:190>
				bool L_92 = V_3;
				bool L_93 = V_0;
				Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_94 = V_6;
				UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_95 = ___1_cameraData;
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_96 = ___3_shadowData;
				bool L_97;
				L_97 = MainLightShadowCasterPass_SetupForEmptyRendering_m18DFF100A5175846F92FC5BA4E4DEDCEBCE227C4(__this, L_92, L_93, L_94, L_95, L_96, NULL);
				V_8 = L_97;
				goto IL_0224;
			}

IL_01cd_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:183>
				int32_t L_98 = V_10;
				V_10 = ((int32_t)il2cpp_codegen_add(L_98, 1));
			}

IL_01d3_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:183>
				int32_t L_99 = V_10;
				int32_t L_100 = __this->___m_ShadowCasterCascadesCount;
				if ((((int32_t)L_99) < ((int32_t)L_100)))
				{
					goto IL_0170_1;
				}
			}
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:193>
				MainLightShadowCasterPass_UpdateTextureDescriptorIfNeeded_mAA75D25F976BC7984D895956B30417CD707CF95B(__this, NULL);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:195>
				UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_101 = ___1_cameraData;
				NullCheck(L_101);
				float L_102 = L_101->___maxShadowDistance;
				UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_103 = ___1_cameraData;
				NullCheck(L_103);
				float L_104 = L_103->___maxShadowDistance;
				__this->___m_MaxShadowDistanceSq = ((float)il2cpp_codegen_multiply(L_102, L_104));
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:196>
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_105 = ___3_shadowData;
				NullCheck(L_105);
				float L_106 = L_105->___mainLightShadowCascadeBorder;
				__this->___m_CascadeBorder = L_106;
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:197>
				__this->___m_CreateEmptyShadowmap = (bool)0;
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:198>
				ScriptableRenderPass_set_useNativeRenderPass_m1D60C30BB1CF1B4D383FFCABC1F57EA755626895_inline(__this, (bool)1, NULL);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:200>
				V_8 = (bool)1;
				goto IL_0224;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_0224:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:201>
		bool L_107 = V_8;
		return L_107;
	}
}
// Method Definition Index: 35182
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_UpdateTextureDescriptorIfNeeded_mAA75D25F976BC7984D895956B30417CD707CF95B (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, const RuntimeMethod* method) 
{
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:205>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:206>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:207>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:208>
		RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* L_0 = (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46*)(&__this->___m_MainLightShadowDescriptor);
		int32_t L_1;
		L_1 = RenderTextureDescriptor_get_width_mB159E4EB08B23B19CCCFADB465864361FB840BFF_inline(L_0, NULL);
		int32_t L_2 = __this->___renderTargetWidth;
		if ((!(((uint32_t)L_1) == ((uint32_t)L_2))))
		{
			goto IL_0043;
		}
	}
	{
		RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* L_3 = (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46*)(&__this->___m_MainLightShadowDescriptor);
		int32_t L_4;
		L_4 = RenderTextureDescriptor_get_height_m1006F9AA45029715C552C8A8C2F102F63D3A91EC_inline(L_3, NULL);
		int32_t L_5 = __this->___renderTargetHeight;
		if ((!(((uint32_t)L_4) == ((uint32_t)L_5))))
		{
			goto IL_0043;
		}
	}
	{
		RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* L_6 = (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46*)(&__this->___m_MainLightShadowDescriptor);
		int32_t L_7;
		L_7 = RenderTextureDescriptor_get_depthBufferBits_mC095E36F9803B2E68E258C03E48ACD0B0E678953(L_6, NULL);
		if ((!(((uint32_t)L_7) == ((uint32_t)((int32_t)16)))))
		{
			goto IL_0043;
		}
	}
	{
		RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* L_8 = (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46*)(&__this->___m_MainLightShadowDescriptor);
		int32_t L_9;
		L_9 = RenderTextureDescriptor_get_colorFormat_mF87FD5E3AC4688BBB921568003ED4A1FFB1614FF(L_8, NULL);
		if ((((int32_t)L_9) == ((int32_t)3)))
		{
			goto IL_005d;
		}
	}

IL_0043:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:210>
		int32_t L_10 = __this->___renderTargetWidth;
		int32_t L_11 = __this->___renderTargetHeight;
		RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46 L_12;
		memset((&L_12), 0, sizeof(L_12));
		RenderTextureDescriptor__ctor_mE27A3C225736C1F806C12A7C31C0DC66A0AFE61B((&L_12), L_10, L_11, 3, ((int32_t)16), NULL);
		__this->___m_MainLightShadowDescriptor = L_12;
	}

IL_005d:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:212>
		return;
	}
}
// Method Definition Index: 35183
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MainLightShadowCasterPass_SetupForEmptyRendering_m18DFF100A5175846F92FC5BA4E4DEDCEBCE227C4 (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, bool ___0_stripShadowsOffVariants, bool ___1_shadowsEnabled, Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* ___2_light, UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* ___3_cameraData, UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* ___4_shadowData, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	float V_1 = 0.0f;
	bool V_2 = false;
	float V_3 = 0.0f;
	float V_4 = 0.0f;
	float V_5 = 0.0f;
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:216>
		bool L_0 = ___0_stripShadowsOffVariants;
		if (L_0)
		{
			goto IL_0005;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:217>
		return (bool)0;
	}

IL_0005:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:219>
		__this->___m_CreateEmptyShadowmap = (bool)1;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:220>
		ScriptableRenderPass_set_useNativeRenderPass_m1D60C30BB1CF1B4D383FFCABC1F57EA755626895_inline(__this, (bool)0, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:222>
		bool L_1 = ___1_shadowsEnabled;
		__this->___m_SetKeywordForEmptyShadowmap = L_1;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:227>
		Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_2 = ___2_light;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_3;
		L_3 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_2, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_3)
		{
			goto IL_0043;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:229>
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_4;
		memset((&L_4), 0, sizeof(L_4));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_4), (0.0f), (0.0f), (1.0f), (0.0f), NULL);
		il2cpp_codegen_runtime_class_init_inline(MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var);
		((MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var))->___s_EmptyShadowParams = L_4;
		goto IL_008d;
	}

IL_0043:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:233>
		UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_5 = ___4_shadowData;
		NullCheck(L_5);
		bool L_6 = L_5->___supportsSoftShadows;
		V_0 = L_6;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:234>
		UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_7 = ___3_cameraData;
		NullCheck(L_7);
		float L_8 = L_7->___maxShadowDistance;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:235>
		UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_9 = ___4_shadowData;
		NullCheck(L_9);
		float L_10 = L_9->___mainLightShadowCascadeBorder;
		V_1 = L_10;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:237>
		Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_11 = ___2_light;
		NullCheck(L_11);
		int32_t L_12;
		L_12 = Light_get_shadows_m1A11721F202C27838A7A8ED72455E6A727CEE6C5(L_11, NULL);
		bool L_13 = V_0;
		V_2 = (bool)((int32_t)(((((int32_t)L_12) == ((int32_t)2))? 1 : 0)&(int32_t)L_13));
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:238>
		Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_14 = ___2_light;
		bool L_15 = V_2;
		il2cpp_codegen_runtime_class_init_inline(ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var);
		float L_16;
		L_16 = ShadowUtils_SoftShadowQualityToShaderProperty_m02B6A27D17A4C26FA5E622F580113664A6ED3BE8(L_14, L_15, NULL);
		V_3 = L_16;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:239>
		float L_17 = V_1;
		ShadowUtils_GetScaleAndBiasForLinearDistanceFade_mE07E0F336969447E89E448D23AF050BF1646B20F(L_8, L_17, (&V_4), (&V_5), NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:240>
		Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_18 = ___2_light;
		NullCheck(L_18);
		float L_19;
		L_19 = Light_get_shadowStrength_m4AB6E78F7A28A97C61EDBD06ECEAF8A565688FC8(L_18, NULL);
		float L_20 = V_3;
		float L_21 = V_4;
		float L_22 = V_5;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_23;
		memset((&L_23), 0, sizeof(L_23));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_23), L_19, L_20, L_21, L_22, NULL);
		il2cpp_codegen_runtime_class_init_inline(MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var);
		((MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var))->___s_EmptyShadowParams = L_23;
	}

IL_008d:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:243>
		return (bool)1;
	}
}
// Method Definition Index: 35184
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_Configure_m337450CB4510A58708D9F85A348CD76EAAA12599 (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* ___0_cmd, RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46 ___1_cameraTextureDescriptor, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5FCFE8BB42D2AA29FAADC098234D0BEF8F39BE29);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC56629E7BE3CBCE4ECF0CAF12E998C56278552E6);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:253>
		bool L_0 = __this->___m_CreateEmptyShadowmap;
		if (!L_0)
		{
			goto IL_0049;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:256>
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B** L_1 = (RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B**)(&__this->___m_EmptyMainLightShadowmapTexture);
		il2cpp_codegen_runtime_class_init_inline(ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = ShadowUtils_ShadowRTReAllocateIfNeeded_m1D2B971FC205B86AA3CCB3396FD0DDCC07B2198B(L_1, 1, 1, ((int32_t)16), 1, (0.0f), _stringLiteralC56629E7BE3CBCE4ECF0CAF12E998C56278552E6, NULL);
		if (!L_2)
		{
			goto IL_002b;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:257>
		__this->___m_EmptyShadowmapNeedsClear = (bool)1;
	}

IL_002b:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:259>
		bool L_3 = __this->___m_EmptyShadowmapNeedsClear;
		if (L_3)
		{
			goto IL_0034;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:260>
		return;
	}

IL_0034:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:262>
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_4 = __this->___m_EmptyMainLightShadowmapTexture;
		ScriptableRenderPass_ConfigureTarget_m6767C5E94D51F989348F415771B770DE844FD4A6(__this, L_4, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:263>
		__this->___m_EmptyShadowmapNeedsClear = (bool)0;
		goto IL_007a;
	}

IL_0049:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:267>
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B** L_5 = (RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B**)(&__this->___m_MainLightShadowmapTexture);
		int32_t L_6 = __this->___renderTargetWidth;
		int32_t L_7 = __this->___renderTargetHeight;
		il2cpp_codegen_runtime_class_init_inline(ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var);
		bool L_8;
		L_8 = ShadowUtils_ShadowRTReAllocateIfNeeded_m1D2B971FC205B86AA3CCB3396FD0DDCC07B2198B(L_5, L_6, L_7, ((int32_t)16), 1, (0.0f), _stringLiteral5FCFE8BB42D2AA29FAADC098234D0BEF8F39BE29, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:268>
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_9 = __this->___m_MainLightShadowmapTexture;
		ScriptableRenderPass_ConfigureTarget_m6767C5E94D51F989348F415771B770DE844FD4A6(__this, L_9, NULL);
	}

IL_007a:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:271>
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_10;
		L_10 = Color_get_black_mB50217951591A045844C61E7FF31EEE3FEF16737_inline(NULL);
		ScriptableRenderPass_ConfigureClear_m5C82128C3ABDD63621501DC012ED91F392ABF123(__this, 7, L_10, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:274>
		return;
	}
}
// Method Definition Index: 35185
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_Execute_m5D1467DC2C85A973DC573C7BD1DF6D37AB08ACEF (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 ___0_context, RenderingData_tAAA01190551D6D5954314E3E1E85B58DD45EED71* ___1_renderingData, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CommandBufferHelpers_tB8C09D01D6F0369DF52DEA955F017D6001BC72E5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContextContainer_Get_TisUniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7_mE62F7903614384541B770CC0B9A99BD2E608A0F8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContextContainer_Get_TisUniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2_m7D529C251F384985900DEA334BC399BAD36BFC8F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContextContainer_Get_TisUniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6_m71849CEFB9C023EDE026A0F38CE6044274505C06_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContextContainer_Get_TisUniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C_m071A668846CA61588A4D53CCA3CFCD3730530E4E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* V_0 = NULL;
	UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* V_1 = NULL;
	UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* V_2 = NULL;
	UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* V_3 = NULL;
	RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* V_4 = NULL;
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:280>
		RenderingData_tAAA01190551D6D5954314E3E1E85B58DD45EED71* L_0 = ___1_renderingData;
		ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* L_1 = L_0->___frameData;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:281>
		ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* L_2 = L_1;
		NullCheck(L_2);
		UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* L_3;
		L_3 = ContextContainer_Get_TisUniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6_m71849CEFB9C023EDE026A0F38CE6044274505C06_inline(L_2, ContextContainer_Get_TisUniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6_m71849CEFB9C023EDE026A0F38CE6044274505C06_RuntimeMethod_var);
		V_0 = L_3;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:282>
		ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* L_4 = L_2;
		NullCheck(L_4);
		UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_5;
		L_5 = ContextContainer_Get_TisUniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7_mE62F7903614384541B770CC0B9A99BD2E608A0F8_inline(L_4, ContextContainer_Get_TisUniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7_mE62F7903614384541B770CC0B9A99BD2E608A0F8_RuntimeMethod_var);
		V_1 = L_5;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:283>
		ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* L_6 = L_4;
		NullCheck(L_6);
		UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* L_7;
		L_7 = ContextContainer_Get_TisUniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2_m7D529C251F384985900DEA334BC399BAD36BFC8F_inline(L_6, ContextContainer_Get_TisUniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2_m7D529C251F384985900DEA334BC399BAD36BFC8F_RuntimeMethod_var);
		V_2 = L_7;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:284>
		NullCheck(L_6);
		UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_8;
		L_8 = ContextContainer_Get_TisUniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C_m071A668846CA61588A4D53CCA3CFCD3730530E4E_inline(L_6, ContextContainer_Get_TisUniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C_m071A668846CA61588A4D53CCA3CFCD3730530E4E_RuntimeMethod_var);
		V_3 = L_8;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:286>
		RenderingData_tAAA01190551D6D5954314E3E1E85B58DD45EED71* L_9 = ___1_renderingData;
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7** L_10;
		L_10 = RenderingData_get_commandBuffer_m747CD6ABF19DD5BB05F8231CC84A9922D9DC080A(L_9, NULL);
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_11 = *((CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7**)L_10);
		il2cpp_codegen_runtime_class_init_inline(CommandBufferHelpers_tB8C09D01D6F0369DF52DEA955F017D6001BC72E5_il2cpp_TypeInfo_var);
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_12;
		L_12 = CommandBufferHelpers_GetRasterCommandBuffer_m6086D650343F166614B3FB5ED89D63DE8F85C42B_inline(L_11, NULL);
		V_4 = L_12;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:287>
		bool L_13 = __this->___m_CreateEmptyShadowmap;
		if (!L_13)
		{
			goto IL_006e;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:289>
		bool L_14 = __this->___m_SetKeywordForEmptyShadowmap;
		if (!L_14)
		{
			goto IL_004b;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:290>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_15 = V_4;
		NullCheck(L_15);
		RasterCommandBuffer_EnableKeyword_m554A685119A5DE8DAD67ADA432176DCEFEDA3494(L_15, (&((ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91_StaticFields*)il2cpp_codegen_static_fields_for(ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91_il2cpp_TypeInfo_var))->___MainLightShadows), NULL);
	}

IL_004b:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:291>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_16 = V_4;
		il2cpp_codegen_runtime_class_init_inline(MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var);
		MainLightShadowCasterPass_SetShadowParamsForEmptyShadowmap_mCBE8758960CEB5A2DF6953350A969A492312810E(L_16, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:292>
		UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* L_17 = V_0;
		NullCheck(L_17);
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_18;
		L_18 = UniversalRenderingData_get_commandBuffer_m8397484CEAB1A0D725DEA8A85C9B955E2B4007F8(L_17, NULL);
		il2cpp_codegen_runtime_class_init_inline(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var);
		int32_t L_19 = ((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____MainLightShadowmapID;
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_20 = __this->___m_EmptyMainLightShadowmapTexture;
		NullCheck(L_20);
		RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B L_21;
		L_21 = RTHandle_get_nameID_m30AF2567853494DB845D83A8B37D0FB523DA76E9_inline(L_20, NULL);
		NullCheck(L_18);
		CommandBuffer_SetGlobalTexture_m65E012CB3C35EA43533CB4FF4C6F6498FDE229CD(L_18, L_19, L_21, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:293>
		return;
	}

IL_006e:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:296>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_22 = (PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)(&__this->___m_PassData);
		UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* L_23 = V_0;
		UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_24 = V_1;
		UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* L_25 = V_2;
		UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_26 = V_3;
		MainLightShadowCasterPass_InitPassData_m53ED19C2B72CB997374CEC86E590E380BD9DEF6E(__this, L_22, L_23, L_24, L_25, L_26, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:297>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_27 = (PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)(&__this->___m_PassData);
		ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 L_28 = ___0_context;
		MainLightShadowCasterPass_InitRendererLists_mF29AB3ADDD25931ABEC828A4A71189E87F3E9A93(__this, L_27, L_28, (RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E*)NULL, (bool)0, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:299>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_29 = V_4;
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_30 = (PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)(&__this->___m_PassData);
		MainLightShadowCasterPass_RenderMainLightCascadeShadowmap_m7F529AA6F3582E375793023C425D366AA04F0547(__this, L_29, L_30, (bool)0, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:300>
		UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* L_31 = V_0;
		NullCheck(L_31);
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_32;
		L_32 = UniversalRenderingData_get_commandBuffer_m8397484CEAB1A0D725DEA8A85C9B955E2B4007F8(L_31, NULL);
		il2cpp_codegen_runtime_class_init_inline(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var);
		int32_t L_33 = ((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____MainLightShadowmapID;
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_34 = __this->___m_MainLightShadowmapTexture;
		NullCheck(L_34);
		RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B L_35;
		L_35 = RTHandle_get_nameID_m30AF2567853494DB845D83A8B37D0FB523DA76E9_inline(L_34, NULL);
		NullCheck(L_32);
		CommandBuffer_SetGlobalTexture_m65E012CB3C35EA43533CB4FF4C6F6498FDE229CD(L_32, L_33, L_35, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:301>
		return;
	}
}
// Method Definition Index: 35186
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_Clear_m87F3BD44E2481FBD03E0A7F66E4EAC6363BD9728 (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:305>
		V_0 = 0;
		goto IL_0019;
	}

IL_0004:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:306>
		Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D* L_0 = __this->___m_MainLightShadowMatrices;
		int32_t L_1 = V_0;
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_2;
		L_2 = Matrix4x4_get_identity_m6568A73831F3E2D587420D20FF423959D7D8AB56_inline(NULL);
		NullCheck(L_0);
		(L_0)->SetAt(static_cast<il2cpp_array_size_t>(L_1), (Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6)L_2);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:305>
		int32_t L_3 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_3, 1));
	}

IL_0019:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:305>
		int32_t L_4 = V_0;
		Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D* L_5 = __this->___m_MainLightShadowMatrices;
		NullCheck(L_5);
		if ((((int32_t)L_4) < ((int32_t)((int32_t)(((RuntimeArray*)L_5)->max_length)))))
		{
			goto IL_0004;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:308>
		V_1 = 0;
		goto IL_0051;
	}

IL_0028:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:309>
		Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_6 = __this->___m_CascadeSplitDistances;
		int32_t L_7 = V_1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_8;
		memset((&L_8), 0, sizeof(L_8));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_8), (0.0f), (0.0f), (0.0f), (0.0f), NULL);
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3)L_8);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:308>
		int32_t L_9 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_9, 1));
	}

IL_0051:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:308>
		int32_t L_10 = V_1;
		Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_11 = __this->___m_CascadeSplitDistances;
		NullCheck(L_11);
		if ((((int32_t)L_10) < ((int32_t)((int32_t)(((RuntimeArray*)L_11)->max_length)))))
		{
			goto IL_0028;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:311>
		V_2 = 0;
		goto IL_0075;
	}

IL_0060:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:312>
		ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04* L_12 = __this->___m_CascadeSlices;
		int32_t L_13 = V_2;
		NullCheck(L_12);
		ShadowSliceData_Clear_mB5BFA7D8B81B48BD2CCF60B127DC0AFBAD9CC6BC(((L_12)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_13))), NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:311>
		int32_t L_14 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_14, 1));
	}

IL_0075:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:311>
		int32_t L_15 = V_2;
		ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04* L_16 = __this->___m_CascadeSlices;
		NullCheck(L_16);
		if ((((int32_t)L_15) < ((int32_t)((int32_t)(((RuntimeArray*)L_16)->max_length)))))
		{
			goto IL_0060;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:313>
		return;
	}
}
// Method Definition Index: 35187
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_SetShadowParamsForEmptyShadowmap_mCBE8758960CEB5A2DF6953350A969A492312810E (RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___0_rasterCommandBuffer, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:317>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_0 = ___0_rasterCommandBuffer;
		il2cpp_codegen_runtime_class_init_inline(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var);
		int32_t L_1 = ((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____ShadowmapSize;
		il2cpp_codegen_runtime_class_init_inline(MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var);
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_2 = ((MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var))->___s_EmptyShadowmapSize;
		NullCheck(L_0);
		RasterCommandBuffer_SetGlobalVector_mB54A698709E920A961D8601DC5B2452B4866C68E(L_0, L_1, L_2, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:318>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_3 = ___0_rasterCommandBuffer;
		int32_t L_4 = ((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____ShadowParams;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_5 = ((MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var))->___s_EmptyShadowParams;
		NullCheck(L_3);
		RasterCommandBuffer_SetGlobalVector_mB54A698709E920A961D8601DC5B2452B4866C68E(L_3, L_4, L_5, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:319>
		return;
	}
}
// Method Definition Index: 35188
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_RenderMainLightCascadeShadowmap_m7F529AA6F3582E375793023C425D366AA04F0547 (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___0_cmd, PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** ___1_data, bool ___2_isRenderGraph, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ProfilingSampler_Get_TisURPProfileId_tA83520239B6C0F10A73CCC6CEC7D3DA1F1932481_m9F3104BDEBD70A287E8F4BAC86579564A19A5661_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* V_0 = NULL;
	int32_t V_1 = 0;
	VisibleLight_t0A4DF5B22865A00F618A0352B805277FA0132805 V_2;
	memset((&V_2), 0, sizeof(V_2));
	ProfilingScope_t57898BA31E8EF8F083EF84E0DA2737AC61CBC5BD V_3;
	memset((&V_3), 0, sizeof(V_3));
	int32_t V_4 = 0;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_5;
	memset((&V_5), 0, sizeof(V_5));
	RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 V_6;
	memset((&V_6), 0, sizeof(V_6));
	RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 G_B9_0;
	memset((&G_B9_0), 0, sizeof(G_B9_0));
	UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* G_B13_0 = NULL;
	UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* G_B12_0 = NULL;
	int32_t G_B14_0 = 0;
	UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* G_B14_1 = NULL;
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:323>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_0 = ___1_data;
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_1 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_0);
		NullCheck(L_1);
		UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* L_2 = L_1->___lightData;
		V_0 = L_2;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:325>
		UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* L_3 = V_0;
		NullCheck(L_3);
		int32_t L_4 = L_3->___mainLightIndex;
		V_1 = L_4;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:326>
		int32_t L_5 = V_1;
		if ((!(((uint32_t)L_5) == ((uint32_t)(-1)))))
		{
			goto IL_0014;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:327>
		return;
	}

IL_0014:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:329>
		UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* L_6 = V_0;
		NullCheck(L_6);
		NativeArray_1_t71485A1E60B31CCAD3E525C907CF172E8B804468* L_7 = (NativeArray_1_t71485A1E60B31CCAD3E525C907CF172E8B804468*)(&L_6->___visibleLights);
		int32_t L_8 = V_1;
		VisibleLight_t0A4DF5B22865A00F618A0352B805277FA0132805 L_9;
		L_9 = IL2CPP_NATIVEARRAY_GET_ITEM(VisibleLight_t0A4DF5B22865A00F618A0352B805277FA0132805, (L_7)->___m_Buffer, L_8);
		V_2 = L_9;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:331>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_10 = ___0_cmd;
		ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* L_11;
		L_11 = ProfilingSampler_Get_TisURPProfileId_tA83520239B6C0F10A73CCC6CEC7D3DA1F1932481_m9F3104BDEBD70A287E8F4BAC86579564A19A5661(((int32_t)15), ProfilingSampler_Get_TisURPProfileId_tA83520239B6C0F10A73CCC6CEC7D3DA1F1932481_m9F3104BDEBD70A287E8F4BAC86579564A19A5661_RuntimeMethod_var);
		ProfilingScope__ctor_mEF7BF01DCAD3709F978E564AEDEDD643FC617904((&V_3), L_10, L_11, NULL);
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_019d:
			{
				ProfilingScope_Dispose_m4231A2ACA1F8E345BB0078310A9F7601704C8BE4((&V_3), NULL);
				return;
			}
		});
		try
		{
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:334>
				RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_12 = ___0_cmd;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_13 = ___1_data;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_14 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_13);
				NullCheck(L_14);
				UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_15 = L_14->___cameraData;
				NullCheck(L_15);
				Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_16 = L_15->___worldSpaceCameraPos;
				il2cpp_codegen_runtime_class_init_inline(ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var);
				ShadowUtils_SetCameraPosition_mD5AB6C44100346891E7C1ADC676715C9C4D4B094(L_12, L_16, NULL);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:338>
				bool L_17 = ___2_isRenderGraph;
				if (L_17)
				{
					goto IL_0058_1;
				}
			}
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:339>
				RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_18 = ___0_cmd;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_19 = ___1_data;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_20 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_19);
				NullCheck(L_20);
				UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_21 = L_20->___cameraData;
				NullCheck(L_21);
				Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_22;
				L_22 = UniversalCameraData_GetViewMatrix_mE4676E11126A0A1F10B2425B245CF438A671A21A(L_21, 0, NULL);
				il2cpp_codegen_runtime_class_init_inline(ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var);
				ShadowUtils_SetWorldToCameraAndCameraToWorldMatrices_mE2BC025C6530F7E5E287A9E43129CF7C6D541793(L_18, L_22, NULL);
			}

IL_0058_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:341>
				V_4 = 0;
				goto IL_0113_1;
			}

IL_0060_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:343>
				int32_t L_23 = V_1;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_24 = ___1_data;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_25 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_24);
				NullCheck(L_25);
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_26 = L_25->___shadowData;
				ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04* L_27 = __this->___m_CascadeSlices;
				int32_t L_28 = V_4;
				NullCheck(L_27);
				Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_29 = ((L_27)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_28)))->___projectionMatrix;
				ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04* L_30 = __this->___m_CascadeSlices;
				int32_t L_31 = V_4;
				NullCheck(L_30);
				int32_t L_32 = ((L_30)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_31)))->___resolution;
				il2cpp_codegen_runtime_class_init_inline(ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var);
				Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_33;
				L_33 = ShadowUtils_GetShadowBias_m865F36086076039F16AF6D1AC4000B8169839BD6((&V_2), L_23, L_26, L_29, ((float)L_32), NULL);
				V_5 = L_33;
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:344>
				RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_34 = ___0_cmd;
				Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_35 = V_5;
				ShadowUtils_SetupShadowCasterConstantBuffer_mA686DC7DDB43AA723119176A4143A494403629EB(L_34, (&V_2), L_35, NULL);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:345>
				RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_36 = ___0_cmd;
				NullCheck(L_36);
				RasterCommandBuffer_SetKeyword_m134867C426359E758261AB04E59061F70A69B5C3(L_36, (&((ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91_StaticFields*)il2cpp_codegen_static_fields_for(ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91_il2cpp_TypeInfo_var))->___CastingPunctualLightShadow), (bool)0, NULL);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:346>
				bool L_37 = ___2_isRenderGraph;
				if (L_37)
				{
					goto IL_00bf_1;
				}
			}
			{
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_38 = ___1_data;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_39 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_38);
				NullCheck(L_39);
				RendererListU5BU5D_t80C28472AABC27AC3809B9300B1F81D3AB423DDC* L_40 = L_39->___shadowRendererLists;
				int32_t L_41 = V_4;
				NullCheck(L_40);
				int32_t L_42 = L_41;
				RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 L_43 = (L_40)->GetAt(static_cast<il2cpp_array_size_t>(L_42));
				G_B9_0 = L_43;
				goto IL_00d2_1;
			}

IL_00bf_1:
			{
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_44 = ___1_data;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_45 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_44);
				NullCheck(L_45);
				RendererListHandleU5BU5D_t9F182E8E1E5140DEC19C6EE1F096F0329CCB7E3B* L_46 = L_45->___shadowRendererListsHandle;
				int32_t L_47 = V_4;
				NullCheck(L_46);
				int32_t L_48 = L_47;
				RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA L_49 = (L_46)->GetAt(static_cast<il2cpp_array_size_t>(L_48));
				RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 L_50;
				L_50 = RendererListHandle_op_Implicit_m23F3E49F9D97B0BABE1044E02A7A70784F05C585(L_49, NULL);
				G_B9_0 = L_50;
			}

IL_00d2_1:
			{
				V_6 = G_B9_0;
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:347>
				RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_51 = ___0_cmd;
				ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04* L_52 = __this->___m_CascadeSlices;
				int32_t L_53 = V_4;
				NullCheck(L_52);
				ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04* L_54 = __this->___m_CascadeSlices;
				int32_t L_55 = V_4;
				NullCheck(L_54);
				Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_56 = ((L_54)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_55)))->___projectionMatrix;
				ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04* L_57 = __this->___m_CascadeSlices;
				int32_t L_58 = V_4;
				NullCheck(L_57);
				Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_59 = ((L_57)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_58)))->___viewMatrix;
				il2cpp_codegen_runtime_class_init_inline(ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var);
				ShadowUtils_RenderShadowSlice_m8A4EF57464A61CC7AB7C5E01EA905AFE3F23FF7B(L_51, ((L_52)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_53))), (&V_6), L_56, L_59, NULL);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:341>
				int32_t L_60 = V_4;
				V_4 = ((int32_t)il2cpp_codegen_add(L_60, 1));
			}

IL_0113_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:341>
				int32_t L_61 = V_4;
				int32_t L_62 = __this->___m_ShadowCasterCascadesCount;
				if ((((int32_t)L_61) < ((int32_t)L_62)))
				{
					goto IL_0060_1;
				}
			}
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:350>
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_63 = ___1_data;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_64 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_63);
				NullCheck(L_64);
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_65 = L_64->___shadowData;
				Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_66;
				L_66 = VisibleLight_get_light_mD179E0BF18C77DBE2FD85FE9687F63A8C1859E6B((&V_2), NULL);
				NullCheck(L_66);
				int32_t L_67;
				L_67 = Light_get_shadows_m1A11721F202C27838A7A8ED72455E6A727CEE6C5(L_66, NULL);
				if ((!(((uint32_t)L_67) == ((uint32_t)2))))
				{
					G_B13_0 = L_65;
					goto IL_0144_1;
				}
				G_B12_0 = L_65;
			}
			{
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_68 = ___1_data;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_69 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_68);
				NullCheck(L_69);
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_70 = L_69->___shadowData;
				NullCheck(L_70);
				bool L_71 = L_70->___supportsSoftShadows;
				G_B14_0 = ((int32_t)(L_71));
				G_B14_1 = G_B12_0;
				goto IL_0145_1;
			}

IL_0144_1:
			{
				G_B14_0 = 0;
				G_B14_1 = G_B13_0;
			}

IL_0145_1:
			{
				NullCheck(G_B14_1);
				G_B14_1->___isKeywordSoftShadowsEnabled = (bool)G_B14_0;
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:351>
				RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_72 = ___0_cmd;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_73 = ___1_data;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_74 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_73);
				NullCheck(L_74);
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_75 = L_74->___shadowData;
				NullCheck(L_75);
				int32_t L_76 = L_75->___mainLightShadowCascadesCount;
				NullCheck(L_72);
				RasterCommandBuffer_SetKeyword_m134867C426359E758261AB04E59061F70A69B5C3(L_72, (&((ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91_StaticFields*)il2cpp_codegen_static_fields_for(ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91_il2cpp_TypeInfo_var))->___MainLightShadows), (bool)((((int32_t)L_76) == ((int32_t)1))? 1 : 0), NULL);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:352>
				RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_77 = ___0_cmd;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_78 = ___1_data;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_79 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_78);
				NullCheck(L_79);
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_80 = L_79->___shadowData;
				NullCheck(L_80);
				int32_t L_81 = L_80->___mainLightShadowCascadesCount;
				NullCheck(L_77);
				RasterCommandBuffer_SetKeyword_m134867C426359E758261AB04E59061F70A69B5C3(L_77, (&((ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91_StaticFields*)il2cpp_codegen_static_fields_for(ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91_il2cpp_TypeInfo_var))->___MainLightShadowCascades), (bool)((((int32_t)L_81) > ((int32_t)1))? 1 : 0), NULL);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:353>
				RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_82 = ___0_cmd;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_83 = ___1_data;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_84 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_83);
				NullCheck(L_84);
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_85 = L_84->___shadowData;
				il2cpp_codegen_runtime_class_init_inline(ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var);
				ShadowUtils_SetSoftShadowQualityShaderKeywords_m295C5B71C0C784A63CBED40DDE0F3762D8268492(L_82, L_85, NULL);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:355>
				RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_86 = ___0_cmd;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_87 = ___1_data;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_88 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_87);
				NullCheck(L_88);
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_89 = L_88->___shadowData;
				MainLightShadowCasterPass_SetupMainLightShadowReceiverConstants_m9FCB09B3813A1B73704CE6B5C3BBB46439979131(__this, L_86, (&V_2), L_89, NULL);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:356>
				goto IL_01ab;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_01ab:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:357>
		return;
	}
}
// Method Definition Index: 35189
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_SetupMainLightShadowReceiverConstants_m9FCB09B3813A1B73704CE6B5C3BBB46439979131 (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* ___0_cmd, VisibleLight_t0A4DF5B22865A00F618A0352B805277FA0132805* ___1_shadowLight, UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* ___2_shadowData, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* V_0 = NULL;
	bool V_1 = false;
	int32_t V_2 = 0;
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 V_3;
	memset((&V_3), 0, sizeof(V_3));
	float V_4 = 0.0f;
	float V_5 = 0.0f;
	float V_6 = 0.0f;
	float V_7 = 0.0f;
	float V_8 = 0.0f;
	float V_9 = 0.0f;
	float V_10 = 0.0f;
	int32_t V_11 = 0;
	int32_t V_12 = 0;
	int32_t G_B3_0 = 0;
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* G_B8_0 = NULL;
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* G_B7_0 = NULL;
	float G_B9_0 = 0.0f;
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* G_B9_1 = NULL;
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:361>
		VisibleLight_t0A4DF5B22865A00F618A0352B805277FA0132805* L_0 = ___1_shadowLight;
		Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_1;
		L_1 = VisibleLight_get_light_mD179E0BF18C77DBE2FD85FE9687F63A8C1859E6B(L_0, NULL);
		V_0 = L_1;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:362>
		VisibleLight_t0A4DF5B22865A00F618A0352B805277FA0132805* L_2 = ___1_shadowLight;
		Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_3;
		L_3 = VisibleLight_get_light_mD179E0BF18C77DBE2FD85FE9687F63A8C1859E6B(L_2, NULL);
		NullCheck(L_3);
		int32_t L_4;
		L_4 = Light_get_shadows_m1A11721F202C27838A7A8ED72455E6A727CEE6C5(L_3, NULL);
		if ((!(((uint32_t)L_4) == ((uint32_t)2))))
		{
			goto IL_001d;
		}
	}
	{
		UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_5 = ___2_shadowData;
		NullCheck(L_5);
		bool L_6 = L_5->___supportsSoftShadows;
		G_B3_0 = ((int32_t)(L_6));
		goto IL_001e;
	}

IL_001d:
	{
		G_B3_0 = 0;
	}

IL_001e:
	{
		V_1 = (bool)G_B3_0;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:364>
		int32_t L_7 = __this->___m_ShadowCasterCascadesCount;
		V_2 = L_7;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:365>
		V_11 = 0;
		goto IL_0050;
	}

IL_002b:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:366>
		Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D* L_8 = __this->___m_MainLightShadowMatrices;
		int32_t L_9 = V_11;
		ShadowSliceDataU5BU5D_t3B41B7A06BAB3677671AEE84FBCF1A23B7DC7D04* L_10 = __this->___m_CascadeSlices;
		int32_t L_11 = V_11;
		NullCheck(L_10);
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_12 = ((L_10)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_11)))->___shadowTransform;
		NullCheck(L_8);
		(L_8)->SetAt(static_cast<il2cpp_array_size_t>(L_9), (Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6)L_12);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:365>
		int32_t L_13 = V_11;
		V_11 = ((int32_t)il2cpp_codegen_add(L_13, 1));
	}

IL_0050:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:365>
		int32_t L_14 = V_11;
		int32_t L_15 = V_2;
		if ((((int32_t)L_14) < ((int32_t)L_15)))
		{
			goto IL_002b;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:371>
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_16;
		L_16 = Matrix4x4_get_zero_m5D5F0475AD231C2C6BE5A9C80E11E24013B1B827(NULL);
		V_3 = L_16;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:372>
		bool L_17;
		L_17 = SystemInfo_get_usesReversedZBuffer_m52819B4B538F590FCA0370FC99775B3AA6B32514(NULL);
		if (L_17)
		{
			G_B8_0 = (&V_3);
			goto IL_006b;
		}
		G_B7_0 = (&V_3);
	}
	{
		G_B9_0 = (0.0f);
		G_B9_1 = G_B7_0;
		goto IL_0070;
	}

IL_006b:
	{
		G_B9_0 = (1.0f);
		G_B9_1 = G_B8_0;
	}

IL_0070:
	{
		G_B9_1->___m22 = G_B9_0;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:373>
		int32_t L_18 = V_2;
		V_12 = L_18;
		goto IL_008e;
	}

IL_007a:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:374>
		Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D* L_19 = __this->___m_MainLightShadowMatrices;
		int32_t L_20 = V_12;
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_21 = V_3;
		NullCheck(L_19);
		(L_19)->SetAt(static_cast<il2cpp_array_size_t>(L_20), (Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6)L_21);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:373>
		int32_t L_22 = V_12;
		V_12 = ((int32_t)il2cpp_codegen_add(L_22, 1));
	}

IL_008e:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:373>
		int32_t L_23 = V_12;
		if ((((int32_t)L_23) <= ((int32_t)4)))
		{
			goto IL_007a;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:376>
		int32_t L_24 = __this->___renderTargetWidth;
		V_4 = ((float)((1.0f)/((float)L_24)));
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:377>
		int32_t L_25 = __this->___renderTargetHeight;
		V_5 = ((float)((1.0f)/((float)L_25)));
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:378>
		float L_26 = V_4;
		V_6 = ((float)il2cpp_codegen_multiply((0.5f), L_26));
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:379>
		float L_27 = V_5;
		V_7 = ((float)il2cpp_codegen_multiply((0.5f), L_27));
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:380>
		Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_28 = V_0;
		bool L_29 = V_1;
		il2cpp_codegen_runtime_class_init_inline(ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var);
		float L_30;
		L_30 = ShadowUtils_SoftShadowQualityToShaderProperty_m02B6A27D17A4C26FA5E622F580113664A6ED3BE8(L_28, L_29, NULL);
		V_8 = L_30;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:382>
		float L_31 = __this->___m_MaxShadowDistanceSq;
		float L_32 = __this->___m_CascadeBorder;
		ShadowUtils_GetScaleAndBiasForLinearDistanceFade_mE07E0F336969447E89E448D23AF050BF1646B20F(L_31, L_32, (&V_9), (&V_10), NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:384>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_33 = ___0_cmd;
		il2cpp_codegen_runtime_class_init_inline(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var);
		int32_t L_34 = ((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____WorldToShadow;
		Matrix4x4U5BU5D_t9C51C93425FABC022B506D2DB3A5FA70F9752C4D* L_35 = __this->___m_MainLightShadowMatrices;
		NullCheck(L_33);
		RasterCommandBuffer_SetGlobalMatrixArray_m8AFED067E7189FA1F1D301BFD7E1F31AF191EDA1(L_33, L_34, L_35, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:385>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:386>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_36 = ___0_cmd;
		int32_t L_37 = ((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____ShadowParams;
		Light_t1E68479B7782AF2050FAA02A5DC612FD034F18F3* L_38 = V_0;
		NullCheck(L_38);
		float L_39;
		L_39 = Light_get_shadowStrength_m4AB6E78F7A28A97C61EDBD06ECEAF8A565688FC8(L_38, NULL);
		float L_40 = V_8;
		float L_41 = V_9;
		float L_42 = V_10;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_43;
		memset((&L_43), 0, sizeof(L_43));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_43), L_39, L_40, L_41, L_42, NULL);
		NullCheck(L_36);
		RasterCommandBuffer_SetGlobalVector_mB54A698709E920A961D8601DC5B2452B4866C68E(L_36, L_37, L_43, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:388>
		int32_t L_44 = __this->___m_ShadowCasterCascadesCount;
		if ((((int32_t)L_44) <= ((int32_t)1)))
		{
			goto IL_0214;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:390>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:391>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_45 = ___0_cmd;
		il2cpp_codegen_runtime_class_init_inline(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var);
		int32_t L_46 = ((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____CascadeShadowSplitSpheres0;
		Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_47 = __this->___m_CascadeSplitDistances;
		NullCheck(L_47);
		int32_t L_48 = 0;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_49 = (L_47)->GetAt(static_cast<il2cpp_array_size_t>(L_48));
		NullCheck(L_45);
		RasterCommandBuffer_SetGlobalVector_mB54A698709E920A961D8601DC5B2452B4866C68E(L_45, L_46, L_49, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:392>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:393>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_50 = ___0_cmd;
		int32_t L_51 = ((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____CascadeShadowSplitSpheres1;
		Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_52 = __this->___m_CascadeSplitDistances;
		NullCheck(L_52);
		int32_t L_53 = 1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_54 = (L_52)->GetAt(static_cast<il2cpp_array_size_t>(L_53));
		NullCheck(L_50);
		RasterCommandBuffer_SetGlobalVector_mB54A698709E920A961D8601DC5B2452B4866C68E(L_50, L_51, L_54, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:394>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:395>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_55 = ___0_cmd;
		int32_t L_56 = ((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____CascadeShadowSplitSpheres2;
		Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_57 = __this->___m_CascadeSplitDistances;
		NullCheck(L_57);
		int32_t L_58 = 2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_59 = (L_57)->GetAt(static_cast<il2cpp_array_size_t>(L_58));
		NullCheck(L_55);
		RasterCommandBuffer_SetGlobalVector_mB54A698709E920A961D8601DC5B2452B4866C68E(L_55, L_56, L_59, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:396>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:397>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_60 = ___0_cmd;
		int32_t L_61 = ((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____CascadeShadowSplitSpheres3;
		Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_62 = __this->___m_CascadeSplitDistances;
		NullCheck(L_62);
		int32_t L_63 = 3;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_64 = (L_62)->GetAt(static_cast<il2cpp_array_size_t>(L_63));
		NullCheck(L_60);
		RasterCommandBuffer_SetGlobalVector_mB54A698709E920A961D8601DC5B2452B4866C68E(L_60, L_61, L_64, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:398>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:399>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:400>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:401>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:402>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_65 = ___0_cmd;
		int32_t L_66 = ((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____CascadeShadowSplitSphereRadii;
		Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_67 = __this->___m_CascadeSplitDistances;
		NullCheck(L_67);
		float L_68 = ((L_67)->GetAddressAt(static_cast<il2cpp_array_size_t>(0)))->___w;
		Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_69 = __this->___m_CascadeSplitDistances;
		NullCheck(L_69);
		float L_70 = ((L_69)->GetAddressAt(static_cast<il2cpp_array_size_t>(0)))->___w;
		Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_71 = __this->___m_CascadeSplitDistances;
		NullCheck(L_71);
		float L_72 = ((L_71)->GetAddressAt(static_cast<il2cpp_array_size_t>(1)))->___w;
		Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_73 = __this->___m_CascadeSplitDistances;
		NullCheck(L_73);
		float L_74 = ((L_73)->GetAddressAt(static_cast<il2cpp_array_size_t>(1)))->___w;
		Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_75 = __this->___m_CascadeSplitDistances;
		NullCheck(L_75);
		float L_76 = ((L_75)->GetAddressAt(static_cast<il2cpp_array_size_t>(2)))->___w;
		Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_77 = __this->___m_CascadeSplitDistances;
		NullCheck(L_77);
		float L_78 = ((L_77)->GetAddressAt(static_cast<il2cpp_array_size_t>(2)))->___w;
		Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_79 = __this->___m_CascadeSplitDistances;
		NullCheck(L_79);
		float L_80 = ((L_79)->GetAddressAt(static_cast<il2cpp_array_size_t>(3)))->___w;
		Vector4U5BU5D_tC0F3A7115F85007510F6D173968200CD31BCF7AD* L_81 = __this->___m_CascadeSplitDistances;
		NullCheck(L_81);
		float L_82 = ((L_81)->GetAddressAt(static_cast<il2cpp_array_size_t>(3)))->___w;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_83;
		memset((&L_83), 0, sizeof(L_83));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_83), ((float)il2cpp_codegen_multiply(L_68, L_70)), ((float)il2cpp_codegen_multiply(L_72, L_74)), ((float)il2cpp_codegen_multiply(L_76, L_78)), ((float)il2cpp_codegen_multiply(L_80, L_82)), NULL);
		NullCheck(L_65);
		RasterCommandBuffer_SetGlobalVector_mB54A698709E920A961D8601DC5B2452B4866C68E(L_65, L_66, L_83, NULL);
	}

IL_0214:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:409>
		UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_84 = ___2_shadowData;
		NullCheck(L_84);
		bool L_85 = L_84->___supportsSoftShadows;
		if (!L_85)
		{
			goto IL_0272;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:411>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:412>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:413>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_86 = ___0_cmd;
		il2cpp_codegen_runtime_class_init_inline(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var);
		int32_t L_87 = ((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____ShadowOffset0;
		float L_88 = V_6;
		float L_89 = V_7;
		float L_90 = V_6;
		float L_91 = V_7;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_92;
		memset((&L_92), 0, sizeof(L_92));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_92), ((-L_88)), ((-L_89)), L_90, ((-L_91)), NULL);
		NullCheck(L_86);
		RasterCommandBuffer_SetGlobalVector_mB54A698709E920A961D8601DC5B2452B4866C68E(L_86, L_87, L_92, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:414>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:415>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:416>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_93 = ___0_cmd;
		int32_t L_94 = ((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____ShadowOffset1;
		float L_95 = V_6;
		float L_96 = V_7;
		float L_97 = V_6;
		float L_98 = V_7;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_99;
		memset((&L_99), 0, sizeof(L_99));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_99), ((-L_95)), L_96, L_97, L_98, NULL);
		NullCheck(L_93);
		RasterCommandBuffer_SetGlobalVector_mB54A698709E920A961D8601DC5B2452B4866C68E(L_93, L_94, L_99, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:418>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:419>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:420>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_100 = ___0_cmd;
		int32_t L_101 = ((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____ShadowmapSize;
		float L_102 = V_4;
		float L_103 = V_5;
		int32_t L_104 = __this->___renderTargetWidth;
		int32_t L_105 = __this->___renderTargetHeight;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_106;
		memset((&L_106), 0, sizeof(L_106));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_106), L_102, L_103, ((float)L_104), ((float)L_105), NULL);
		NullCheck(L_100);
		RasterCommandBuffer_SetGlobalVector_mB54A698709E920A961D8601DC5B2452B4866C68E(L_100, L_101, L_106, NULL);
	}

IL_0272:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:422>
		return;
	}
}
// Method Definition Index: 35190
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_InitPassData_m53ED19C2B72CB997374CEC86E590E380BD9DEF6E (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** ___0_passData, UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* ___1_renderingData, UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* ___2_cameraData, UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* ___3_lightData, UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* ___4_shadowData, const RuntimeMethod* method) 
{
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:431>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_0 = ___0_passData;
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_1 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_0);
		NullCheck(L_1);
		L_1->___pass = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___pass), (void*)__this);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:432>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_2 = ___0_passData;
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_3 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_2);
		bool L_4 = __this->___m_CreateEmptyShadowmap;
		NullCheck(L_3);
		L_3->___emptyShadowmap = L_4;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:433>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_5 = ___0_passData;
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_6 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_5);
		bool L_7 = __this->___m_SetKeywordForEmptyShadowmap;
		NullCheck(L_6);
		L_6->___setKeywordForEmptyShadowmap = L_7;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:434>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_8 = ___0_passData;
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_9 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_8);
		UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* L_10 = ___1_renderingData;
		NullCheck(L_9);
		L_9->___renderingData = L_10;
		Il2CppCodeGenWriteBarrier((void**)(&L_9->___renderingData), (void*)L_10);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:435>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_11 = ___0_passData;
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_12 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_11);
		UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_13 = ___2_cameraData;
		NullCheck(L_12);
		L_12->___cameraData = L_13;
		Il2CppCodeGenWriteBarrier((void**)(&L_12->___cameraData), (void*)L_13);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:436>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_14 = ___0_passData;
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_15 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_14);
		UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* L_16 = ___3_lightData;
		NullCheck(L_15);
		L_15->___lightData = L_16;
		Il2CppCodeGenWriteBarrier((void**)(&L_15->___lightData), (void*)L_16);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:437>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_17 = ___0_passData;
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_18 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_17);
		UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_19 = ___4_shadowData;
		NullCheck(L_18);
		L_18->___shadowData = L_19;
		Il2CppCodeGenWriteBarrier((void**)(&L_18->___shadowData), (void*)L_19);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:438>
		return;
	}
}
// Method Definition Index: 35191
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass_InitRendererLists_mF29AB3ADDD25931ABEC828A4A71189E87F3E9A93 (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** ___0_passData, ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 ___1_context, RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* ___2_renderGraph, bool ___3_useRenderGraph, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniversalRenderPipeline_t54B4737DC500C08628C5BE283D8C583C14DED98F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	ShadowDrawingSettings_t3C0AD7F3960F1BF5536867AF6E641F23F4C7AFA4 V_1;
	memset((&V_1), 0, sizeof(V_1));
	ShadowDrawingSettings_t3C0AD7F3960F1BF5536867AF6E641F23F4C7AFA4 V_2;
	memset((&V_2), 0, sizeof(V_2));
	int32_t V_3 = 0;
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:442>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_0 = ___0_passData;
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_1 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_0);
		NullCheck(L_1);
		UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* L_2 = L_1->___lightData;
		NullCheck(L_2);
		int32_t L_3 = L_2->___mainLightIndex;
		V_0 = L_3;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:443>
		bool L_4 = __this->___m_CreateEmptyShadowmap;
		if (L_4)
		{
			goto IL_0082;
		}
	}
	{
		int32_t L_5 = V_0;
		if ((((int32_t)L_5) == ((int32_t)(-1))))
		{
			goto IL_0082;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:445>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:446>
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:447>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_6 = ___0_passData;
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_7 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_6);
		NullCheck(L_7);
		UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* L_8 = L_7->___renderingData;
		NullCheck(L_8);
		CullingResults_tD6B7EF20B68D47DFF3A99EB2EA73F47F1D460267 L_9 = L_8->___cullResults;
		int32_t L_10 = V_0;
		ShadowDrawingSettings__ctor_mA9BADD0F63BF177F6BF380999B9B7115263B2BF4((&V_2), L_9, L_10, NULL);
		il2cpp_codegen_runtime_class_init_inline(UniversalRenderPipeline_t54B4737DC500C08628C5BE283D8C583C14DED98F_il2cpp_TypeInfo_var);
		UniversalRenderPipelineAsset_tE8A9AA6F030CC3B558CEA2EB54FFF4FC58CA6232* L_11;
		L_11 = UniversalRenderPipeline_get_asset_mCDEF564C748A6FE271F3749C82ECA64D0F6DE9E9(NULL);
		NullCheck(L_11);
		bool L_12;
		L_12 = UniversalRenderPipelineAsset_get_useRenderingLayers_mA473541E634D2A1BEB4CEAFBF27B79251E0FA5E6_inline(L_11, NULL);
		ShadowDrawingSettings_set_useRenderingLayerMaskTest_m9E81FEE30547B3720C365016689F87A2AB63F025((&V_2), L_12, NULL);
		ShadowDrawingSettings_t3C0AD7F3960F1BF5536867AF6E641F23F4C7AFA4 L_13 = V_2;
		V_1 = L_13;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:449>
		V_3 = 0;
		goto IL_0079;
	}

IL_0044:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:451>
		bool L_14 = ___3_useRenderGraph;
		if (!L_14)
		{
			goto IL_005f;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:452>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_15 = ___0_passData;
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_16 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_15);
		NullCheck(L_16);
		RendererListHandleU5BU5D_t9F182E8E1E5140DEC19C6EE1F096F0329CCB7E3B* L_17 = L_16->___shadowRendererListsHandle;
		int32_t L_18 = V_3;
		RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* L_19 = ___2_renderGraph;
		NullCheck(L_19);
		RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA L_20;
		L_20 = RenderGraph_CreateShadowRendererList_mB2EE56B34D7B4C2544C0044F040979D561AB12EE(L_19, (&V_1), NULL);
		NullCheck(L_17);
		(L_17)->SetAt(static_cast<il2cpp_array_size_t>(L_18), (RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA)L_20);
		goto IL_0075;
	}

IL_005f:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:454>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A** L_21 = ___0_passData;
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_22 = *((PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A**)L_21);
		NullCheck(L_22);
		RendererListU5BU5D_t80C28472AABC27AC3809B9300B1F81D3AB423DDC* L_23 = L_22->___shadowRendererLists;
		int32_t L_24 = V_3;
		il2cpp_codegen_runtime_class_init_inline(ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36_il2cpp_TypeInfo_var);
		RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85 L_25;
		L_25 = ScriptableRenderContext_CreateShadowRendererList_m34053F9A4CF0DCB00BD4A8816C2756A3943DDA3D((&___1_context), (&V_1), NULL);
		NullCheck(L_23);
		(L_23)->SetAt(static_cast<il2cpp_array_size_t>(L_24), (RendererList_t608CE60421616EF4211F5B8AC62E3C36D4BDDF85)L_25);
	}

IL_0075:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:449>
		int32_t L_26 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_add(L_26, 1));
	}

IL_0079:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:449>
		int32_t L_27 = V_3;
		int32_t L_28 = __this->___m_ShadowCasterCascadesCount;
		if ((((int32_t)L_27) < ((int32_t)L_28)))
		{
			goto IL_0044;
		}
	}

IL_0082:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:457>
		return;
	}
}
// Method Definition Index: 35192
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 MainLightShadowCasterPass_Render_m3FF52791F1B4A2D12C50839271DA19041D83CE66 (MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* __this, RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* ___0_graph, ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* ___1_frameData, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContextContainer_Get_TisUniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7_mE62F7903614384541B770CC0B9A99BD2E608A0F8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContextContainer_Get_TisUniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2_m7D529C251F384985900DEA334BC399BAD36BFC8F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContextContainer_Get_TisUniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6_m71849CEFB9C023EDE026A0F38CE6044274505C06_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ContextContainer_Get_TisUniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C_m071A668846CA61588A4D53CCA3CFCD3730530E4E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IBaseRenderGraphBuilder_tFFF84F72F862F1BE246A789AB6A59F959B490F3D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IRasterRenderGraphBuilder_SetRenderFunc_TisPassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A_mF80B40083B6B7C1FEDE12E6F5156C27582A83E8B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IRasterRenderGraphBuilder_t607F94718848D836CFEEF0DE553E4A79CABD9372_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RenderGraph_AddRasterRenderPass_TisPassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A_mC53A7D9CBCFE77D6877B8B7B2819DACBFD86AE9B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3CRenderU3Eb__39_0_m33741C44C25353E83945D40A6BCA43D0EB888829_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniversalRenderer_t31019D4AD52F646128E0D1649E7B87E33BA36D8A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5FCFE8BB42D2AA29FAADC098234D0BEF8F39BE29);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral73FF05278C18960F05EB20845B1ECE59D9D3F9FF);
		s_Il2CppMethodInitialized = true;
	}
	UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* V_0 = NULL;
	UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* V_1 = NULL;
	UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* V_2 = NULL;
	UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* V_3 = NULL;
	TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 V_4;
	memset((&V_4), 0, sizeof(V_4));
	RuntimeObject* V_5 = NULL;
	PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* V_6 = NULL;
	ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 V_7;
	memset((&V_7), 0, sizeof(V_7));
	int32_t V_8 = 0;
	int32_t G_B7_0 = 0;
	String_t* G_B7_1 = NULL;
	RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46 G_B7_2;
	memset((&G_B7_2), 0, sizeof(G_B7_2));
	RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* G_B7_3 = NULL;
	int32_t G_B6_0 = 0;
	String_t* G_B6_1 = NULL;
	RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46 G_B6_2;
	memset((&G_B6_2), 0, sizeof(G_B6_2));
	RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* G_B6_3 = NULL;
	int32_t G_B8_0 = 0;
	int32_t G_B8_1 = 0;
	String_t* G_B8_2 = NULL;
	RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46 G_B8_3;
	memset((&G_B8_3), 0, sizeof(G_B8_3));
	RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* G_B8_4 = NULL;
	BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D* G_B14_0 = NULL;
	RuntimeObject* G_B14_1 = NULL;
	BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D* G_B13_0 = NULL;
	RuntimeObject* G_B13_1 = NULL;
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:461>
		ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* L_0 = ___1_frameData;
		NullCheck(L_0);
		UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* L_1;
		L_1 = ContextContainer_Get_TisUniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6_m71849CEFB9C023EDE026A0F38CE6044274505C06_inline(L_0, ContextContainer_Get_TisUniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6_m71849CEFB9C023EDE026A0F38CE6044274505C06_RuntimeMethod_var);
		V_0 = L_1;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:462>
		ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* L_2 = ___1_frameData;
		NullCheck(L_2);
		UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_3;
		L_3 = ContextContainer_Get_TisUniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7_mE62F7903614384541B770CC0B9A99BD2E608A0F8_inline(L_2, ContextContainer_Get_TisUniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7_mE62F7903614384541B770CC0B9A99BD2E608A0F8_RuntimeMethod_var);
		V_1 = L_3;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:463>
		ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* L_4 = ___1_frameData;
		NullCheck(L_4);
		UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* L_5;
		L_5 = ContextContainer_Get_TisUniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2_m7D529C251F384985900DEA334BC399BAD36BFC8F_inline(L_4, ContextContainer_Get_TisUniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2_m7D529C251F384985900DEA334BC399BAD36BFC8F_RuntimeMethod_var);
		V_2 = L_5;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:464>
		ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* L_6 = ___1_frameData;
		NullCheck(L_6);
		UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_7;
		L_7 = ContextContainer_Get_TisUniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C_m071A668846CA61588A4D53CCA3CFCD3730530E4E_inline(L_6, ContextContainer_Get_TisUniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C_m071A668846CA61588A4D53CCA3CFCD3730530E4E_RuntimeMethod_var);
		V_3 = L_7;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:468>
		RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* L_8 = ___0_graph;
		String_t* L_9;
		L_9 = ScriptableRenderPass_get_passName_m838292A44DB6ED7D67E43C1DE58383959B4F1925_inline(__this, NULL);
		ProfilingSampler_t420D4672EDB44E0EF980B31ADFD9E5747200FECE* L_10;
		L_10 = ScriptableRenderPass_get_profilingSampler_m627C9BF8A4A08101DCB6F40E0A97145A5A1CDA38(__this, NULL);
		NullCheck(L_8);
		RuntimeObject* L_11;
		L_11 = RenderGraph_AddRasterRenderPass_TisPassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A_mC53A7D9CBCFE77D6877B8B7B2819DACBFD86AE9B(L_8, L_9, (&V_6), L_10, _stringLiteral73FF05278C18960F05EB20845B1ECE59D9D3F9FF, ((int32_t)468), RenderGraph_AddRasterRenderPass_TisPassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A_mC53A7D9CBCFE77D6877B8B7B2819DACBFD86AE9B_RuntimeMethod_var);
		V_5 = L_11;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_010e:
			{
				{
					RuntimeObject* L_12 = V_5;
					if (!L_12)
					{
						goto IL_0119;
					}
				}
				{
					RuntimeObject* L_13 = V_5;
					NullCheck(L_13);
					InterfaceActionInvoker0::Invoke(0, IDisposable_t030E0496B4E0E4E4F086825007979AF51F7248C5_il2cpp_TypeInfo_var, L_13);
				}

IL_0119:
				{
					return;
				}
			}
		});
		try
		{
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:470>
				UniversalRenderingData_t045D13A1E051DF623B170223B7CBF1161DDA94E6* L_14 = V_0;
				UniversalCameraData_t7904AE9D68B973761A72DFAA7CA3DE915696E1C7* L_15 = V_1;
				UniversalLightData_tCFFFAB4033CD97BE9F339F0C90BB7F98C06FFBA2* L_16 = V_2;
				UniversalShadowData_t25DAA68FC1556989EBF2235389B4E652DF98B06C* L_17 = V_3;
				MainLightShadowCasterPass_InitPassData_m53ED19C2B72CB997374CEC86E590E380BD9DEF6E(__this, (&V_6), L_14, L_15, L_16, L_17, NULL);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:471>
				il2cpp_codegen_initobj((&V_7), sizeof(ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36));
				ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 L_18 = V_7;
				RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* L_19 = ___0_graph;
				MainLightShadowCasterPass_InitRendererLists_mF29AB3ADDD25931ABEC828A4A71189E87F3E9A93(__this, (&V_6), L_18, L_19, (bool)1, NULL);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:473>
				bool L_20 = __this->___m_CreateEmptyShadowmap;
				if (L_20)
				{
					goto IL_00ba_1;
				}
			}
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:475>
				V_8 = 0;
				goto IL_0084_1;
			}

IL_0069_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:477>
				RuntimeObject* L_21 = V_5;
				PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_22 = V_6;
				NullCheck(L_22);
				RendererListHandleU5BU5D_t9F182E8E1E5140DEC19C6EE1F096F0329CCB7E3B* L_23 = L_22->___shadowRendererListsHandle;
				int32_t L_24 = V_8;
				NullCheck(L_23);
				NullCheck(L_21);
				InterfaceActionInvoker1< RendererListHandle_t2DFC72A560B979AE0BAFBABBD8B9AF5DC1FEFEBA* >::Invoke(9, IBaseRenderGraphBuilder_tFFF84F72F862F1BE246A789AB6A59F959B490F3D_il2cpp_TypeInfo_var, L_21, ((L_23)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_24))));
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:475>
				int32_t L_25 = V_8;
				V_8 = ((int32_t)il2cpp_codegen_add(L_25, 1));
			}

IL_0084_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:475>
				int32_t L_26 = V_8;
				int32_t L_27 = __this->___m_ShadowCasterCascadesCount;
				if ((((int32_t)L_26) < ((int32_t)L_27)))
				{
					goto IL_0069_1;
				}
			}
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:480>
				RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* L_28 = ___0_graph;
				RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46 L_29 = __this->___m_MainLightShadowDescriptor;
				il2cpp_codegen_runtime_class_init_inline(ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var);
				bool L_30 = ((ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_StaticFields*)il2cpp_codegen_static_fields_for(ShadowUtils_tCFE84AA46B8156AE034EF14220AD0BBEB011F4E5_il2cpp_TypeInfo_var))->___m_ForceShadowPointSampling;
				if (L_30)
				{
					G_B7_0 = 1;
					G_B7_1 = _stringLiteral5FCFE8BB42D2AA29FAADC098234D0BEF8F39BE29;
					G_B7_2 = L_29;
					G_B7_3 = L_28;
					goto IL_00a5_1;
				}
				G_B6_0 = 1;
				G_B6_1 = _stringLiteral5FCFE8BB42D2AA29FAADC098234D0BEF8F39BE29;
				G_B6_2 = L_29;
				G_B6_3 = L_28;
			}
			{
				G_B8_0 = 1;
				G_B8_1 = G_B6_0;
				G_B8_2 = G_B6_1;
				G_B8_3 = G_B6_2;
				G_B8_4 = G_B6_3;
				goto IL_00a6_1;
			}

IL_00a5_1:
			{
				G_B8_0 = 0;
				G_B8_1 = G_B7_0;
				G_B8_2 = G_B7_1;
				G_B8_3 = G_B7_2;
				G_B8_4 = G_B7_3;
			}

IL_00a6_1:
			{
				il2cpp_codegen_runtime_class_init_inline(UniversalRenderer_t31019D4AD52F646128E0D1649E7B87E33BA36D8A_il2cpp_TypeInfo_var);
				TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 L_31;
				L_31 = UniversalRenderer_CreateRenderGraphTexture_m4BCA4F2339499873D3DE1C8562D3FB7B7DE21613(G_B8_4, G_B8_3, G_B8_2, (bool)G_B8_1, G_B8_0, 1, NULL);
				V_4 = L_31;
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:481>
				RuntimeObject* L_32 = V_5;
				TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 L_33 = V_4;
				NullCheck(L_32);
				InterfaceActionInvoker2< TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388, int32_t >::Invoke(4, IRasterRenderGraphBuilder_t607F94718848D836CFEEF0DE553E4A79CABD9372_il2cpp_TypeInfo_var, L_32, L_33, 2);
				goto IL_00c7_1;
			}

IL_00ba_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:485>
				RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* L_34 = ___0_graph;
				NullCheck(L_34);
				RenderGraphDefaultResources_tCE331152C84ED1A36CF186CA0092AE10E599E25D* L_35;
				L_35 = RenderGraph_get_defaultResources_m9392476073E82DC8F45ED8AB11B271EA471FC206_inline(L_34, NULL);
				NullCheck(L_35);
				TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 L_36;
				L_36 = RenderGraphDefaultResources_get_defaultShadowTexture_mF16D95793225F7D0A560E587401F5638A8A8E913_inline(L_35, NULL);
				V_4 = L_36;
			}

IL_00c7_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:488>
				RuntimeObject* L_37 = V_5;
				NullCheck(L_37);
				InterfaceActionInvoker1< bool >::Invoke(12, IBaseRenderGraphBuilder_tFFF84F72F862F1BE246A789AB6A59F959B490F3D_il2cpp_TypeInfo_var, L_37, (bool)1);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:490>
				il2cpp_codegen_runtime_class_init_inline(TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388_il2cpp_TypeInfo_var);
				bool L_38;
				L_38 = TextureHandle_IsValid_mECFF64B8BAC6402F0D37B67BB79FFB3AB3C7F3C2_inline((&V_4), NULL);
				if (!L_38)
				{
					goto IL_00e6_1;
				}
			}
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:491>
				RuntimeObject* L_39 = V_5;
				il2cpp_codegen_runtime_class_init_inline(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var);
				int32_t L_40 = ((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____MainLightShadowmapID;
				NullCheck(L_39);
				InterfaceActionInvoker2< TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388*, int32_t >::Invoke(3, IBaseRenderGraphBuilder_tFFF84F72F862F1BE246A789AB6A59F959B490F3D_il2cpp_TypeInfo_var, L_39, (&V_4), L_40);
			}

IL_00e6_1:
			{
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:493>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:494>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:495>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:496>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:497>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:498>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:499>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:500>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:501>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:502>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:503>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:504>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:505>
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:506>
				RuntimeObject* L_41 = V_5;
				il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_il2cpp_TypeInfo_var);
				BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D* L_42 = ((U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_il2cpp_TypeInfo_var))->___U3CU3E9__39_0;
				BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D* L_43 = L_42;
				if (L_43)
				{
					G_B14_0 = L_43;
					G_B14_1 = L_41;
					goto IL_0107_1;
				}
				G_B13_0 = L_43;
				G_B13_1 = L_41;
			}
			{
				il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_il2cpp_TypeInfo_var);
				U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264* L_44 = ((U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_il2cpp_TypeInfo_var))->___U3CU3E9;
				BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D* L_45 = (BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D*)il2cpp_codegen_object_new(BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D_il2cpp_TypeInfo_var);
				BaseRenderFunc_2__ctor_m37F90588EE8CDF8E636149561D131DF846D2406E(L_45, L_44, (intptr_t)((void*)U3CU3Ec_U3CRenderU3Eb__39_0_m33741C44C25353E83945D40A6BCA43D0EB888829_RuntimeMethod_var), NULL);
				BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D* L_46 = L_45;
				((U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_il2cpp_TypeInfo_var))->___U3CU3E9__39_0 = L_46;
				Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_il2cpp_TypeInfo_var))->___U3CU3E9__39_0), (void*)L_46);
				G_B14_0 = L_46;
				G_B14_1 = G_B13_1;
			}

IL_0107_1:
			{
				NullCheck(G_B14_1);
				GenericInterfaceActionInvoker1< BaseRenderFunc_2_tA15E6EDA1B9C738B9499AE215AE60ACD977F6A5D* >::Invoke(IRasterRenderGraphBuilder_SetRenderFunc_TisPassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A_mF80B40083B6B7C1FEDE12E6F5156C27582A83E8B_RuntimeMethod_var, G_B14_1, G_B14_0);
				//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:507>
				goto IL_011a;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_011a:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:509>
		TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 L_47 = V_4;
		return L_47;
	}
}
// Method Definition Index: 35193
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowCasterPass__cctor_mE4EE322985B7D688F09A50415FFCACC9FA2D3F60 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:37>
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_0;
		memset((&L_0), 0, sizeof(L_0));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_0), (0.0f), (0.0f), (1.0f), (0.0f), NULL);
		((MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var))->___s_EmptyShadowParams = L_0;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:38>
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_1;
		memset((&L_1), 0, sizeof(L_1));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_1), (1.0f), (1.0f), (1.0f), (1.0f), NULL);
		((MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var))->___s_EmptyShadowmapSize = L_1;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Method Definition Index: 35194
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MainLightShadowConstantBuffer__cctor_m14A3DA235FD398CC1C9F0495A250FD34CBCDEB21 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral078C5819101F5FFE2A982E701EF7729257290FF7);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1428B80A06183DF581677E12334AA50BA983AEA7);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral22CD4666EAE2AB1BED30C112E46A3027A62CFC9B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2CC3067D916B46FDC1022B552374730D579C2A17);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral59DE37D5237EDCD7A817E9624C3FD01BE5F920C3);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5F20103D3E71C714D1518DC7B1C00D058D9A1D37);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5FCFE8BB42D2AA29FAADC098234D0BEF8F39BE29);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7796075F085B1A73F0304B0217A48603C4A6B819);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7A4F8AA35B73603CF0C795996C54334A2D00248C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7E7DB1B4C2DED075605289B76FF28624395D3688);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralABC8C9038985FA39FE6CEE7EA87E485F8723F5AE);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:43>
		int32_t L_0;
		L_0 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral7796075F085B1A73F0304B0217A48603C4A6B819, NULL);
		((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____WorldToShadow = L_0;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:44>
		int32_t L_1;
		L_1 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral22CD4666EAE2AB1BED30C112E46A3027A62CFC9B, NULL);
		((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____ShadowParams = L_1;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:45>
		int32_t L_2;
		L_2 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteralABC8C9038985FA39FE6CEE7EA87E485F8723F5AE, NULL);
		((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____CascadeShadowSplitSpheres0 = L_2;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:46>
		int32_t L_3;
		L_3 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral1428B80A06183DF581677E12334AA50BA983AEA7, NULL);
		((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____CascadeShadowSplitSpheres1 = L_3;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:47>
		int32_t L_4;
		L_4 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral7A4F8AA35B73603CF0C795996C54334A2D00248C, NULL);
		((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____CascadeShadowSplitSpheres2 = L_4;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:48>
		int32_t L_5;
		L_5 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral2CC3067D916B46FDC1022B552374730D579C2A17, NULL);
		((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____CascadeShadowSplitSpheres3 = L_5;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:49>
		int32_t L_6;
		L_6 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral5F20103D3E71C714D1518DC7B1C00D058D9A1D37, NULL);
		((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____CascadeShadowSplitSphereRadii = L_6;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:50>
		int32_t L_7;
		L_7 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral078C5819101F5FFE2A982E701EF7729257290FF7, NULL);
		((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____ShadowOffset0 = L_7;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:51>
		int32_t L_8;
		L_8 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral7E7DB1B4C2DED075605289B76FF28624395D3688, NULL);
		((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____ShadowOffset1 = L_8;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:52>
		int32_t L_9;
		L_9 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral59DE37D5237EDCD7A817E9624C3FD01BE5F920C3, NULL);
		((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____ShadowmapSize = L_9;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:53>
		int32_t L_10;
		L_10 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral5FCFE8BB42D2AA29FAADC098234D0BEF8F39BE29, NULL);
		((MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_StaticFields*)il2cpp_codegen_static_fields_for(MainLightShadowConstantBuffer_tDE7E52C397EA5C7066924F7C9DC843321DF0A6E3_il2cpp_TypeInfo_var))->____MainLightShadowmapID = L_10;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Method Definition Index: 35195
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PassData__ctor_m8A566CD8229EC1D10D7A229EC89195F68F862CE9 (PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RendererListHandleU5BU5D_t9F182E8E1E5140DEC19C6EE1F096F0329CCB7E3B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RendererListU5BU5D_t80C28472AABC27AC3809B9300B1F81D3AB423DDC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:66>
		RendererListU5BU5D_t80C28472AABC27AC3809B9300B1F81D3AB423DDC* L_0 = (RendererListU5BU5D_t80C28472AABC27AC3809B9300B1F81D3AB423DDC*)(RendererListU5BU5D_t80C28472AABC27AC3809B9300B1F81D3AB423DDC*)SZArrayNew(RendererListU5BU5D_t80C28472AABC27AC3809B9300B1F81D3AB423DDC_il2cpp_TypeInfo_var, (uint32_t)4);
		__this->___shadowRendererLists = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___shadowRendererLists), (void*)L_0);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:67>
		RendererListHandleU5BU5D_t9F182E8E1E5140DEC19C6EE1F096F0329CCB7E3B* L_1 = (RendererListHandleU5BU5D_t9F182E8E1E5140DEC19C6EE1F096F0329CCB7E3B*)(RendererListHandleU5BU5D_t9F182E8E1E5140DEC19C6EE1F096F0329CCB7E3B*)SZArrayNew(RendererListHandleU5BU5D_t9F182E8E1E5140DEC19C6EE1F096F0329CCB7E3B_il2cpp_TypeInfo_var, (uint32_t)4);
		__this->___shadowRendererListsHandle = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___shadowRendererListsHandle), (void*)L_1);
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Method Definition Index: 35196
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__cctor_m0C5537439A9A39074863DDB68E4394401731B16A (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264* L_0 = (U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264*)il2cpp_codegen_object_new(U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_il2cpp_TypeInfo_var);
		U3CU3Ec__ctor_m058892C1C716FD6680DFAFD5ED2A8BFAD692237C(L_0, NULL);
		((U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_il2cpp_TypeInfo_var))->___U3CU3E9 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264_il2cpp_TypeInfo_var))->___U3CU3E9), (void*)L_0);
		return;
	}
}
// Method Definition Index: 35197
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_m058892C1C716FD6680DFAFD5ED2A8BFAD692237C (U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
// Method Definition Index: 35198
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec_U3CRenderU3Eb__39_0_m33741C44C25353E83945D40A6BCA43D0EB888829 (U3CU3Ec_tC651251045AF6738E88E0DC843CE52D8B72A3264* __this, PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* ___0_data, RasterGraphContext_tC4D3E53FF7131476487751ACB0237D56C8327147 ___1_context, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* V_0 = NULL;
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:495>
		RasterGraphContext_tC4D3E53FF7131476487751ACB0237D56C8327147 L_0 = ___1_context;
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_1 = L_0.___cmd;
		V_0 = L_1;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:496>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_2 = ___0_data;
		NullCheck(L_2);
		bool L_3 = L_2->___emptyShadowmap;
		if (L_3)
		{
			goto IL_001f;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:498>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_4 = ___0_data;
		NullCheck(L_4);
		MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3* L_5 = L_4->___pass;
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_6 = V_0;
		NullCheck(L_5);
		MainLightShadowCasterPass_RenderMainLightCascadeShadowmap_m7F529AA6F3582E375793023C425D366AA04F0547(L_5, L_6, (&___0_data), (bool)1, NULL);
		return;
	}

IL_001f:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:502>
		PassData_t4EBCD157121F31BADC982DAC39A13767A7E5207A* L_7 = ___0_data;
		NullCheck(L_7);
		bool L_8 = L_7->___setKeywordForEmptyShadowmap;
		if (!L_8)
		{
			goto IL_0032;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:503>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_9 = V_0;
		NullCheck(L_9);
		RasterCommandBuffer_EnableKeyword_m554A685119A5DE8DAD67ADA432176DCEFEDA3494(L_9, (&((ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91_StaticFields*)il2cpp_codegen_static_fields_for(ShaderGlobalKeywords_tCD2A8F654428E5D252204D24E379C6725E5A2C91_il2cpp_TypeInfo_var))->___MainLightShadows), NULL);
	}

IL_0032:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:504>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_10 = V_0;
		il2cpp_codegen_runtime_class_init_inline(MainLightShadowCasterPass_tC550260377ED69F98337CF963695B7A090B137E3_il2cpp_TypeInfo_var);
		MainLightShadowCasterPass_SetShadowParamsForEmptyShadowmap_mCBE8758960CEB5A2DF6953350A969A492312810E(L_10, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/MainLightShadowCasterPass.cs:506>
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Method Definition Index: 35199
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* RenderTargetBufferSystem_get_backBuffer_mF7305DBD196865D17A0029AC212E8CC02D8205A2 (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:27>
		il2cpp_codegen_runtime_class_init_inline(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var);
		bool L_0 = ((RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_StaticFields*)il2cpp_codegen_static_fields_for(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var))->___m_AisBackBuffer;
		if (L_0)
		{
			goto IL_000e;
		}
	}
	{
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_1 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_B);
		return L_1;
	}

IL_000e:
	{
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_2 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		return L_2;
	}
}
// Method Definition Index: 35200
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* RenderTargetBufferSystem_get_frontBuffer_mDD8E09139E41F59A682216AD6C0A94AE4387E11A (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:28>
		il2cpp_codegen_runtime_class_init_inline(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var);
		bool L_0 = ((RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_StaticFields*)il2cpp_codegen_static_fields_for(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var))->___m_AisBackBuffer;
		if (L_0)
		{
			goto IL_000e;
		}
	}
	{
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_1 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		return L_1;
	}

IL_000e:
	{
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_2 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_B);
		return L_2;
	}
}
// Method Definition Index: 35201
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RenderTargetBufferSystem__ctor_m86BE218D4CA2ED16CC91EEAE8A08BE67A5E860BE (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, String_t* ___0_name, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralAB69FA1AB6BB831506EFCAD83900FEE751E85F6F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEF420ABFDDBDA7B9EE665D85EF62E4A437554003);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:25>
		__this->___m_AllowMSAA = (bool)1;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:30>
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:32>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_0 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		String_t* L_1 = ___0_name;
		String_t* L_2;
		L_2 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(L_1, _stringLiteralEF420ABFDDBDA7B9EE665D85EF62E4A437554003, NULL);
		L_0->___name = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_0->___name), (void*)L_2);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:33>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_3 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_B);
		String_t* L_4 = ___0_name;
		String_t* L_5;
		L_5 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(L_4, _stringLiteralAB69FA1AB6BB831506EFCAD83900FEE751E85F6F, NULL);
		L_3->___name = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&L_3->___name), (void*)L_5);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:34>
		return;
	}
}
// Method Definition Index: 35202
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RenderTargetBufferSystem_Dispose_m1C3AC5DA450F8B824721316FAC28EBAE5377DBC6 (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, const RuntimeMethod* method) 
{
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* G_B2_0 = NULL;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* G_B1_0 = NULL;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* G_B5_0 = NULL;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* G_B4_0 = NULL;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* G_B8_0 = NULL;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* G_B7_0 = NULL;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* G_B11_0 = NULL;
	RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* G_B10_0 = NULL;
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:38>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_0 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_1 = L_0->___rtMSAA;
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_2 = L_1;
		if (L_2)
		{
			G_B2_0 = L_2;
			goto IL_0011;
		}
		G_B1_0 = L_2;
	}
	{
		goto IL_0016;
	}

IL_0011:
	{
		NullCheck(G_B2_0);
		RTHandle_Release_m743C2A22FD95D177D2D425E9DF1F3088161F387B(G_B2_0, NULL);
	}

IL_0016:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:39>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_3 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_B);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_4 = L_3->___rtMSAA;
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_5 = L_4;
		if (L_5)
		{
			G_B5_0 = L_5;
			goto IL_0027;
		}
		G_B4_0 = L_5;
	}
	{
		goto IL_002c;
	}

IL_0027:
	{
		NullCheck(G_B5_0);
		RTHandle_Release_m743C2A22FD95D177D2D425E9DF1F3088161F387B(G_B5_0, NULL);
	}

IL_002c:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:40>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_6 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_7 = L_6->___rtResolve;
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_8 = L_7;
		if (L_8)
		{
			G_B8_0 = L_8;
			goto IL_003d;
		}
		G_B7_0 = L_8;
	}
	{
		goto IL_0042;
	}

IL_003d:
	{
		NullCheck(G_B8_0);
		RTHandle_Release_m743C2A22FD95D177D2D425E9DF1F3088161F387B(G_B8_0, NULL);
	}

IL_0042:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:41>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_9 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_B);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_10 = L_9->___rtResolve;
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_11 = L_10;
		if (L_11)
		{
			G_B11_0 = L_11;
			goto IL_0052;
		}
		G_B10_0 = L_11;
	}
	{
		return;
	}

IL_0052:
	{
		NullCheck(G_B11_0);
		RTHandle_Release_m743C2A22FD95D177D2D425E9DF1F3088161F387B(G_B11_0, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:42>
		return;
	}
}
// Method Definition Index: 35203
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* RenderTargetBufferSystem_PeekBackBuffer_m5496A9F37497CE9915D760AD5F44FEA5EE304941 (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, const RuntimeMethod* method) 
{
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:46>
		bool L_0 = __this->___m_AllowMSAA;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_1;
		L_1 = RenderTargetBufferSystem_get_backBuffer_mF7305DBD196865D17A0029AC212E8CC02D8205A2(__this, NULL);
		int32_t L_2 = L_1->___msaa;
		if ((((int32_t)L_2) > ((int32_t)1)))
		{
			goto IL_0022;
		}
	}

IL_0016:
	{
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_3;
		L_3 = RenderTargetBufferSystem_get_backBuffer_mF7305DBD196865D17A0029AC212E8CC02D8205A2(__this, NULL);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_4 = L_3->___rtResolve;
		return L_4;
	}

IL_0022:
	{
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_5;
		L_5 = RenderTargetBufferSystem_get_backBuffer_mF7305DBD196865D17A0029AC212E8CC02D8205A2(__this, NULL);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_6 = L_5->___rtMSAA;
		return L_6;
	}
}
// Method Definition Index: 35204
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* RenderTargetBufferSystem_GetBackBuffer_m5783C133D632176EB13AA0B5651723B212AAE3B1 (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* ___0_cmd, const RuntimeMethod* method) 
{
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:51>
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_0 = ___0_cmd;
		RenderTargetBufferSystem_ReAllocate_m21F112E4C9D22893403D2BDC60ED8D41312AE0D9(__this, L_0, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:52>
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_1;
		L_1 = RenderTargetBufferSystem_PeekBackBuffer_m5496A9F37497CE9915D760AD5F44FEA5EE304941(__this, NULL);
		return L_1;
	}
}
// Method Definition Index: 35205
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* RenderTargetBufferSystem_GetFrontBuffer_m85150875CDE3FB4ED1E33FFABD9B9F1893DEA2D1 (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* ___0_cmd, const RuntimeMethod* method) 
{
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:57>
		bool L_0 = __this->___m_AllowMSAA;
		if (L_0)
		{
			goto IL_0022;
		}
	}
	{
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_1;
		L_1 = RenderTargetBufferSystem_get_frontBuffer_mDD8E09139E41F59A682216AD6C0A94AE4387E11A(__this, NULL);
		int32_t L_2 = L_1->___msaa;
		if ((((int32_t)L_2) <= ((int32_t)1)))
		{
			goto IL_0022;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:58>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_3;
		L_3 = RenderTargetBufferSystem_get_frontBuffer_mDD8E09139E41F59A682216AD6C0A94AE4387E11A(__this, NULL);
		L_3->___msaa = 1;
	}

IL_0022:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:60>
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_4 = ___0_cmd;
		RenderTargetBufferSystem_ReAllocate_m21F112E4C9D22893403D2BDC60ED8D41312AE0D9(__this, L_4, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:62>
		bool L_5 = __this->___m_AllowMSAA;
		if (!L_5)
		{
			goto IL_003f;
		}
	}
	{
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_6;
		L_6 = RenderTargetBufferSystem_get_frontBuffer_mDD8E09139E41F59A682216AD6C0A94AE4387E11A(__this, NULL);
		int32_t L_7 = L_6->___msaa;
		if ((((int32_t)L_7) > ((int32_t)1)))
		{
			goto IL_004b;
		}
	}

IL_003f:
	{
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_8;
		L_8 = RenderTargetBufferSystem_get_frontBuffer_mDD8E09139E41F59A682216AD6C0A94AE4387E11A(__this, NULL);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_9 = L_8->___rtResolve;
		return L_9;
	}

IL_004b:
	{
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_10;
		L_10 = RenderTargetBufferSystem_get_frontBuffer_mDD8E09139E41F59A682216AD6C0A94AE4387E11A(__this, NULL);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_11 = L_10->___rtMSAA;
		return L_11;
	}
}
// Method Definition Index: 35206
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RenderTargetBufferSystem_Swap_m3D2279D4D03B17F4BA36717BAB07360C6F2C6D31 (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:67>
		il2cpp_codegen_runtime_class_init_inline(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var);
		bool L_0 = ((RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_StaticFields*)il2cpp_codegen_static_fields_for(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var))->___m_AisBackBuffer;
		((RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_StaticFields*)il2cpp_codegen_static_fields_for(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var))->___m_AisBackBuffer = (bool)((((int32_t)L_0) == ((int32_t)0))? 1 : 0);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:68>
		return;
	}
}
// Method Definition Index: 35207
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RenderTargetBufferSystem_ReAllocate_m21F112E4C9D22893403D2BDC60ED8D41312AE0D9 (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* ___0_cmd, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RenderingUtils_t4E40200449A82FA3A172A563C490DF11FADA2BE1_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:72>
		il2cpp_codegen_runtime_class_init_inline(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var);
		RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46 L_0 = ((RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_StaticFields*)il2cpp_codegen_static_fields_for(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var))->___m_Desc;
		V_0 = L_0;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:74>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_1 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		int32_t L_2 = L_1->___msaa;
		RenderTextureDescriptor_set_msaaSamples_m6910E09489372746391B14FBAF59A7237539D6C4_inline((&V_0), L_2, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:75>
		int32_t L_3;
		L_3 = RenderTextureDescriptor_get_msaaSamples_mFCC33643AFF2265C8305DCFD79ED8774A1A8FA22_inline((&V_0), NULL);
		if ((((int32_t)L_3) <= ((int32_t)1)))
		{
			goto IL_004d;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:76>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_4 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B** L_5 = (RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B**)(&L_4->___rtMSAA);
		int32_t L_6 = __this->___m_FilterMode;
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_7 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		String_t* L_8 = L_7->___name;
		il2cpp_codegen_runtime_class_init_inline(RenderingUtils_t4E40200449A82FA3A172A563C490DF11FADA2BE1_il2cpp_TypeInfo_var);
		bool L_9;
		L_9 = RenderingUtils_ReAllocateHandleIfNeeded_mB2BC0F5A65EFBBD73D29B7C5AA081D84FECED9EF(L_5, (&V_0), L_6, 1, 1, (0.0f), L_8, NULL);
	}

IL_004d:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:78>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_10 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_B);
		int32_t L_11 = L_10->___msaa;
		RenderTextureDescriptor_set_msaaSamples_m6910E09489372746391B14FBAF59A7237539D6C4_inline((&V_0), L_11, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:79>
		int32_t L_12;
		L_12 = RenderTextureDescriptor_get_msaaSamples_mFCC33643AFF2265C8305DCFD79ED8774A1A8FA22_inline((&V_0), NULL);
		if ((((int32_t)L_12) <= ((int32_t)1)))
		{
			goto IL_0094;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:80>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_13 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_B);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B** L_14 = (RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B**)(&L_13->___rtMSAA);
		int32_t L_15 = __this->___m_FilterMode;
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_16 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_B);
		String_t* L_17 = L_16->___name;
		il2cpp_codegen_runtime_class_init_inline(RenderingUtils_t4E40200449A82FA3A172A563C490DF11FADA2BE1_il2cpp_TypeInfo_var);
		bool L_18;
		L_18 = RenderingUtils_ReAllocateHandleIfNeeded_mB2BC0F5A65EFBBD73D29B7C5AA081D84FECED9EF(L_14, (&V_0), L_15, 1, 1, (0.0f), L_17, NULL);
	}

IL_0094:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:82>
		RenderTextureDescriptor_set_msaaSamples_m6910E09489372746391B14FBAF59A7237539D6C4_inline((&V_0), 1, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:83>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_19 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B** L_20 = (RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B**)(&L_19->___rtResolve);
		int32_t L_21 = __this->___m_FilterMode;
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_22 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		String_t* L_23 = L_22->___name;
		il2cpp_codegen_runtime_class_init_inline(RenderingUtils_t4E40200449A82FA3A172A563C490DF11FADA2BE1_il2cpp_TypeInfo_var);
		bool L_24;
		L_24 = RenderingUtils_ReAllocateHandleIfNeeded_mB2BC0F5A65EFBBD73D29B7C5AA081D84FECED9EF(L_20, (&V_0), L_21, 1, 1, (0.0f), L_23, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:84>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_25 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_B);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B** L_26 = (RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B**)(&L_25->___rtResolve);
		int32_t L_27 = __this->___m_FilterMode;
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_28 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_B);
		String_t* L_29 = L_28->___name;
		bool L_30;
		L_30 = RenderingUtils_ReAllocateHandleIfNeeded_mB2BC0F5A65EFBBD73D29B7C5AA081D84FECED9EF(L_26, (&V_0), L_27, 1, 1, (0.0f), L_29, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:85>
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_31 = ___0_cmd;
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_32 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		String_t* L_33 = L_32->___name;
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_34 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_35 = L_34->___rtResolve;
		RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B L_36;
		L_36 = RTHandle_op_Implicit_m2462183372B0496DE475889924EDCAAAD2011B54(L_35, NULL);
		NullCheck(L_31);
		CommandBuffer_SetGlobalTexture_mD6F1CC7E87FA88B5838D5EDAFBA602EF94FE1F69(L_31, L_33, L_36, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:86>
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_37 = ___0_cmd;
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_38 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_B);
		String_t* L_39 = L_38->___name;
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_40 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_B);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_41 = L_40->___rtResolve;
		RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B L_42;
		L_42 = RTHandle_op_Implicit_m2462183372B0496DE475889924EDCAAAD2011B54(L_41, NULL);
		NullCheck(L_37);
		CommandBuffer_SetGlobalTexture_mD6F1CC7E87FA88B5838D5EDAFBA602EF94FE1F69(L_37, L_39, L_42, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:87>
		return;
	}
}
// Method Definition Index: 35208
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RenderTargetBufferSystem_Clear_m6E8586BB1A508B94496610A54EF39BE0A03B431B (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* G_B2_0 = NULL;
	RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* G_B1_0 = NULL;
	int32_t G_B3_0 = 0;
	RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* G_B3_1 = NULL;
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:91>
		il2cpp_codegen_runtime_class_init_inline(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var);
		((RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_StaticFields*)il2cpp_codegen_static_fields_for(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var))->___m_AisBackBuffer = (bool)1;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:92>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_0 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		int32_t L_1 = L_0->___msaa;
		if ((((int32_t)L_1) > ((int32_t)1)))
		{
			G_B2_0 = __this;
			goto IL_0025;
		}
		G_B1_0 = __this;
	}
	{
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_2 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_B);
		int32_t L_3 = L_2->___msaa;
		G_B3_0 = ((((int32_t)L_3) > ((int32_t)1))? 1 : 0);
		G_B3_1 = G_B1_0;
		goto IL_0026;
	}

IL_0025:
	{
		G_B3_0 = 1;
		G_B3_1 = G_B2_0;
	}

IL_0026:
	{
		NullCheck(G_B3_1);
		G_B3_1->___m_AllowMSAA = (bool)G_B3_0;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:93>
		return;
	}
}
// Method Definition Index: 35209
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RenderTargetBufferSystem_SetCameraSettings_m1F65A3121D31191F44E826D47ECBE5279EDC93F8 (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46 ___0_desc, int32_t ___1_filterMode, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:97>
		RenderTextureDescriptor_set_depthStencilFormat_m7EC335006743B59AAD1895AD06B2DD74333F9A03_inline((&___0_desc), 0, NULL);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:98>
		RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46 L_0 = ___0_desc;
		il2cpp_codegen_runtime_class_init_inline(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var);
		((RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_StaticFields*)il2cpp_codegen_static_fields_for(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var))->___m_Desc = L_0;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:99>
		int32_t L_1 = ___1_filterMode;
		__this->___m_FilterMode = L_1;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:101>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_2 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		int32_t L_3;
		L_3 = RenderTextureDescriptor_get_msaaSamples_mFCC33643AFF2265C8305DCFD79ED8774A1A8FA22_inline((&((RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_StaticFields*)il2cpp_codegen_static_fields_for(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var))->___m_Desc), NULL);
		L_2->___msaa = L_3;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:102>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_4 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_B);
		int32_t L_5;
		L_5 = RenderTextureDescriptor_get_msaaSamples_mFCC33643AFF2265C8305DCFD79ED8774A1A8FA22_inline((&((RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_StaticFields*)il2cpp_codegen_static_fields_for(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var))->___m_Desc), NULL);
		L_4->___msaa = L_5;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:104>
		int32_t L_6;
		L_6 = RenderTextureDescriptor_get_msaaSamples_mFCC33643AFF2265C8305DCFD79ED8774A1A8FA22_inline((&((RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_StaticFields*)il2cpp_codegen_static_fields_for(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var))->___m_Desc), NULL);
		if ((((int32_t)L_6) <= ((int32_t)1)))
		{
			goto IL_0053;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:105>
		RenderTargetBufferSystem_EnableMSAA_mFACEC550EEF2910AC94C1F22C0DA146DBE36F3CA(__this, (bool)1, NULL);
	}

IL_0053:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:106>
		return;
	}
}
// Method Definition Index: 35210
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* RenderTargetBufferSystem_GetBufferA_m0E904787365B2DC88C4966E4D5B530B2A3639241 (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, const RuntimeMethod* method) 
{
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:110>
		bool L_0 = __this->___m_AllowMSAA;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_1 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		int32_t L_2 = L_1->___msaa;
		if ((((int32_t)L_2) > ((int32_t)1)))
		{
			goto IL_0022;
		}
	}

IL_0016:
	{
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_3 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_4 = L_3->___rtResolve;
		return L_4;
	}

IL_0022:
	{
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_5 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* L_6 = L_5->___rtMSAA;
		return L_6;
	}
}
// Method Definition Index: 35211
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RenderTargetBufferSystem_EnableMSAA_mFACEC550EEF2910AC94C1F22C0DA146DBE36F3CA (RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13* __this, bool ___0_enable, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:115>
		bool L_0 = ___0_enable;
		__this->___m_AllowMSAA = L_0;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:116>
		bool L_1 = ___0_enable;
		if (!L_1)
		{
			goto IL_0034;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:118>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_2 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_A);
		il2cpp_codegen_runtime_class_init_inline(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var);
		int32_t L_3;
		L_3 = RenderTextureDescriptor_get_msaaSamples_mFCC33643AFF2265C8305DCFD79ED8774A1A8FA22_inline((&((RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_StaticFields*)il2cpp_codegen_static_fields_for(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var))->___m_Desc), NULL);
		L_2->___msaa = L_3;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:119>
		SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4* L_4 = (SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4*)(&__this->___m_B);
		int32_t L_5;
		L_5 = RenderTextureDescriptor_get_msaaSamples_mFCC33643AFF2265C8305DCFD79ED8774A1A8FA22_inline((&((RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_StaticFields*)il2cpp_codegen_static_fields_for(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var))->___m_Desc), NULL);
		L_4->___msaa = L_5;
	}

IL_0034:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:121>
		return;
	}
}
// Method Definition Index: 35212
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RenderTargetBufferSystem__cctor_m43224CB0048305175C0E52072E876BEFD934F869 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/RenderTargetBufferSystem.cs:21>
		((RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_StaticFields*)il2cpp_codegen_static_fields_for(RenderTargetBufferSystem_tB98B680006BB96E6EBC6311583EE31302F16EC13_il2cpp_TypeInfo_var))->___m_AisBackBuffer = (bool)1;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_marshal_pinvoke(const SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4& unmarshaled, SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_marshaled_pinvoke& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4____rtMSAA_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___rtMSAAException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4____rtMSAA_FieldInfo_var, SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___rtMSAAException, NULL);
}
IL2CPP_EXTERN_C void SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_marshal_pinvoke_back(const SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_marshaled_pinvoke& marshaled, SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4____rtMSAA_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___rtMSAAException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4____rtMSAA_FieldInfo_var, SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___rtMSAAException, NULL);
}
IL2CPP_EXTERN_C void SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_marshal_pinvoke_cleanup(SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_marshal_com(const SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4& unmarshaled, SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_marshaled_com& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4____rtMSAA_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___rtMSAAException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4____rtMSAA_FieldInfo_var, SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___rtMSAAException, NULL);
}
IL2CPP_EXTERN_C void SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_marshal_com_back(const SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_marshaled_com& marshaled, SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4____rtMSAA_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___rtMSAAException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4____rtMSAA_FieldInfo_var, SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___rtMSAAException, NULL);
}
IL2CPP_EXTERN_C void SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_marshal_com_cleanup(SwapBuffer_t431F23072C45F1BEE6FF42872627D5393B39A7C4_marshaled_com& marshaled)
{
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
// Method Definition Index: 32863
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ScriptableRenderPass_set_renderPassEvent_m63FA581FFDE1C69C2E1358BD0B8DB30275334960_inline (ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/ScriptableRenderPass.cs:216>
		int32_t L_0 = ___0_value;
		__this->___U3CrenderPassEventU3Ek__BackingField = L_0;
		return;
	}
}
// Method Definition Index: 32988
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool ScriptableRenderer_get_stripShadowsOffVariants_mEC78AA6E4F4353DEF4DA00EB6E2BF7A55CEE322F_inline (ScriptableRenderer_tF15B95BB85F26BE4B4719901D909831B89DC8892* __this, const RuntimeMethod* method) 
{
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/ScriptableRenderer.cs:725>
		bool L_0 = __this->___U3CstripShadowsOffVariantsU3Ek__BackingField;
		return L_0;
	}
}
// Method Definition Index: 32887
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ScriptableRenderPass_set_useNativeRenderPass_m1D60C30BB1CF1B4D383FFCABC1F57EA755626895_inline (ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/ScriptableRenderPass.cs:342>
		bool L_0 = ___0_value;
		__this->___U3CuseNativeRenderPassU3Ek__BackingField = L_0;
		return;
	}
}
// Method Definition Index: 46481
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t RenderTextureDescriptor_get_width_mB159E4EB08B23B19CCCFADB465864361FB840BFF_inline (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CwidthU3Ek__BackingField;
		return L_0;
	}
}
// Method Definition Index: 46483
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t RenderTextureDescriptor_get_height_m1006F9AA45029715C552C8A8C2F102F63D3A91EC_inline (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CheightU3Ek__BackingField;
		return L_0;
	}
}
// Method Definition Index: 46945
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline (Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* __this, float ___0_x, float ___1_y, float ___2_z, float ___3_w, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		float L_2 = ___2_z;
		__this->___z = L_2;
		float L_3 = ___3_w;
		__this->___w = L_3;
		return;
	}
}
// Method Definition Index: 46627
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_black_mB50217951591A045844C61E7FF31EEE3FEF16737_inline (const RuntimeMethod* method) 
{
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0;
		memset((&L_0), 0, sizeof(L_0));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_0), (0.0f), (0.0f), (0.0f), (1.0f), NULL);
		return L_0;
	}
}
// Method Definition Index: 27044
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* CommandBufferHelpers_GetRasterCommandBuffer_m6086D650343F166614B3FB5ED89D63DE8F85C42B_inline (CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* ___0_baseBuffer, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CommandBufferHelpers_tB8C09D01D6F0369DF52DEA955F017D6001BC72E5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.core@5e056b397fc0/Runtime/CommandBuffers/CommandBufferHelpers.cs:24>
		il2cpp_codegen_runtime_class_init_inline(CommandBufferHelpers_tB8C09D01D6F0369DF52DEA955F017D6001BC72E5_il2cpp_TypeInfo_var);
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_0 = ((CommandBufferHelpers_tB8C09D01D6F0369DF52DEA955F017D6001BC72E5_StaticFields*)il2cpp_codegen_static_fields_for(CommandBufferHelpers_tB8C09D01D6F0369DF52DEA955F017D6001BC72E5_il2cpp_TypeInfo_var))->___rasterCmd;
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_1 = ___0_baseBuffer;
		NullCheck(L_0);
		((BaseCommandBuffer_tD67BB9B3F740537BD3F3A96FA17D06E6C3BFDC06*)L_0)->___m_WrappedCommandBuffer = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&((BaseCommandBuffer_tD67BB9B3F740537BD3F3A96FA17D06E6C3BFDC06*)L_0)->___m_WrappedCommandBuffer), (void*)L_1);
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.core@5e056b397fc0/Runtime/CommandBuffers/CommandBufferHelpers.cs:25>
		RasterCommandBuffer_t4300C4F080EA3CF43C7ACFDBD4F94EA633FE98E8* L_2 = ((CommandBufferHelpers_tB8C09D01D6F0369DF52DEA955F017D6001BC72E5_StaticFields*)il2cpp_codegen_static_fields_for(CommandBufferHelpers_tB8C09D01D6F0369DF52DEA955F017D6001BC72E5_il2cpp_TypeInfo_var))->___rasterCmd;
		return L_2;
	}
}
// Method Definition Index: 29762
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B RTHandle_get_nameID_m30AF2567853494DB845D83A8B37D0FB523DA76E9_inline (RTHandle_t135537761C47BC929F032B3C8F4D55EA1111B07B* __this, const RuntimeMethod* method) 
{
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.core@5e056b397fc0/Runtime/Textures/RTHandle.cs:126>
		RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B L_0 = __this->___m_NameID;
		return L_0;
	}
}
// Method Definition Index: 46718
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 Matrix4x4_get_identity_m6568A73831F3E2D587420D20FF423959D7D8AB56_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_0 = ((Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6_StaticFields*)il2cpp_codegen_static_fields_for(Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6_il2cpp_TypeInfo_var))->___identityMatrix;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_1 = V_0;
		return L_1;
	}
}
// Method Definition Index: 32122
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool UniversalRenderPipelineAsset_get_useRenderingLayers_mA473541E634D2A1BEB4CEAFBF27B79251E0FA5E6_inline (UniversalRenderPipelineAsset_tE8A9AA6F030CC3B558CEA2EB54FFF4FC58CA6232* __this, const RuntimeMethod* method) 
{
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Data/UniversalRenderPipelineAsset.cs:1526>
		bool L_0 = __this->___m_SupportsLightLayers;
		return L_0;
	}
}
// Method Definition Index: 32881
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* ScriptableRenderPass_get_passName_m838292A44DB6ED7D67E43C1DE58383959B4F1925_inline (ScriptableRenderPass_tEA38F6C7AD8D111A2251E4C2A7530BCEE7D6D2B0* __this, const RuntimeMethod* method) 
{
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.universal@a2829103b3d4/Runtime/Passes/ScriptableRenderPass.cs:337>
		String_t* L_0 = __this->___m_PassName;
		return L_0;
	}
}
// Method Definition Index: 30832
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RenderGraphDefaultResources_tCE331152C84ED1A36CF186CA0092AE10E599E25D* RenderGraph_get_defaultResources_m9392476073E82DC8F45ED8AB11B271EA471FC206_inline (RenderGraph_t73ECE03B3CA3D0D17F1448D64AE020E81592AA1E* __this, const RuntimeMethod* method) 
{
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.core@5e056b397fc0/Runtime/RenderGraph/RenderGraph.cs:526>
		RenderGraphDefaultResources_tCE331152C84ED1A36CF186CA0092AE10E599E25D* L_0 = __this->___m_DefaultResources;
		return L_0;
	}
}
// Method Definition Index: 31106
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 RenderGraphDefaultResources_get_defaultShadowTexture_mF16D95793225F7D0A560E587401F5638A8A8E913_inline (RenderGraphDefaultResources_tCE331152C84ED1A36CF186CA0092AE10E599E25D* __this, const RuntimeMethod* method) 
{
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.core@5e056b397fc0/Runtime/RenderGraph/RenderGraphDefaultResources.cs:35>
		TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388 L_0 = __this->___U3CdefaultShadowTextureU3Ek__BackingField;
		return L_0;
	}
}
// Method Definition Index: 31452
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool TextureHandle_IsValid_mECFF64B8BAC6402F0D37B67BB79FFB3AB3C7F3C2_inline (TextureHandle_t680ABA3F8B50859351BA5DD66220084F87F37388* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.core@5e056b397fc0/Runtime/RenderGraph/RenderGraphResourceTexture.cs:117>
		ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C* L_0 = (ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C*)(&__this->___handle);
		il2cpp_codegen_runtime_class_init_inline(ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = ResourceHandle_IsValid_m20B0218FDCA98DCD069AE3BE86FEFCAEDB985B9A_inline(L_0, NULL);
		return L_1;
	}
}
// Method Definition Index: 46486
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void RenderTextureDescriptor_set_msaaSamples_m6910E09489372746391B14FBAF59A7237539D6C4_inline (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CmsaaSamplesU3Ek__BackingField = L_0;
		return;
	}
}
// Method Definition Index: 46485
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t RenderTextureDescriptor_get_msaaSamples_mFCC33643AFF2265C8305DCFD79ED8774A1A8FA22_inline (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CmsaaSamplesU3Ek__BackingField;
		return L_0;
	}
}
// Method Definition Index: 46495
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void RenderTextureDescriptor_set_depthStencilFormat_m7EC335006743B59AAD1895AD06B2DD74333F9A03_inline (RenderTextureDescriptor_t69845881CE6437E4E61F92074F2F84079F23FA46* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CdepthStencilFormatU3Ek__BackingField = L_0;
		return;
	}
}
// Method Definition Index: 27865
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* ContextContainer_Get_TisRuntimeObject_mD332AE37F62256B78E48145FFDEADB66FEEF3A5E_gshared_inline (ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* __this, const RuntimeMethod* method) 
{
	il2cpp_rgctx_method_init(method);
	uint32_t V_0 = 0;
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.core@5e056b397fc0/Runtime/Common/ContextContainer.cs:26>
		il2cpp_codegen_runtime_class_init_inline(il2cpp_rgctx_data(method->rgctx_data, 1));
		uint32_t L_0 = ((TypeId_1_tF7C39317892E31289E8C529424E70ED463C8334C_StaticFields*)il2cpp_codegen_static_fields_for(il2cpp_rgctx_data(method->rgctx_data, 1)))->___value;
		V_0 = L_0;
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.core@5e056b397fc0/Runtime/Common/ContextContainer.cs:27>
		uint32_t L_1 = V_0;
		bool L_2;
		L_2 = ContextContainer_Contains_mD38FBF0FAC84169E395802CBF880BD0980490AF3_inline(__this, L_1, NULL);
		if (L_2)
		{
			goto IL_0033;
		}
	}
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.core@5e056b397fc0/Runtime/Common/ContextContainer.cs:29>
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_3 = { reinterpret_cast<intptr_t> (il2cpp_rgctx_type(method->rgctx_data, 2)) };
		il2cpp_codegen_runtime_class_init_inline(il2cpp_defaults.systemtype_class);
		Type_t* L_4;
		L_4 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_3, NULL);
		NullCheck(L_4);
		String_t* L_5;
		L_5 = VirtualFuncInvoker0< String_t* >::Invoke(25, L_4);
		String_t* L_6;
		L_6 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral49AE794C00022ECA141068DEA9531BF6E0D342B7)), L_5, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral01A849374C26F36DBF4641EFCBB31ABDCEFF10D2)), NULL);
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_7 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_7, L_6, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_7, method);
	}

IL_0033:
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.core@5e056b397fc0/Runtime/Common/ContextContainer.cs:32>
		ItemU5BU5D_t4AC8CDDFF9ED634B9ECE154F0C638C00B12B5CCB* L_8 = __this->___m_Items;
		uint32_t L_9 = V_0;
		NullCheck(L_8);
		ContextItem_tFC5ECB349F0410923AE6BC2A9CAACA3589B51086* L_10 = ((L_8)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_9)))->___storage;
		return ((RuntimeObject*)Castclass((RuntimeObject*)L_10, il2cpp_rgctx_data(method->rgctx_data, 3)));
	}
}
// Method Definition Index: 46608
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, float ___0_r, float ___1_g, float ___2_b, float ___3_a, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_r;
		__this->___r = L_0;
		float L_1 = ___1_g;
		__this->___g = L_1;
		float L_2 = ___2_b;
		__this->___b = L_2;
		float L_3 = ___3_a;
		__this->___a = L_3;
		return;
	}
}
// Method Definition Index: 31414
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool ResourceHandle_IsValid_m20B0218FDCA98DCD069AE3BE86FEFCAEDB985B9A_inline (ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	uint32_t V_0 = 0;
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.core@5e056b397fc0/Runtime/RenderGraph/RenderGraphResources.cs:75>
		uint32_t L_0 = __this->___m_Value;
		V_0 = ((int32_t)((int32_t)L_0&((int32_t)-65536)));
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.core@5e056b397fc0/Runtime/RenderGraph/RenderGraphResources.cs:76>
		uint32_t L_1 = V_0;
		if (!L_1)
		{
			goto IL_0023;
		}
	}
	{
		uint32_t L_2 = V_0;
		il2cpp_codegen_runtime_class_init_inline(ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C_il2cpp_TypeInfo_var);
		uint32_t L_3 = ((ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C_StaticFields*)il2cpp_codegen_static_fields_for(ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C_il2cpp_TypeInfo_var))->___s_CurrentValidBit;
		if ((((int32_t)L_2) == ((int32_t)L_3)))
		{
			goto IL_0021;
		}
	}
	{
		uint32_t L_4 = V_0;
		il2cpp_codegen_runtime_class_init_inline(ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C_il2cpp_TypeInfo_var);
		uint32_t L_5 = ((ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C_StaticFields*)il2cpp_codegen_static_fields_for(ResourceHandle_tD3B1FFBD59EB9C23F0A020351836F834C4BD276C_il2cpp_TypeInfo_var))->___s_SharedResourceValidBit;
		return (bool)((((int32_t)L_4) == ((int32_t)L_5))? 1 : 0);
	}

IL_0021:
	{
		return (bool)1;
	}

IL_0023:
	{
		return (bool)0;
	}
}
// Method Definition Index: 27869
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool ContextContainer_Contains_mD38FBF0FAC84169E395802CBF880BD0980490AF3_inline (ContextContainer_t384E08F63FABEFF6A7F1A753F5EF02699DAD414C* __this, uint32_t ___0_typeId, const RuntimeMethod* method) 
{
	{
		//<source_info:./Library/PackageCache/com.unity.render-pipelines.core@5e056b397fc0/Runtime/Common/ContextContainer.cs:108>
		uint32_t L_0 = ___0_typeId;
		ItemU5BU5D_t4AC8CDDFF9ED634B9ECE154F0C638C00B12B5CCB* L_1 = __this->___m_Items;
		NullCheck(L_1);
		if ((((int64_t)((int64_t)(uint64_t)((uint32_t)L_0))) >= ((int64_t)((int64_t)((int32_t)(((RuntimeArray*)L_1)->max_length))))))
		{
			goto IL_001f;
		}
	}
	{
		ItemU5BU5D_t4AC8CDDFF9ED634B9ECE154F0C638C00B12B5CCB* L_2 = __this->___m_Items;
		uint32_t L_3 = ___0_typeId;
		NullCheck(L_2);
		bool L_4 = ((L_2)->GetAddressAt(static_cast<il2cpp_array_size_t>(L_3)))->___isSet;
		return L_4;
	}

IL_001f:
	{
		return (bool)0;
	}
}
