{"logs": [{"outputFile": "com.location.pryze.launcher-mergeReleaseResources-19:/values-be/values-be.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11/transforms/01b0759c45be5acd57b988c62a807a3d/transformed/core-1.9.0/res/values-be/values-be.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2917", "endColumns": "100", "endOffsets": "3013"}}, {"source": "/Users/<USER>/.gradle/caches/8.11/transforms/a776d172d9d24a1128478cf2dc19e9b4/transformed/appcompat-1.6.1/res/values-be/values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}}]}]}