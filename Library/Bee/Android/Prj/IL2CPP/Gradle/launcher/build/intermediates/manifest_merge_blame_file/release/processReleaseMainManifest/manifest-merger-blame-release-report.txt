1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.location.pryze"
4    android:installLocation="preferExternal"
5    android:versionCode="1"
6    android:versionName="0.1.0" >
7
8    <uses-sdk
9        android:minSdkVersion="23"
10        android:targetSdkVersion="36" />
11
12    <supports-screens
12-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:4:3-163
13        android:anyDensity="true"
13-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:4:135-160
14        android:largeScreens="true"
14-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:4:78-105
15        android:normalScreens="true"
15-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:4:49-77
16        android:smallScreens="true"
16-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:4:21-48
17        android:xlargeScreens="true" />
17-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:4:106-134
18
19    <uses-permission android:name="android.permission.INTERNET" />
19-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:8:5-67
19-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:8:22-64
20    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
20-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:9:5-79
20-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:9:22-76
21    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
21-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:10:5-81
21-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:10:22-78
22
23    <uses-feature android:glEsVersion="0x00030000" />
23-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:12:5-54
23-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:12:19-51
24    <uses-feature
24-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:13:5-15:36
25        android:name="android.hardware.vulkan.version"
25-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:14:9-55
26        android:required="false" />
26-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:15:9-33
27    <uses-feature
27-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:16:5-18:36
28        android:name="android.hardware.location.gps"
28-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:17:9-53
29        android:required="false" />
29-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:18:9-33
30    <uses-feature
30-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:19:5-21:36
31        android:name="android.hardware.location"
31-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:20:9-49
32        android:required="false" />
32-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:21:9-33
33    <uses-feature
33-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:22:5-24:36
34        android:name="android.hardware.touchscreen"
34-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:23:9-52
35        android:required="false" />
35-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:24:9-33
36    <uses-feature
36-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:25:5-27:36
37        android:name="android.hardware.touchscreen.multitouch"
37-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:26:9-63
38        android:required="false" />
38-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:27:9-33
39    <uses-feature
39-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:28:5-30:36
40        android:name="android.hardware.touchscreen.multitouch.distinct"
40-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:29:9-72
41        android:required="false" />
41-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:30:9-33
42
43    <permission
43-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.11/transforms/01b0759c45be5acd57b988c62a807a3d/transformed/core-1.9.0/AndroidManifest.xml:22:5-24:47
44        android:name="com.location.pryze.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.11/transforms/01b0759c45be5acd57b988c62a807a3d/transformed/core-1.9.0/AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.11/transforms/01b0759c45be5acd57b988c62a807a3d/transformed/core-1.9.0/AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.location.pryze.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.11/transforms/01b0759c45be5acd57b988c62a807a3d/transformed/core-1.9.0/AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.11/transforms/01b0759c45be5acd57b988c62a807a3d/transformed/core-1.9.0/AndroidManifest.xml:26:22-94
48
49    <application
49-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:3:3-83
50        android:appCategory="game"
50-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:33:9-35
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.9.0] /Users/<USER>/.gradle/caches/8.11/transforms/01b0759c45be5acd57b988c62a807a3d/transformed/core-1.9.0/AndroidManifest.xml:28:18-86
52        android:enableOnBackInvokedCallback="true"
52-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:34:9-51
53        android:extractNativeLibs="true"
54        android:icon="@mipmap/app_icon"
54-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:3:16-47
55        android:label="@string/app_name" >
55-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml:3:48-80
56        <meta-data
56-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:36:9-38:33
57            android:name="unity.splash-mode"
57-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:37:13-45
58            android:value="0" />
58-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:38:13-30
59        <meta-data
59-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:39:9-41:36
60            android:name="unity.splash-enable"
60-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:40:13-47
61            android:value="True" />
61-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:41:13-33
62        <meta-data
62-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:42:9-44:36
63            android:name="unity.launch-fullscreen"
63-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:43:13-51
64            android:value="True" />
64-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:44:13-33
65        <meta-data
65-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:45:9-47:36
66            android:name="unity.render-outside-safearea"
66-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:46:13-57
67            android:value="True" />
67-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:47:13-33
68        <meta-data
68-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:48:9-50:50
69            android:name="notch.config"
69-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:49:13-40
70            android:value="portrait|landscape" />
70-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:50:13-47
71        <meta-data
71-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:51:9-53:36
72            android:name="unity.auto-report-fully-drawn"
72-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:52:13-57
73            android:value="true" />
73-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:53:13-33
74        <meta-data
74-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:54:9-56:36
75            android:name="unity.strip-engine-code"
75-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:55:13-51
76            android:value="true" />
76-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:56:13-33
77        <meta-data
77-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:57:9-59:36
78            android:name="unity.auto-set-game-state"
78-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:58:13-53
79            android:value="true" />
79-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:59:13-33
80
81        <activity
81-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:61:9-96:20
82            android:name="com.unity3d.player.UnityPlayerGameActivity"
82-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:62:13-70
83            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
83-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:63:13-194
84            android:enabled="true"
84-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:64:13-35
85            android:exported="true"
85-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:65:13-36
86            android:hardwareAccelerated="false"
86-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:66:13-48
87            android:launchMode="singleTask"
87-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:67:13-44
88            android:resizeableActivity="true"
88-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:68:13-46
89            android:screenOrientation="fullUser"
89-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:69:13-49
90            android:theme="@style/BaseUnityGameActivityTheme" >
90-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:70:13-62
91            <intent-filter>
91-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:71:13-75:29
92                <category android:name="android.intent.category.LAUNCHER" />
92-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:72:17-77
92-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:72:27-74
93
94                <action android:name="android.intent.action.MAIN" />
94-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:74:17-69
94-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:74:25-66
95            </intent-filter>
96
97            <meta-data
97-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:77:13-79:40
98                android:name="unityplayer.UnityActivity"
98-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:78:17-57
99                android:value="true" />
99-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:79:17-37
100            <meta-data
100-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:80:13-82:40
101                android:name="android.app.lib_name"
101-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:81:17-52
102                android:value="game" />
102-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:82:17-37
103            <meta-data
103-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:83:13-85:71
104                android:name="WindowManagerPreference:FreeformWindowSize"
104-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:84:17-74
105                android:value="@string/FreeformWindowSize_maximize" />
105-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:85:17-68
106            <meta-data
106-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:86:13-88:79
107                android:name="WindowManagerPreference:FreeformWindowOrientation"
107-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:87:17-81
108                android:value="@string/FreeformWindowOrientation_landscape" />
108-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:88:17-76
109            <meta-data
109-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:89:13-91:40
110                android:name="notch_support"
110-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:90:17-45
111                android:value="true" />
111-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:91:17-37
112
113            <layout
113-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:93:13-95:44
114                android:minHeight="300px"
114-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:94:17-42
115                android:minWidth="400px" />
115-->[:unityLibrary] /Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:95:17-41
116        </activity>
117
118        <provider
118-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11/transforms/506e0923b2edf699a568247bbabd40eb/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
119            android:name="androidx.startup.InitializationProvider"
119-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11/transforms/506e0923b2edf699a568247bbabd40eb/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:25:13-67
120            android:authorities="com.location.pryze.androidx-startup"
120-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11/transforms/506e0923b2edf699a568247bbabd40eb/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:26:13-68
121            android:exported="false" >
121-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11/transforms/506e0923b2edf699a568247bbabd40eb/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:27:13-37
122            <meta-data
122-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11/transforms/506e0923b2edf699a568247bbabd40eb/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
123                android:name="androidx.emoji2.text.EmojiCompatInitializer"
123-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11/transforms/506e0923b2edf699a568247bbabd40eb/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
124                android:value="androidx.startup" />
124-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11/transforms/506e0923b2edf699a568247bbabd40eb/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
125            <meta-data
125-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.11/transforms/2be88b33c7c135c3fb5a1d54448b4c97/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:31:13-33:52
126                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
126-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.11/transforms/2be88b33c7c135c3fb5a1d54448b4c97/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:32:17-78
127                android:value="androidx.startup" />
127-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.11/transforms/2be88b33c7c135c3fb5a1d54448b4c97/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:33:17-49
128        </provider>
129    </application>
130
131</manifest>
