<variant
    name="release"
    package="com.location.pryze"
    minSdkVersion="23"
    targetSdkVersion="36"
    mergedManifest="build/intermediates/merged_manifest/release/processReleaseMainManifest/AndroidManifest.xml"
    proguardFiles="build/intermediates/default_proguard_files/global/proguard-android.txt-8.7.2"
    partialResultsDir="build/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out"
    desugaredMethodsFiles="/Users/<USER>/.gradle/caches/8.11/transforms/c65cacf2deeed601588801b53879e513/transformed/D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src/main/AndroidManifest.xml"
        javaDirectories="src/main/java:src/release/java:src/main/kotlin:src/release/kotlin"
        resDirectories="src/main/res:src/release/res"
        assetsDirectories="src/main/assets:src/release/assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="build/intermediates/javac/release/compileReleaseJavaWithJavac/classes:build/intermediates/compile_and_runtime_not_namespaced_r_class_jar/release/processReleaseResources/R.jar"
      type="MAIN"
      applicationId="com.location.pryze"
      generatedSourceFolders="build/generated/ap_generated_sources/release/out"
      generatedResourceFolders="build/generated/res/resValues/release"
      desugaredMethodsFiles="/Users/<USER>/.gradle/caches/8.11/transforms/c65cacf2deeed601588801b53879e513/transformed/D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
