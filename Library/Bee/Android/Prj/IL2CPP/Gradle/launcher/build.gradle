apply plugin: 'com.android.application'
apply from: 'setupSymbols.gradle'
apply from: '../shared/keepUnitySymbols.gradle'
apply from: '../shared/common.gradle'

dependencies {
    implementation project(':unityLibrary')
}

android {
    namespace "com.location.pryze"
    ndkPath "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK"
    ndkVersion "27.2.12479018"
    compileSdk 36
    buildToolsVersion = "34.0.0"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    defaultConfig {
        applicationId "com.location.pryze"
        versionName "0.1.0"
        minSdk 23
        targetSdk 36
        versionCode 1

        ndk {
            abiFilters "arm64-v8a"
            debugSymbolLevel "none"
        }
    }

    lint {
        abortOnError false
    }

    androidResources {
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"
        noCompress = ['.unity3d', '.ress', '.resource', '.obb', '.bundle', '.unityexp'] + unityStreamingAssets.tokenize(', ')
    }

    packaging {
        jniLibs {
            useLegacyPackaging true
        }
    }

    buildTypes {
        debug {
            minifyEnabled = false
            proguardFiles getDefaultProguardFile('proguard-android.txt')
            jniDebuggable = true
            signingConfig signingConfigs.debug
        }

        release {
            minifyEnabled = false
            proguardFiles getDefaultProguardFile('proguard-android.txt')
            signingConfig signingConfigs.debug
        }
    }

    bundle {
        language {
            enableSplit = false
        }

        density {
            enableSplit = false
        }

        abi {
            enableSplit = true
        }

        texture {
            enableSplit = true
        }
    }
}