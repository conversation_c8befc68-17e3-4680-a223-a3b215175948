1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    xmlns:tools="http://schemas.android.com/tools"
4    package="com.unity3d.player" >
5
6    <uses-sdk android:minSdkVersion="23" />
7
8    <uses-permission android:name="android.permission.INTERNET" />
8-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:3:3-65
8-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:3:20-62
9    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
9-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:4:3-77
9-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:4:20-74
10    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
10-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:5:3-79
10-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:5:20-76
11
12    <uses-feature android:glEsVersion="0x00030000" />
12-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:6:3-52
12-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:6:17-49
13    <uses-feature
13-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:7:3-91
14        android:name="android.hardware.vulkan.version"
14-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:7:17-63
15        android:required="false" />
15-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:7:64-88
16    <uses-feature
16-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:8:3-89
17        android:name="android.hardware.location.gps"
17-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:8:17-61
18        android:required="false" />
18-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:8:62-86
19    <uses-feature
19-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:9:3-85
20        android:name="android.hardware.location"
20-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:9:17-57
21        android:required="false" />
21-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:9:58-82
22    <uses-feature
22-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:10:3-88
23        android:name="android.hardware.touchscreen"
23-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:10:17-60
24        android:required="false" />
24-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:10:61-85
25    <uses-feature
25-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:11:3-99
26        android:name="android.hardware.touchscreen.multitouch"
26-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:11:17-71
27        android:required="false" />
27-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:11:72-96
28    <uses-feature
28-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:12:3-108
29        android:name="android.hardware.touchscreen.multitouch.distinct"
29-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:12:17-80
30        android:required="false" />
30-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:12:81-105
31
32    <application
32-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:13:3-34:17
33        android:appCategory="game"
33-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:13:16-42
34        android:enableOnBackInvokedCallback="true"
34-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:13:43-85
35        android:extractNativeLibs="true" >
35-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:13:86-118
36        <meta-data
36-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:14:5-69
37            android:name="unity.splash-mode"
37-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:14:16-48
38            android:value="0" />
38-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:14:49-66
39        <meta-data
39-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:15:5-74
40            android:name="unity.splash-enable"
40-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:15:16-50
41            android:value="True" />
41-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:15:51-71
42        <meta-data
42-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:16:5-78
43            android:name="unity.launch-fullscreen"
43-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:16:16-54
44            android:value="True" />
44-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:16:55-75
45        <meta-data
45-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:17:5-84
46            android:name="unity.render-outside-safearea"
46-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:17:16-60
47            android:value="True" />
47-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:17:61-81
48        <meta-data
48-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:18:5-81
49            android:name="notch.config"
49-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:18:16-43
50            android:value="portrait|landscape" />
50-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:18:44-78
51        <meta-data
51-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:19:5-84
52            android:name="unity.auto-report-fully-drawn"
52-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:19:16-60
53            android:value="true" />
53-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:19:61-81
54        <meta-data
54-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:20:5-78
55            android:name="unity.strip-engine-code"
55-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:20:16-54
56            android:value="true" />
56-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:20:55-75
57        <meta-data
57-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:21:5-80
58            android:name="unity.auto-set-game-state"
58-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:21:16-56
59            android:value="true" />
59-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:21:57-77
60
61        <activity
61-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:22:5-33:16
62            android:name="com.unity3d.player.UnityPlayerGameActivity"
62-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:22:312-369
63            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
63-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:22:15-196
64            android:enabled="true"
64-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:22:197-219
65            android:exported="true"
65-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:22:220-243
66            android:hardwareAccelerated="false"
66-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:22:244-279
67            android:launchMode="singleTask"
67-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:22:280-311
68            android:resizeableActivity="true"
68-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:22:370-403
69            android:screenOrientation="fullUser"
69-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:22:404-440
70            android:theme="@style/BaseUnityGameActivityTheme" >
70-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:22:441-490
71            <intent-filter>
71-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:23:7-26:23
72                <category android:name="android.intent.category.LAUNCHER" />
72-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:24:9-69
72-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:24:19-66
73
74                <action android:name="android.intent.action.MAIN" />
74-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:25:9-61
74-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:25:17-58
75            </intent-filter>
76
77            <meta-data
77-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:27:7-82
78                android:name="unityplayer.UnityActivity"
78-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:27:18-58
79                android:value="true" />
79-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:27:59-79
80            <meta-data
80-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:28:7-77
81                android:name="android.app.lib_name"
81-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:28:18-53
82                android:value="game" />
82-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:28:54-74
83            <meta-data
83-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:29:7-130
84                android:name="WindowManagerPreference:FreeformWindowSize"
84-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:29:18-75
85                android:value="@string/FreeformWindowSize_maximize" />
85-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:29:76-127
86            <meta-data
86-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:30:7-145
87                android:name="WindowManagerPreference:FreeformWindowOrientation"
87-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:30:18-82
88                android:value="@string/FreeformWindowOrientation_landscape" />
88-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:30:83-142
89            <meta-data
89-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:31:7-70
90                android:name="notch_support"
90-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:31:18-46
91                android:value="true" />
91-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:31:47-67
92
93            <layout
93-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:32:7-68
94                android:minHeight="300px"
94-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:32:15-40
95                android:minWidth="400px" />
95-->/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml:32:41-65
96        </activity>
97    </application>
98
99</manifest>
