# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 78ms
  generate-prefab-packages
    [gap of 18ms]
    exec-prefab 1982ms
    [gap of 88ms]
  generate-prefab-packages completed in 2088ms
  execute-generate-process
    exec-configure 294ms
    [gap of 644ms]
  execute-generate-process completed in 942ms
  [gap of 51ms]
  remove-unexpected-so-files 39ms
  [gap of 74ms]
  write-metadata-json-to-file 51ms
generate_cxx_metadata completed in 3343ms

