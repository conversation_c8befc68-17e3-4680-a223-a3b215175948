{"AndroidPlayerBuildProgram.Actions.GenerateManifests+Arguments": {"Configuration": {"TargetSDKVersion": 36, "LauncherManifestTemplatePath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Apk/LauncherManifest.xml", "LauncherManifestTemplateUsed": false, "LibraryManifestTemplatePath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Apk/UnityManifest.xml", "LibraryManifestCustomTemplateUsed": false, "LauncherManifestPath": "launcher/src/main/AndroidManifest.xml", "LibraryManifestPath": "unityLibrary/src/main/AndroidManifest.xml", "TVCompatibility": false, "AppCategory": "game", "BannerEnabled": true, "PreferredInstallLocation": "PreferExternal", "TextureSupport": "Generic", "GamepadSupportLevel": "SupportsDPad", "SupportedAspectRatioMode": 1, "MaxAspectRatio": 2.4, "MinAspectRatio": 1, "ForceInternetPermission": false, "UseLowAccuracyLocation": false, "ForceSDCardPermission": false, "PreserveFramebufferAlpha": false, "DefaultInterfaceOrientation": "AutoRotation", "AllowedAutorotateToPortrait": true, "AllowedAutorotateToPortraitUpsideDown": true, "AllowedAutorotateToLandscapeLeft": true, "AllowedAutorotateToLandscapeRight": true, "SplashScreenScale": "Center", "RenderOutsideSafeArea": true, "GraphicsDevices": ["Vulkan", "OpenGLES3"], "OpenGLRequireES31": false, "OpenGLRequireES31AEP": false, "OpenGLRequireES32": false, "StartInFullscreen": true, "DefaultWindowWidth": 1920, "DefaultWindowHeight": 1080, "MinimumWindowWidth": 400, "MinimumWindowHeight": 300, "ResizeableActivity": true, "FullScreenMode": "FullScreenWindow", "AutoRotationBehavior": "User", "StripEngineCode": true, "ApplicationEntry": "GameActivity", "JavaFileNames": ["UnityPlayerGameActivity.java"], "EnableOnBackInvokedCallback": true}, "Services": {"EnableUnityConnect": true, "EnablePerformanceReporting": false, "EnableAnalytics": false, "EnableCrashReporting": true, "EnableInsights": true}, "ProjectPath": "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle", "ArtifactsPath": "Library/Bee/artifacts/Android/Manifest", "FeatureChecklist": ["Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.Burst-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.Burst.Unsafe-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.Collections-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.Collections.LowLevel.ILSupport-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.Mathematics-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.RenderPipeline.Universal.ShaderLibrary-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime.Shared-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.GPUDriven.Runtime-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Universal.Runtime-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityConsentModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.VFXModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt"], "Development": false, "UsingObb": false, "UsingMTE": false, "GradleResourcesInformation": {"TargetSDKVersion": 36, "RoundIconsAvailable": false, "RoundIconsSupported": true, "AdaptiveIconsSupported": true, "AdaptiveIconsAvailable": false}, "LauncherManifestDiagnosticsPath": "Library/Bee/artifacts/Android/Manifest/IntermediateLauncherManifestDiag.txt", "LibraryManifestDiagnosticsPath": "Library/Bee/artifacts/Android/Manifest/IntermediateLibraryManifestDiag.txt", "APIRequiringInternetPermission": ["UnityEngine.Networking", "System.Net.Sockets", "System.Net.WebRequest", "UnityEngine.Ping", "UnityEngine.Networking.UnityWebRequest"]}, "Bee.TundraBackend.CSharpActionInvocationInformation": {"typeFullName": "AndroidPlayerBuildProgram.Actions.GenerateManifests", "methodName": "Run", "assemblyLocation": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/AndroidPlayerBuildProgram.exe", "targets": ["Library/Bee/artifacts/Android/Manifest/IntermediateLauncherManifestDiag.txt", "Library/Bee/artifacts/Android/Manifest/IntermediateLibraryManifestDiag.txt", "Library/Bee/artifacts/Android/Manifest/launcher/src/main/AndroidManifest.xml", "Library/Bee/artifacts/Android/Manifest/unityLibrary/src/main/AndroidManifest.xml"], "inputs": ["Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.Burst-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.Burst.Unsafe-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.Collections-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.Collections.LowLevel.ILSupport-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.Mathematics-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.RenderPipeline.Universal.ShaderLibrary-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Core.Runtime.Shared-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.GPUDriven.Runtime-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.RenderPipelines.Universal.Runtime-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityConsentModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.VFXModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Apk/LauncherManifest.xml", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Apk/UnityManifest.xml"], "targetDirectories": []}}