{"AndroidPlayerBuildProgram.Actions.ActionGenerateProjectFiles+Arguments": {"ProjectPath": "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle", "Architectures": "ARM64", "BuildSystem": "<PERSON><PERSON><PERSON>", "GradleProjectCreateInfo": {"ArtifactsPath": "Library/Bee/artifacts/Android", "EnvironmentVariableInputs": ["UNITY_THISISABUILDMACHINE:"], "HostPlatform": "OSX", "ApplicationType": "APK", "BuildType": "Release", "AndroidSDKPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/SDK", "AndroidNDKPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK", "AndroidJavaPath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/OpenJDK", "PreferredHeapSizeForJVM": 4096, "GradleVersion": "8.11", "ProjectFiles": {"UnityLibraryBuildGradle": {"RelativeDestinationPath": "unityLibrary/build.gradle", "CanBeModifiedByUser": true}, "LauncherBuildGradle": {"RelativeDestinationPath": "launcher/build.gradle", "CanBeModifiedByUser": true}, "LauncherSetupUnitySymbolsGradle": {"SourcePath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/launcher/setupSymbols.gradle", "RelativeDestinationPath": "launcher/setupSymbols.gradle", "CanBeModifiedByUser": false}, "SharedKeepUnitySymbolsGradle": {"SourcePath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/shared/keepUnitySymbols.gradle", "RelativeDestinationPath": "shared/keepUnitySymbols.gradle", "CanBeModifiedByUser": false}, "SharedCommonGradle": {"SourcePath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/shared/common.gradle", "RelativeDestinationPath": "shared/common.gradle", "CanBeModifiedByUser": false}, "ProjectLevelBuildGradle": {"RelativeDestinationPath": "build.gradle", "CanBeModifiedByUser": true}, "GradleProperties": {"RelativeDestinationPath": "gradle.properties", "CanBeModifiedByUser": true}, "UnityProguard": {"SourcePath": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/UnityProGuardTemplate.txt", "RelativeDestinationPath": "unityLibrary/proguard-unity.txt", "CanBeModifiedByUser": true}, "ProguardUser": {"RelativeDestinationPath": "unityLibrary/proguard-user.txt", "CanBeModifiedByUser": true}, "GradleSettings": {"RelativeDestinationPath": "settings.gradle", "CanBeModifiedByUser": true}, "LocalProperties": {"RelativeDestinationPath": "local.properties", "CanBeModifiedByUser": true}}, "AdditionalLibrariesRelativePaths": [], "AdditionalUserInputs": [], "AdditionalUserOutputs": {"AdditionalManifests": [], "AdditionalBuildGradleFiles": [], "AdditionalGradleSettings": [], "AdditionalGradleProperties": [], "AdditionalFilesWithContents": []}, "UserCopyData": {"FilesToCopy": [], "DirectoriesToCopy": []}, "AdditionalUserData": [], "BuildTools": "34.0.0", "TargetSDKVersion": 36, "MinSDKVersion": 23, "PackageName": "com.location.pryze", "Architectures": "ARM64", "BuildApkPerCpuArchitecture": false, "DebugSymbols": {"Level": "None", "Format": "5"}, "VersionCode": 1, "VersionName": "0.1.0", "Minify": 1, "NoCompressOptions": {"RelativeFilePaths": [], "FileExtensions": []}, "UseCustomKeystore": false, "KeystorePath": "", "KeystoreName": "", "KeystorePassword": "", "KeystoreAliasName": "", "KeystoreAliasPassword": "", "ScriptingImplementation": "IL2CPP", "AndroidLibraries": [], "AARFiles": [], "BuiltinJavaSourcePaths": ["com/unity3d/player/UnityPlayerGameActivity.java"], "JavaSourcePaths": [], "KotlinSourcePaths": [], "PlayerPackage": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer", "PlayerPackageTools": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools", "SymlinkSources": false, "InstallIntoBuildsFolder": false, "UnityPath": "", "UnityProjectPath": "/Users/<USER>/Desktop/LocationService", "OverrideCMakeIntermdiateDirectory": true, "Dependencies": [], "ApplicationEntry": "GameActivity", "JarFiles": ["classes.jar"], "UseOptimizedFramePacing": false, "ReportGooglePlayAppDependencies": false, "UnityVersion": "6000.2.2f1"}}, "Bee.TundraBackend.CSharpActionInvocationInformation": {"typeFullName": "AndroidPlayerBuildProgram.Actions.ActionGenerateProjectFiles", "methodName": "Run", "assemblyLocation": "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/AndroidPlayerBuildProgram.exe", "targets": ["Library/Bee/artifacts/Android/IntermediateFiles.txt", "Library/Bee/artifacts/Android/Gradle/build.gradle", "Library/Bee/artifacts/Android/Gradle/unityLibrary/build.gradle", "Library/Bee/artifacts/Android/Gradle/launcher/build.gradle", "Library/Bee/artifacts/Android/Gradle/gradle.properties", "Library/Bee/artifacts/Android/Gradle/local.properties", "Library/Bee/artifacts/Android/Gradle/settings.gradle", "Library/Bee/artifacts/Android/Gradle/build.gradle.xml", "Library/Bee/artifacts/Android/Gradle/unityLibrary/build.gradle.xml", "Library/Bee/artifacts/Android/Gradle/launcher/build.gradle.xml", "Library/Bee/artifacts/Android/Gradle/gradle.properties.xml", "Library/Bee/artifacts/Android/Gradle/local.properties.xml", "Library/Bee/artifacts/Android/Gradle/settings.gradle.xml", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/setupSymbols.gradle", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/shared/keepUnitySymbols.gradle", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/shared/common.gradle", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/CMakeLists.txt", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/GAToUnityCallbacks.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/MacroEnd.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/MacroHeaderBegin.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/MacroSourceBegin.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAApplication.cpp", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAApplication.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAConfiguration.cpp", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAConfiguration.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGADebug.cpp", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGADebug.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAEntry.cpp", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAEvents.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInput.cpp", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInput.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInputKeyEvent.cpp", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAInputMotionEvent.cpp", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGASoftKeyboard.cpp", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGASoftKeyboard.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGASoftKeyboardCallbacks.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGATypes.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UGAVersion.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGACallbacks.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGAConfigurationCallbacks.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGAKeyEventCallbacks.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/GameActivity/UnityToGAMotionEventCallbacks.h", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/FramePacing/CMakeLists.txt", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/CMakeLists.txt", "Library/Bee/artifacts/Android/Gradle/unityLibrary/proguard-unity.txt", "/Users/<USER>/Desktop/LocationService/Library/Bee/Android/Prj/IL2CPP/Gradle/gradle/wrapper/gradle-wrapper.properties"], "inputs": ["/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/launcher/setupSymbols.gradle", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/shared/keepUnitySymbols.gradle", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/shared/common.gradle", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Tools/UnityProGuardTemplate.txt", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/CMakeLists.txt", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/GAToUnityCallbacks.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/MacroEnd.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/MacroHeaderBegin.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/MacroSourceBegin.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/ReadMe.txt", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAApplication.cpp", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAApplication.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAConfiguration.cpp", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAConfiguration.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGADebug.cpp", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGADebug.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAEntry.cpp", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAEvents.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInput.cpp", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInput.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInputKeyEvent.cpp", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAInputMotionEvent.cpp", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGASoftKeyboard.cpp", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGASoftKeyboard.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGASoftKeyboardCallbacks.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGATypes.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UGAVersion.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGACallbacks.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGAConfigurationCallbacks.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGAKeyEventCallbacks.h", "/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/Source/GameActivity/UnityToGAMotionEventCallbacks.h"], "targetDirectories": []}}