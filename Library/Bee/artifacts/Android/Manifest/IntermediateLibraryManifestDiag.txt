Using template 'Assets/Plugins/Android/AndroidManifest.xml'

android.hardware.vulkan.version was added because:
	One of the graphics devices was Vulkan
android.permission.INTERNET was added because:
	UnityEngine.Networking was present in:
		Unity.InputSystem-FeaturesChecked.txt
	Crash Reporting
	App Insights
Location related settings were added because:
	UnityEngine.Input::get_location was present in:
		Assembly-CSharp-FeaturesChecked.txt
Unity is trying to add element uses-permission#android.permission.ACCESS_FINE_LOCATION but it is already declared by the user in Assets/Plugins/Android/AndroidManifest.xml.
Unity is trying to add element uses-permission#android.permission.ACCESS_COARSE_LOCATION but it is already declared by the user in Assets/Plugins/Android/AndroidManifest.xml.
android.hardware.touchscreen.multitouch, android.hardware.touchscreen.multitouch.distinct were enabled because:
	UnityEngine.Input::GetTouch was present in:
		UnityEngine.UI-FeaturesChecked.txt
	UnityEngine.Input::get_touchCount was present in:
		UnityEngine.UI-FeaturesChecked.txt
