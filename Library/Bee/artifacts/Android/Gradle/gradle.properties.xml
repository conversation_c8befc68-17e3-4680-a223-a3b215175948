<root name="" element-id="228" assign-type="None" type="Unity.Android.Gradle.GradlePropertiesFile" base-type="Unity.Android.Gradle.BaseBlock" is-default="false" dependencies="">
  <element name="org.gradle.jvmargs" element-id="229" assign-type="EqualsNoSpaces" property-value="-Xmx4096M" type="Unity.Android.Gradle.Property`1" base-type="BaseProperty" is-default="true" dependencies="" />
  <element name="org.gradle.parallel" element-id="230" assign-type="EqualsNoSpaces" property-value="True" type="Unity.Android.Gradle.PropertyBoolean" base-type="BaseProperty" is-default="true" dependencies="" />
  <element name="unityStreamingAssets" element-id="231" assign-type="EqualsNoSpaces" property-value="" type="Unity.Android.Gradle.Property`1" base-type="BaseProperty" is-default="true" dependencies="" />
  <element name="" element-id="234" assign-type="None" raw-value="unityTemplateVersion=20" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="235" assign-type="None" raw-value="unityProjectPath=/Users/<USER>/Desktop/LocationService" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="236" assign-type="None" raw-value="unity.projectPath=/Users/<USER>/Desktop/LocationService" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="237" assign-type="None" raw-value="unity.debugSymbolLevel=none" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="238" assign-type="None" raw-value="unity.buildToolsVersion=34.0.0" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="239" assign-type="None" raw-value="unity.minSdkVersion=23" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="240" assign-type="None" raw-value="unity.targetSdkVersion=36" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="241" assign-type="None" raw-value="unity.compileSdkVersion=36" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="242" assign-type="None" raw-value="unity.applicationId=com.location.pryze" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="243" assign-type="None" raw-value="unity.abiFilters=arm64-v8a" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="244" assign-type="None" raw-value="unity.versionCode=1" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="245" assign-type="None" raw-value="unity.versionName=0.1.0" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="246" assign-type="None" raw-value="unity.namespace=com.location.pryze" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="247" assign-type="None" raw-value="unity.agpVersion=8.7.2" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="248" assign-type="None" raw-value="unity.androidSdkPath=/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/SDK" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="249" assign-type="None" raw-value="unity.androidNdkPath=/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="250" assign-type="None" raw-value="unity.androidNdkVersion=27.2.12479018" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="251" assign-type="None" raw-value="unity.jdkPath=/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/OpenJDK" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="252" assign-type="None" raw-value="unity.javaCompatabilityVersion=VERSION_17" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="253" assign-type="None" raw-value="unity.installInBuildFolder=false" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="254" assign-type="None" raw-value="android.useAndroidX=true" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="255" assign-type="None" raw-value="android.enableJetifier=true" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="256" assign-type="None" raw-value="android.bundle.includeNativeDebugMetadata=false" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  <element name="" element-id="257" assign-type="None" raw-value="org.gradle.welcome=never" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
</root>