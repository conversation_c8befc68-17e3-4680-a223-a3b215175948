<root name="" element-id="112" assign-type="None" type="Unity.Android.Gradle.ModuleBuildGradleFile" base-type="Unity.Android.Gradle.BaseBlock" is-default="false" dependencies="">
  <element name="" element-id="113" assign-type="None" type="Unity.Android.Gradle.ApplyPluginList" base-type="Unity.Android.Gradle.BaseBlock" is-default="true" dependencies="">
    <element name="" element-id="197" assign-type="None" raw-value="apply plugin: 'com.android.application'" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
    <element name="" element-id="198" assign-type="None" raw-value="apply from: 'setupSymbols.gradle'" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
    <element name="" element-id="199" assign-type="None" raw-value="apply from: '../shared/keepUnitySymbols.gradle'" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
    <element name="" element-id="200" assign-type="None" raw-value="apply from: '../shared/common.gradle'" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  </element>
  <element name="dependencies" element-id="114" assign-type="None" type="Unity.Android.Gradle.Dependencies" base-type="Unity.Android.Gradle.BaseBlock" is-default="true" dependencies="113">
    <element name="" element-id="201" assign-type="None" raw-value="implementation project(':unityLibrary')" type="Unity.Android.Gradle.Element" base-type="Unity.Android.Gradle.Element" is-default="false" dependencies="" />
  </element>
  <element name="android" element-id="116" assign-type="None" type="Unity.Android.Gradle.Android" base-type="Unity.Android.Gradle.BaseBlock" is-default="true" dependencies="113">
    <element name="namespace" element-id="117" assign-type="None" property-value="com.location.pryze" type="Unity.Android.Gradle.PropertyString" base-type="BaseProperty" is-default="true" dependencies="" />
    <element name="ndkPath" element-id="119" assign-type="None" property-value="/Applications/Unity/Hub/Editor/6000.2.2f1/PlaybackEngines/AndroidPlayer/NDK" type="Unity.Android.Gradle.PropertyString" base-type="BaseProperty" is-default="true" dependencies="" />
    <element name="ndkVersion" element-id="120" assign-type="None" property-value="27.2.12479018" type="Unity.Android.Gradle.PropertyString" base-type="BaseProperty" is-default="true" dependencies="" />
    <element name="compileSdk" element-id="121" assign-type="None" property-value="36" type="Unity.Android.Gradle.PropertyUnsignedInteger" base-type="BaseProperty" is-default="true" dependencies="" />
    <element name="buildToolsVersion" element-id="122" assign-type="Equals" property-value="34.0.0" type="Unity.Android.Gradle.PropertyString" base-type="BaseProperty" is-default="true" dependencies="" />
    <element name="compileOptions" element-id="124" assign-type="None" type="Unity.Android.Gradle.CompileOptions" base-type="Unity.Android.Gradle.BaseBlock" is-default="true" dependencies="">
      <element name="sourceCompatibility" element-id="125" assign-type="None" property-value="Version_17" type="Unity.Android.Gradle.PropertyEnum`1" base-type="BaseProperty" is-default="true" dependencies="" />
      <element name="targetCompatibility" element-id="126" assign-type="None" property-value="Version_17" type="Unity.Android.Gradle.PropertyEnum`1" base-type="BaseProperty" is-default="true" dependencies="" />
    </element>
    <element name="defaultConfig" element-id="127" assign-type="None" type="Unity.Android.Gradle.Flavor" base-type="Unity.Android.Gradle.BaseBlock" is-default="true" dependencies="">
      <element name="applicationId" element-id="128" assign-type="None" property-value="com.location.pryze" type="Unity.Android.Gradle.PropertyString" base-type="BaseProperty" is-default="true" dependencies="" />
      <element name="versionName" element-id="133" assign-type="None" property-value="0.1.0" type="Unity.Android.Gradle.PropertyString" base-type="BaseProperty" is-default="true" dependencies="" />
      <element name="minSdk" element-id="135" assign-type="None" property-value="23" type="Unity.Android.Gradle.PropertyUnsignedInteger" base-type="BaseProperty" is-default="true" dependencies="" />
      <element name="targetSdk" element-id="136" assign-type="None" property-value="36" type="Unity.Android.Gradle.PropertyUnsignedInteger" base-type="BaseProperty" is-default="true" dependencies="" />
      <element name="versionCode" element-id="137" assign-type="None" property-value="1" type="Unity.Android.Gradle.PropertyUnsignedInteger" base-type="BaseProperty" is-default="true" dependencies="" />
      <element name="ndk" element-id="140" assign-type="None" type="Unity.Android.Gradle.Ndk" base-type="Unity.Android.Gradle.BaseBlock" is-default="true" dependencies="">
        <element name="abiFilters" element-id="141" assign-type="None" property-value="arm64-v8a" type="Unity.Android.Gradle.PropertyStringArray" base-type="BaseProperty" is-default="true" dependencies="" />
        <element name="debugSymbolLevel" element-id="142" assign-type="Quotes" property-value="None" type="Unity.Android.Gradle.PropertyEnum`1" base-type="BaseProperty" is-default="true" dependencies="" />
      </element>
    </element>
    <element name="lint" element-id="143" assign-type="None" type="Unity.Android.Gradle.Lint" base-type="Unity.Android.Gradle.BaseBlock" is-default="true" dependencies="">
      <element name="abortOnError" element-id="145" assign-type="None" property-value="False" type="Unity.Android.Gradle.PropertyBoolean" base-type="BaseProperty" is-default="true" dependencies="" />
    </element>
    <element name="androidResources" element-id="173" assign-type="None" type="Unity.Android.Gradle.AndroidResources" base-type="Unity.Android.Gradle.BaseBlock" is-default="true" dependencies="">
      <element name="ignoreAssetsPattern" element-id="176" assign-type="Equals" property-value="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~" type="Unity.Android.Gradle.PropertyString" base-type="BaseProperty" is-default="true" dependencies="" />
      <element name="noCompress" element-id="178" assign-type="Equals" raw-value="['.unity3d', '.ress', '.resource', '.obb', '.bundle', '.unityexp'] + unityStreamingAssets.tokenize(', ')" type="Unity.Android.Gradle.PropertyStringArray" base-type="BaseProperty" is-default="true" dependencies="" />
    </element>
    <element name="packaging" element-id="181" assign-type="None" type="Unity.Android.Gradle.Packaging" base-type="Unity.Android.Gradle.BaseBlock" is-default="true" dependencies="">
      <element name="jniLibs" element-id="182" assign-type="None" type="Unity.Android.Gradle.JniLibs" base-type="Unity.Android.Gradle.BaseBlock" is-default="true" dependencies="">
        <element name="useLegacyPackaging" element-id="183" assign-type="None" property-value="True" type="Unity.Android.Gradle.PropertyBoolean" base-type="BaseProperty" is-default="true" dependencies="" />
      </element>
    </element>
    <element name="buildTypes" element-id="185" assign-type="None" type="Unity.Android.Gradle.BuildTypes" base-type="Unity.Android.Gradle.BaseBlock" is-default="true" dependencies="">
      <element name="debug" element-id="202" assign-type="None" type="Unity.Android.Gradle.BuildType" base-type="Unity.Android.Gradle.BaseBlock" is-default="false" dependencies="">
        <element name="minifyEnabled" element-id="203" assign-type="Equals" property-value="False" type="Unity.Android.Gradle.PropertyBoolean" base-type="BaseProperty" is-default="true" dependencies="" />
        <element name="proguardFiles" element-id="204" assign-type="None" raw-value="getDefaultProguardFile('proguard-android.txt')" type="Unity.Android.Gradle.PropertyStringArray" base-type="BaseProperty" is-default="true" dependencies="" />
        <element name="jniDebuggable" element-id="205" assign-type="Equals" property-value="True" type="Unity.Android.Gradle.PropertyBoolean" base-type="BaseProperty" is-default="true" dependencies="" />
        <element name="signingConfig" element-id="207" assign-type="None" property-value="signingConfigs.debug" type="Unity.Android.Gradle.Property`1" base-type="BaseProperty" is-default="true" dependencies="" />
      </element>
      <element name="release" element-id="208" assign-type="None" type="Unity.Android.Gradle.BuildType" base-type="Unity.Android.Gradle.BaseBlock" is-default="false" dependencies="">
        <element name="minifyEnabled" element-id="209" assign-type="Equals" property-value="False" type="Unity.Android.Gradle.PropertyBoolean" base-type="BaseProperty" is-default="true" dependencies="" />
        <element name="proguardFiles" element-id="210" assign-type="None" raw-value="getDefaultProguardFile('proguard-android.txt')" type="Unity.Android.Gradle.PropertyStringArray" base-type="BaseProperty" is-default="true" dependencies="" />
        <element name="signingConfig" element-id="213" assign-type="None" property-value="signingConfigs.debug" type="Unity.Android.Gradle.Property`1" base-type="BaseProperty" is-default="true" dependencies="" />
      </element>
    </element>
    <element name="bundle" element-id="214" assign-type="None" type="Unity.Android.Gradle.Bundle" base-type="Unity.Android.Gradle.BaseBlock" is-default="false" dependencies="">
      <element name="language" element-id="215" assign-type="None" type="Unity.Android.Gradle.BundleElement" base-type="Unity.Android.Gradle.BaseBlock" is-default="true" dependencies="">
        <element name="enableSplit" element-id="216" assign-type="Equals" property-value="False" type="Unity.Android.Gradle.PropertyBoolean" base-type="BaseProperty" is-default="true" dependencies="" />
      </element>
      <element name="density" element-id="217" assign-type="None" type="Unity.Android.Gradle.BundleElement" base-type="Unity.Android.Gradle.BaseBlock" is-default="true" dependencies="">
        <element name="enableSplit" element-id="218" assign-type="Equals" property-value="False" type="Unity.Android.Gradle.PropertyBoolean" base-type="BaseProperty" is-default="true" dependencies="" />
      </element>
      <element name="abi" element-id="219" assign-type="None" type="Unity.Android.Gradle.BundleElement" base-type="Unity.Android.Gradle.BaseBlock" is-default="true" dependencies="">
        <element name="enableSplit" element-id="220" assign-type="Equals" property-value="True" type="Unity.Android.Gradle.PropertyBoolean" base-type="BaseProperty" is-default="true" dependencies="" />
      </element>
      <element name="texture" element-id="221" assign-type="None" type="Unity.Android.Gradle.BundleElement" base-type="Unity.Android.Gradle.BaseBlock" is-default="true" dependencies="">
        <element name="enableSplit" element-id="222" assign-type="Equals" property-value="True" type="Unity.Android.Gradle.PropertyBoolean" base-type="BaseProperty" is-default="true" dependencies="" />
      </element>
    </element>
  </element>
</root>