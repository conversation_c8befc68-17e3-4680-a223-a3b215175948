{ "pid": 44007, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "<PERSON><PERSON>ink<PERSON>" } },
{ "pid": 44007, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 44007, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen2" } },
{ "pid": 44007, "tid": 12884901888, "ts": 1758795362356158, "dur": 7599, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Blocking GC"} },
{ "pid": 44007, "tid": 12884901888, "ts": 1758795363056475, "dur": 47788, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 44007, "tid": 12884901888, "ts": 1758795364426027, "dur": 31495, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 44007, "tid": 1, "ts": 1758795365257400, "dur": 1357, "ph": "X", "name": "GC - Gen2", "args": {} },
{ "pid": 44007, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen1" } },
{ "pid": 44007, "tid": 1, "ts": 1758795365258758, "dur": 4, "ph": "X", "name": "GC - Gen1", "args": {} },
{ "pid": 44007, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen0" } },
{ "pid": 44007, "tid": 4294967296, "ts": 1758795362600184, "dur": 33524, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 44007, "tid": 4294967296, "ts": 1758795363709114, "dur": 29741, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 44007, "tid": 4294967296, "ts": 1758795365137128, "dur": 37333, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 44007, "tid": 1, "ts": 1758795365258763, "dur": 1, "ph": "X", "name": "GC - Gen0", "args": {} },
{ "pid": 44007, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 44007, "tid": 1, "ts": 1758795361989686, "dur": 3261497, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 44007, "tid": 1, "ts": 1758795361991184, "dur": 42453, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795361995484, "dur": 18151, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362033639, "dur": 5372, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362052468, "dur": 35482, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362089263, "dur": 94042, "ph": "X", "name": "SetupAndRegisterUnityEngineSteps", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362183341, "dur": 36700, "ph": "X", "name": "ResolveUnityEngine", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362220067, "dur": 29707, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362249786, "dur": 109765, "ph": "X", "name": "InitializeEngineStrippingStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362359559, "dur": 2986, "ph": "X", "name": "ResolveForEngineModuleStrippingEnabledStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362362549, "dur": 3671, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362366223, "dur": 797, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362367022, "dur": 1624, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362368647, "dur": 276, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362368940, "dur": 7026, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362375970, "dur": 490, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362376463, "dur": 118, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362376581, "dur": 61, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362376643, "dur": 169, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362376812, "dur": 113, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362376926, "dur": 55, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362376981, "dur": 616, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362377598, "dur": 441, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362378053, "dur": 4038, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362382098, "dur": 4464, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362386564, "dur": 3490, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362390056, "dur": 4251, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362394312, "dur": 2597, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362396914, "dur": 1037, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362397953, "dur": 959, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362398923, "dur": 1396, "ph": "X", "name": "UnityLoadReferencesStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362400324, "dur": 1096, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps2", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362401425, "dur": 597, "ph": "X", "name": "SetupI18N", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362402024, "dur": 3699, "ph": "X", "name": "ResolveFromDescriptorsStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362405725, "dur": 136, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362405862, "dur": 1608, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362407471, "dur": 75, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362407547, "dur": 1083, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362408631, "dur": 373, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362409010, "dur": 22105, "ph": "X", "name": "UnityBlacklistStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362431119, "dur": 3699, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362434828, "dur": 99155, "ph": "X", "name": "DynamicDependencyLookupStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362534001, "dur": 460, "ph": "X", "name": "EarlyReportGenerationStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362534465, "dur": 481, "ph": "X", "name": "ResolveTestsStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362534949, "dur": 178, "ph": "X", "name": "LoadI18nAssemblies", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362535131, "dur": 598, "ph": "X", "name": "ResolveMonoBehaviourItselfStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362535738, "dur": 556, "ph": "X", "name": "ResolveFromAllUserMonoBehaviours", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362536296, "dur": 3130, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362539427, "dur": 17, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362539447, "dur": 96096, "ph": "X", "name": "ResolveFromPreserveAttribute", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362635566, "dur": 10366, "ph": "X", "name": "EngineStrippingAnnotationStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362645952, "dur": 124696, "ph": "X", "name": "UnityTypeMapStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362770670, "dur": 253, "ph": "X", "name": "BeforeMarkReportGenerationStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362770927, "dur": 74325, "ph": "X", "name": "BeforeMarkAnalyticsStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362845272, "dur": 16936, "ph": "X", "name": "RemoveSecurityStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362862226, "dur": 5437, "ph": "X", "name": "RemoveSecurityFromCopyAssemblies", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362867675, "dur": 12395, "ph": "X", "name": "RemoveFeaturesStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362880087, "dur": 12148, "ph": "X", "name": "RemoveUnreachableBlocksStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795362892254, "dur": 1727874, "ph": "X", "name": "UnityMarkStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795364620145, "dur": 509, "ph": "X", "name": "ValidateVirtualMethodAnnotationsStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795364620656, "dur": 3189, "ph": "X", "name": "ProcessWarningsStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795364623851, "dur": 54340, "ph": "X", "name": "UnitySweepStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795364678211, "dur": 2892, "ph": "X", "name": "UnityCodeRewriterStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795364681117, "dur": 2756, "ph": "X", "name": "CleanStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795364683880, "dur": 1826, "ph": "X", "name": "StubifyStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795364685709, "dur": 2126, "ph": "X", "name": "AddUnresolvedStubsStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795364687838, "dur": 2266, "ph": "X", "name": "BeforeOutputAnalyticsStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795364690107, "dur": 107, "ph": "X", "name": "BeforeOutputReportGenerationStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795364690217, "dur": 319, "ph": "X", "name": "ProcessCopiedAssembliesRequiringSecurityAttributesRemoved", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795364690542, "dur": 301, "ph": "X", "name": "SealerStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795364690845, "dur": 531392, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795365222248, "dur": 14870, "ph": "X", "name": "LinkerToEditorDataGenerationStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795365237123, "dur": 127, "ph": "X", "name": "ReportGenerationStep", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795365238851, "dur": 12249, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795365251184, "dur": 877, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795365258765, "dur": 49, "ph": "X", "name": "", "args": {} },
{ "pid": 44007, "tid": 1, "ts": 1758795365257039, "dur": 1931, "ph": "X", "name": "Write chrome-trace events", "args": {} },
