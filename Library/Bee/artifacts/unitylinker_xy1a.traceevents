{ "pid": 41191, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "<PERSON><PERSON>ink<PERSON>" } },
{ "pid": 41191, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 41191, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen2" } },
{ "pid": 41191, "tid": 12884901888, "ts": 1758794509421763, "dur": 8171, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Blocking GC"} },
{ "pid": 41191, "tid": 12884901888, "ts": 1758794510170558, "dur": 50684, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 41191, "tid": 12884901888, "ts": 1758794511662798, "dur": 30289, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 41191, "tid": 1, "ts": 1758794512616631, "dur": 1724, "ph": "X", "name": "GC - Gen2", "args": {} },
{ "pid": 41191, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen1" } },
{ "pid": 41191, "tid": 1, "ts": 1758794512618357, "dur": 5, "ph": "X", "name": "GC - Gen1", "args": {} },
{ "pid": 41191, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen0" } },
{ "pid": 41191, "tid": 4294967296, "ts": 1758794509693605, "dur": 32119, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 41191, "tid": 4294967296, "ts": 1758794510861199, "dur": 26892, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 41191, "tid": 4294967296, "ts": 1758794512486968, "dur": 42157, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 41191, "tid": 1, "ts": 1758794512618363, "dur": 1, "ph": "X", "name": "GC - Gen0", "args": {} },
{ "pid": 41191, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 41191, "tid": 1, "ts": 1758794509012480, "dur": 3597157, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 41191, "tid": 1, "ts": 1758794509013658, "dur": 47123, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509018257, "dur": 19877, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509060782, "dur": 5939, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509081149, "dur": 37341, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509119967, "dur": 100811, "ph": "X", "name": "SetupAndRegisterUnityEngineSteps", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509220808, "dur": 49112, "ph": "X", "name": "ResolveUnityEngine", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509269942, "dur": 35892, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509305849, "dur": 118753, "ph": "X", "name": "InitializeEngineStrippingStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509424611, "dur": 2905, "ph": "X", "name": "ResolveForEngineModuleStrippingEnabledStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509427519, "dur": 3733, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509431255, "dur": 811, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509432067, "dur": 1634, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509433702, "dur": 275, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509433984, "dur": 6711, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509440699, "dur": 457, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509441156, "dur": 111, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509441268, "dur": 63, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509441332, "dur": 754, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509442087, "dur": 120, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509442208, "dur": 53, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509442262, "dur": 613, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509442875, "dur": 455, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509443337, "dur": 3554, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509446898, "dur": 4318, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509451218, "dur": 3966, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509455189, "dur": 5030, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509460224, "dur": 2536, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509462763, "dur": 1614, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509464381, "dur": 1340, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509465732, "dur": 1563, "ph": "X", "name": "UnityLoadReferencesStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509467301, "dur": 1219, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps2", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509468524, "dur": 601, "ph": "X", "name": "SetupI18N", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509469128, "dur": 3858, "ph": "X", "name": "ResolveFromDescriptorsStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509472987, "dur": 150, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509473138, "dur": 1731, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509474870, "dur": 87, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509474958, "dur": 1117, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509476076, "dur": 435, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509476515, "dur": 20969, "ph": "X", "name": "UnityBlacklistStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509497487, "dur": 3608, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509501102, "dur": 118188, "ph": "X", "name": "DynamicDependencyLookupStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509619336, "dur": 389, "ph": "X", "name": "EarlyReportGenerationStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509619730, "dur": 615, "ph": "X", "name": "ResolveTestsStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509620363, "dur": 316, "ph": "X", "name": "LoadI18nAssemblies", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509620684, "dur": 640, "ph": "X", "name": "ResolveMonoBehaviourItselfStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509621327, "dur": 590, "ph": "X", "name": "ResolveFromAllUserMonoBehaviours", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509621920, "dur": 3495, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509625420, "dur": 15, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509625440, "dur": 103708, "ph": "X", "name": "ResolveFromPreserveAttribute", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509729173, "dur": 10420, "ph": "X", "name": "EngineStrippingAnnotationStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509739609, "dur": 131822, "ph": "X", "name": "UnityTypeMapStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509871454, "dur": 304, "ph": "X", "name": "BeforeMarkReportGenerationStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509871761, "dur": 74540, "ph": "X", "name": "BeforeMarkAnalyticsStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509946319, "dur": 17556, "ph": "X", "name": "RemoveSecurityStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509963894, "dur": 5611, "ph": "X", "name": "RemoveSecurityFromCopyAssemblies", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509969516, "dur": 13292, "ph": "X", "name": "RemoveFeaturesStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509982829, "dur": 13016, "ph": "X", "name": "RemoveUnreachableBlocksStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794509995870, "dur": 1905438, "ph": "X", "name": "UnityMarkStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794511901324, "dur": 508, "ph": "X", "name": "ValidateVirtualMethodAnnotationsStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794511901835, "dur": 3686, "ph": "X", "name": "ProcessWarningsStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794511905533, "dur": 64006, "ph": "X", "name": "UnitySweepStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794511969556, "dur": 3022, "ph": "X", "name": "UnityCodeRewriterStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794511972588, "dur": 3010, "ph": "X", "name": "CleanStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794511975607, "dur": 1731, "ph": "X", "name": "StubifyStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794511977342, "dur": 2116, "ph": "X", "name": "AddUnresolvedStubsStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794511979461, "dur": 2420, "ph": "X", "name": "BeforeOutputAnalyticsStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794511981887, "dur": 123, "ph": "X", "name": "BeforeOutputReportGenerationStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794511982014, "dur": 355, "ph": "X", "name": "ProcessCopiedAssembliesRequiringSecurityAttributesRemoved", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794511982372, "dur": 318, "ph": "X", "name": "SealerStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794511982692, "dur": 596744, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794512579453, "dur": 15545, "ph": "X", "name": "LinkerToEditorDataGenerationStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794512595002, "dur": 124, "ph": "X", "name": "ReportGenerationStep", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794512596743, "dur": 12804, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794512609638, "dur": 930, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794512618365, "dur": 51, "ph": "X", "name": "", "args": {} },
{ "pid": 41191, "tid": 1, "ts": 1758794512616207, "dur": 2372, "ph": "X", "name": "Write chrome-trace events", "args": {} },
