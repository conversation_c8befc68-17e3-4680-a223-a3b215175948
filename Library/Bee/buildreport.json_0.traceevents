{ "pid": 37977, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 37977, "tid": 1, "ts": 1758794730720375, "dur": 4053, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 37977, "tid": 1, "ts": 1758794730724433, "dur": 36972, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 37977, "tid": 1, "ts": 1758794730761413, "dur": 1432, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 37977, "tid": 593, "ts": 1758794730771858, "dur": 15, "ph": "X", "name": "", "args": {} },
{ "pid": 37977, "tid": 154618822656, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 37977, "tid": 154618822656, "ts": 1758794730720280, "dur": 8910, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 37977, "tid": 154618822656, "ts": 1758794730729192, "dur": 42178, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 37977, "tid": 154618822656, "ts": 1758794730729221, "dur": 86, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 37977, "tid": 154618822656, "ts": 1758794730729309, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 37977, "tid": 154618822656, "ts": 1758794730729313, "dur": 893, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 37977, "tid": 154618822656, "ts": 1758794730730211, "dur": 47, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 37977, "tid": 154618822656, "ts": 1758794730730259, "dur": 3907, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 37977, "tid": 154618822656, "ts": 1758794730734171, "dur": 3, "ph": "X", "name": "ProcessMessages 483", "args": {} },
{ "pid": 37977, "tid": 154618822656, "ts": 1758794730734175, "dur": 4132, "ph": "X", "name": "ReadAsync 483", "args": {} },
{ "pid": 37977, "tid": 154618822656, "ts": 1758794730738311, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 37977, "tid": 154618822656, "ts": 1758794730738314, "dur": 25774, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 37977, "tid": 154618822656, "ts": 1758794730764092, "dur": 1, "ph": "X", "name": "ProcessMessages 25", "args": {} },
{ "pid": 37977, "tid": 154618822656, "ts": 1758794730764094, "dur": 7264, "ph": "X", "name": "ReadAsync 25", "args": {} },
{ "pid": 37977, "tid": 593, "ts": 1758794730771876, "dur": 18, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 37977, "tid": 150323855360, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 37977, "tid": 150323855360, "ts": 1758794730719862, "dur": 42998, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 37977, "tid": 150323855360, "ts": 1758794730762863, "dur": 91, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 37977, "tid": 593, "ts": 1758794730771895, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 37977, "tid": 146028888064, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 37977, "tid": 146028888064, "ts": 1758794730690269, "dur": 81300, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 37977, "tid": 146028888064, "ts": 1758794730690684, "dur": 28127, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 37977, "tid": 146028888064, "ts": 1758794730771574, "dur": 29, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 37977, "tid": 593, "ts": 1758794730771901, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1758794730729903, "dur":2906, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794730732816, "dur":1648, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794730734474, "dur":128, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794730734651, "dur":30172, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794730764941, "dur":4242, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1758794730734514, "dur":144, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758794730734662, "dur":4432, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1758794730739116, "dur":25704, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794730734522, "dur":145, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758794730734686, "dur":30071, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794730734529, "dur":147, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794730734676, "dur":29222, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758794730764155, "dur":566, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":3, "ts":1758794730763899, "dur":825, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794730734543, "dur":141, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758794730734684, "dur":30142, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794730734552, "dur":143, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758794730734695, "dur":30066, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794730734563, "dur":155, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758794730734718, "dur":30070, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794730734576, "dur":126, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758794730734703, "dur":30061, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794730734585, "dur":121, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758794730734707, "dur":30141, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794730734596, "dur":113, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758794730734709, "dur":30117, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794730734604, "dur":108, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758794730734713, "dur":30057, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758794730771469, "dur":334, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 37977, "tid": 593, "ts": 1758794730772147, "dur": 23, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 37977, "tid": 593, "ts": 1758794730772199, "dur": 96, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 37977, "tid": 593, "ts": 1758794730771856, "dur": 468, "ph": "X", "name": "Write chrome-trace events", "args": {} },
