{ "pid": 37977, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 37977, "tid": 1, "ts": 1758795203907242, "dur": 5927, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 37977, "tid": 1, "ts": 1758795203913172, "dur": 67157, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 37977, "tid": 1, "ts": 1758795203980336, "dur": 2730, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 37977, "tid": 625, "ts": 1758795204669121, "dur": 1058, "ph": "X", "name": "", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795203905984, "dur": 452, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795203906437, "dur": 753008, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795203907166, "dur": 3139, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795203910309, "dur": 1203, "ph": "X", "name": "ProcessMessages 551", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795203911515, "dur": 57, "ph": "X", "name": "ReadAsync 551", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795203911574, "dur": 229, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795203911805, "dur": 740900, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795204652717, "dur": 2030, "ph": "X", "name": "ProcessMessages 6958", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795204654749, "dur": 4208, "ph": "X", "name": "ReadAsync 6958", "args": {} },
{ "pid": 37977, "tid": 625, "ts": 1758795204670183, "dur": 16, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 37977, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 37977, "tid": 8589934592, "ts": 1758795203903580, "dur": 79557, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 37977, "tid": 8589934592, "ts": 1758795203983139, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 37977, "tid": 8589934592, "ts": 1758795203983145, "dur": 1556, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 37977, "tid": 625, "ts": 1758795204670201, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 37977, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 37977, "tid": 4294967296, "ts": 1758795203868404, "dur": 792039, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 37977, "tid": 4294967296, "ts": 1758795203872764, "dur": 27158, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 37977, "tid": 4294967296, "ts": 1758795204660591, "dur": 4713, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 37977, "tid": 4294967296, "ts": 1758795204662390, "dur": 1984, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 37977, "tid": 4294967296, "ts": 1758795204665368, "dur": 10, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 37977, "tid": 625, "ts": 1758795204670207, "dur": 13, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1758795203902803, "dur":2450, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795203905259, "dur":1404, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795203906700, "dur":89, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795203906829, "dur":745647, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795204652504, "dur":55, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795204652641, "dur":3868, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1758795203906723, "dur":110, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795203907033, "dur":133, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/ScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1758795203906835, "dur":4123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1758795203911438, "dur":740781, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1758795203906730, "dur":107, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795203906843, "dur":77979, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795203985117, "dur":325, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":2, "ts":1758795203984823, "dur":624, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795203985447, "dur":667091, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795203906737, "dur":156, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795203906893, "dur":745550, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795203906744, "dur":108, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795203906852, "dur":78597, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795203985449, "dur":667040, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795203906754, "dur":105, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795203906859, "dur":745695, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795203906762, "dur":103, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795203906865, "dur":745600, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795203906769, "dur":102, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795203906872, "dur":745642, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795203906776, "dur":102, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795203906879, "dur":745649, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795203906783, "dur":102, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795203906885, "dur":745552, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795203906790, "dur":100, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795203906890, "dur":745658, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795204658576, "dur":237, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 37977, "tid": 625, "ts": 1758795204671169, "dur": 1872, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 37977, "tid": 625, "ts": 1758795204673088, "dur": 991, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 37977, "tid": 625, "ts": 1758795204668559, "dur": 6049, "ph": "X", "name": "Write chrome-trace events", "args": {} },
