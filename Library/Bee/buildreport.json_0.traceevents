{ "pid": 37977, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 37977, "tid": 1, "ts": 1758795357975201, "dur": 5668, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 37977, "tid": 1, "ts": 1758795357980871, "dur": 38367, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 37977, "tid": 1, "ts": 1758795358019248, "dur": 2223, "ph": "X", "name": "Write<PERSON>son", "args": {} },
{ "pid": 37977, "tid": 642, "ts": 1758795358730647, "dur": 962, "ph": "X", "name": "", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795357973622, "dur": 4602, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795357978227, "dur": 741819, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795357978912, "dur": 3302, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795357982219, "dur": 1672, "ph": "X", "name": "ProcessMessages 571", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795357983894, "dur": 726890, "ph": "X", "name": "ReadAsync 571", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795358710798, "dur": 2056, "ph": "X", "name": "ProcessMessages 6958", "args": {} },
{ "pid": 37977, "tid": 12884901888, "ts": 1758795358712858, "dur": 6445, "ph": "X", "name": "ReadAsync 6958", "args": {} },
{ "pid": 37977, "tid": 642, "ts": 1758795358731613, "dur": 17, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 37977, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 37977, "tid": 8589934592, "ts": 1758795357971605, "dur": 49960, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 37977, "tid": 8589934592, "ts": 1758795358021567, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 37977, "tid": 8589934592, "ts": 1758795358021574, "dur": 1595, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 37977, "tid": 642, "ts": 1758795358731632, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 37977, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 37977, "tid": 4294967296, "ts": 1758795357938428, "dur": 783490, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 37977, "tid": 4294967296, "ts": 1758795357941496, "dur": 26287, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 37977, "tid": 4294967296, "ts": 1758795358722069, "dur": 4190, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 37977, "tid": 4294967296, "ts": 1758795358723823, "dur": 1529, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 37977, "tid": 4294967296, "ts": 1758795358726319, "dur": 8, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 37977, "tid": 642, "ts": 1758795358731642, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1758795357974076, "dur":2853, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795357976938, "dur":1673, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795357978642, "dur":106, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795357978789, "dur":731867, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795358710788, "dur":4783, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1758795357978678, "dur":114, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1758795357978969, "dur":141, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library/ScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1758795357978795, "dur":2140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1758795357981191, "dur":729131, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1758795357978682, "dur":114, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795357978806, "dur":44339, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795358023389, "dur":153, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"/Applications/Unity/Hub/Editor/6000.2.2f1/Unity.app/Contents/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":2, "ts":1758795358023146, "dur":398, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1758795358023544, "dur":687097, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795357978690, "dur":118, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795357978808, "dur":44738, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1758795358023547, "dur":687056, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795357978697, "dur":117, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1758795357978814, "dur":731767, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795357978712, "dur":121, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1758795357978833, "dur":731851, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795357978719, "dur":103, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1758795357978822, "dur":731802, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795357978728, "dur":97, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1758795357978825, "dur":731875, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795357978735, "dur":92, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1758795357978827, "dur":731768, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795357978742, "dur":87, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1758795357978829, "dur":731840, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795357978753, "dur":78, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1758795357978831, "dur":731777, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1758795358717762, "dur":116, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 37977, "tid": 642, "ts": 1758795358731972, "dur": 1499, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 37977, "tid": 642, "ts": 1758795358733530, "dur": 1013, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 37977, "tid": 642, "ts": 1758795358730027, "dur": 5050, "ph": "X", "name": "Write chrome-trace events", "args": {} },
